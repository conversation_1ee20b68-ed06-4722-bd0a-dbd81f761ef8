{"channel": "stable-24_05", "channel_nix_path": "/nix/store/r9dxa39mzcwfm6qp398j3wkms3vwiqcd-nixpkgs-stable-24_05-24.05.tar.gz/nixpkgs-stable-24_05", "env": {"CFLAGS": "", "GI_TYPELIB_PATH": "", "LDFLAGS": "", "NIX_CFLAGS_COMPILE": "", "NIX_LDFLAGS": "", "PATH": "/nix/store/hp7fmkb1rzmfxisj83c8f8dqz146nm6q-supabase-cli-1.168.1/bin", "PKG_CONFIG_PATH": "", "PKG_CONFIG_PATH_FOR_TARGET": "", "REPLIT_LD_LIBRARY_PATH": "", "XDG_DATA_DIRS": "/nix/store/hp7fmkb1rzmfxisj83c8f8dqz146nm6q-supabase-cli-1.168.1/share"}, "packages": ["supabase-cli"]}