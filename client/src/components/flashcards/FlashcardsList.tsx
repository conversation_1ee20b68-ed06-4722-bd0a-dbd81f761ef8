import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Edit, Trash2, RotateCcw, Check, X } from "lucide-react";
// Removed Supabase import - using backend API endpoints
import { useAuth } from "@/hooks/useAuth";
import { Tables } from "@/types/supabase";
import { useToast } from "@/hooks/use-toast";

type Flashcard = Tables<"flashcards">;

interface FlashcardsListProps {
  flashcards: Flashcard[];
  loading: boolean;
  onRefreshFlashcards: () => void;
  onEditFlashcard: (flashcard: Flashcard) => void;
  selectedDeckId: string;
}

const FlashcardsList: React.FC<FlashcardsListProps> = ({
  flashcards,
  loading,
  onRefreshFlashcards,
  onEditFlashcard,
  selectedDeckId,
}) => {
  const { user } = useAuth();
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const { toast } = useToast();

  // Inline editing state
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingValues, setEditingValues] = useState<{
    front_text: string;
    back_text: string;
  }>({ front_text: "", back_text: "" });
  const [savingId, setSavingId] = useState<string | null>(null);

  const handleDelete = async (flashcard: Flashcard) => {
    if (!confirm(`Are you sure you want to delete this flashcard?`)) {
      return;
    }

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to delete flashcards.",
        variant: "destructive",
      });
      return;
    }

    setDeletingId(flashcard.id);
    try {
      // Delete flashcard via backend API
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/flashcards/${flashcard.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete flashcard');
      }

      onRefreshFlashcards();
      toast({
        title: "Success",
        description: "Flashcard deleted successfully.",
      });
    } catch (error: any) {
      console.error("Error deleting flashcard:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete flashcard.",
        variant: "destructive",
      });
    } finally {
      setDeletingId(null);
    }
  };

  // Inline editing functions
  const startInlineEdit = (flashcard: Flashcard) => {
    setEditingId(flashcard.id);
    setEditingValues({
      front_text: flashcard.front_text,
      back_text: flashcard.back_text,
    });
  };

  const cancelInlineEdit = () => {
    setEditingId(null);
    setEditingValues({ front_text: "", back_text: "" });
  };

  const saveInlineEdit = async (flashcard: Flashcard) => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to edit flashcards.",
        variant: "destructive",
      });
      return;
    }

    setSavingId(flashcard.id);
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/flashcards/${flashcard.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          front_text: editingValues.front_text.trim(),
          back_text: editingValues.back_text.trim(),
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update flashcard');
      }

      setEditingId(null);
      setEditingValues({ front_text: "", back_text: "" });
      onRefreshFlashcards();
      toast({
        title: "Success",
        description: "Flashcard updated successfully.",
      });
    } catch (error: any) {
      console.error("Error updating flashcard:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to update flashcard.",
        variant: "destructive",
      });
    } finally {
      setSavingId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getSRSStatus = (flashcard: Flashcard) => {
    // Since SRS fields don't exist in current flashcard schema, treat all as "New"
    // This can be updated when SRS functionality is properly implemented for flashcards
    return { label: "New", color: "bg-blue-500" };
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-24 bg-slate-700 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  if (flashcards.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-slate-400 mb-4">
          <RotateCcw className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>No flashcards found in this deck.</p>
          <p className="text-sm">Add some flashcards to get started!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <p className="text-sm text-slate-400">
          {flashcards.length} flashcard{flashcards.length !== 1 ? "s" : ""}
        </p>
        <Button
          variant="outline"
          size="sm"
          onClick={onRefreshFlashcards}
          className="border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="space-y-3">
        {flashcards.map((flashcard, index) => {
          const srsStatus = getSRSStatus(flashcard);
          
          return (
            <Card
              key={flashcard.id}
              className="bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 transition-colors"
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-slate-400 font-mono text-sm bg-slate-800 px-2 py-1 rounded">
                        #{index + 1}
                      </span>
                      <span
                        className={`${srsStatus.color} text-white text-xs px-2 py-1 rounded-full`}
                      >
                        {srsStatus.label}
                      </span>
                      <span className="text-xs text-slate-500">
                        Created: {flashcard.created_at ? formatDate(flashcard.created_at) : 'Unknown'}
                      </span>
                    </div>

                    <div className="space-y-2">
                      <div>
                        <p className="text-xs text-slate-400 mb-1">Question:</p>
                        {editingId === flashcard.id ? (
                          <Textarea
                            value={editingValues.front_text}
                            onChange={(e) => setEditingValues(prev => ({ ...prev, front_text: e.target.value }))}
                            className="bg-slate-800 border-slate-600 text-slate-200 text-sm min-h-[60px] resize-none"
                            placeholder="Enter question..."
                          />
                        ) : (
                          <p className="text-slate-200 text-sm line-clamp-2">
                            {flashcard.front_text}
                          </p>
                        )}
                      </div>

                      <div>
                        <p className="text-xs text-slate-400 mb-1">Answer:</p>
                        {editingId === flashcard.id ? (
                          <Textarea
                            value={editingValues.back_text}
                            onChange={(e) => setEditingValues(prev => ({ ...prev, back_text: e.target.value }))}
                            className="bg-slate-800 border-slate-600 text-slate-300 text-sm min-h-[60px] resize-none"
                            placeholder="Enter answer..."
                          />
                        ) : (
                          <p className="text-slate-300 text-sm line-clamp-2">
                            {flashcard.back_text}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* SRS statistics not available in current schema */}
                    <div className="mt-2 text-xs text-slate-500">
                      SRS tracking not yet implemented for flashcards
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    {editingId === flashcard.id ? (
                      // Save and Cancel buttons when editing
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => saveInlineEdit(flashcard)}
                          disabled={savingId === flashcard.id || !editingValues.front_text.trim() || !editingValues.back_text.trim()}
                          className="border-green-600 text-green-400 hover:bg-green-900/20"
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={cancelInlineEdit}
                          disabled={savingId === flashcard.id}
                          className="border-slate-600 text-slate-300 hover:bg-slate-600"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </>
                    ) : (
                      // Edit and Delete buttons when not editing
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => startInlineEdit(flashcard)}
                          disabled={editingId !== null || deletingId === flashcard.id}
                          className="border-slate-600 text-slate-300 hover:bg-slate-600"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(flashcard)}
                          disabled={deletingId === flashcard.id || editingId !== null}
                          className="border-red-600 text-red-400 hover:bg-red-900/20"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default FlashcardsList;
