2025-06-03 03:17:53.620 [info] [main] Log level: Info
2025-06-03 03:17:53.620 [info] [main] Validating found git in: "git"
2025-06-03 03:17:53.620 [info] [main] Using git "2.47.2" from "git"
2025-06-03 03:17:53.620 [info] [Model][doInitialScan] Initial repository scan started
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [3ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --git-dir --git-common-dir [150ms]
2025-06-03 03:17:53.620 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-03 03:17:53.620 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-03 03:17:53.620 [info] > git config --get commit.template [4ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:17:53.620 [info] > git status -z -uall [10ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [465ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [54ms]
2025-06-03 03:17:53.620 [info] > git config --get commit.template [13ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [1ms]
2025-06-03 03:17:53.620 [info] > git config --get --local branch.main.vscode-merge-base [7ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [11ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [11ms]
2025-06-03 03:17:53.620 [info] > git merge-base refs/heads/main refs/remotes/origin/main [7ms]
2025-06-03 03:17:53.620 [info] > git status -z -uall [6ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [16ms]
2025-06-03 03:17:53.620 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [30ms]
2025-06-03 03:17:53.620 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [51ms]
2025-06-03 03:17:53.620 [info] > git merge-base refs/heads/main refs/remotes/origin/main [2ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [21ms]
2025-06-03 03:17:53.620 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [8ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [1ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [3ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [362ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [5ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [258ms]
2025-06-03 03:17:53.620 [info] > git fetch [581ms]
2025-06-03 03:17:53.620 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-03 03:17:53.620 [info] > git config --get commit.template [14ms]
2025-06-03 03:17:53.620 [info] > git rev-parse --show-toplevel [21ms]
2025-06-03 03:17:53.620 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-03 03:17:53.794 [info] > git config --get commit.template [163ms]
2025-06-03 03:17:53.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [205ms]
2025-06-03 03:17:53.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [52ms]
2025-06-03 03:17:54.347 [info] > git status -z -uall [21ms]
2025-06-03 03:17:54.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-03 03:17:54.393 [info] > git config --get commit.template [19ms]
2025-06-03 03:17:54.428 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:17:54.994 [info] > git status -z -uall [548ms]
2025-06-03 03:17:54.995 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [534ms]
2025-06-03 03:17:57.316 [info] > git config --get --local branch.main.github-pr-owner-number [212ms]
2025-06-03 03:17:57.316 [warning] [Git][config] git config failed: Failed to execute git
2025-06-03 03:20:05.019 [info] > git show --textconv :client/src/components/ui/toast.tsx [15ms]
2025-06-03 03:20:05.022 [info] > git ls-files --stage -- client/src/components/ui/toast.tsx [6ms]
2025-06-03 03:20:05.056 [info] > git cat-file -s a822477534192c4df5073e4015f7461e739d3344 [4ms]
2025-06-03 03:20:05.186 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-03 03:23:29.061 [info] > git config --get commit.template [13ms]
2025-06-03 03:23:29.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:23:29.086 [info] > git status -z -uall [11ms]
2025-06-03 03:23:29.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:23:34.128 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-03 03:23:34.130 [info] > git config --get commit.template [19ms]
2025-06-03 03:23:34.186 [info] > git status -z -uall [28ms]
2025-06-03 03:23:34.187 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:23:39.243 [info] > git config --get commit.template [22ms]
2025-06-03 03:23:39.244 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:23:39.269 [info] > git status -z -uall [11ms]
2025-06-03 03:23:39.269 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:23:44.290 [info] > git config --get commit.template [8ms]
2025-06-03 03:23:44.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:23:44.305 [info] > git status -z -uall [6ms]
2025-06-03 03:23:44.306 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:23:49.327 [info] > git config --get commit.template [7ms]
2025-06-03 03:23:49.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:23:49.345 [info] > git status -z -uall [8ms]
2025-06-03 03:23:49.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:23:54.368 [info] > git config --get commit.template [7ms]
2025-06-03 03:23:54.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:23:54.383 [info] > git status -z -uall [7ms]
2025-06-03 03:23:54.384 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:23:59.408 [info] > git config --get commit.template [9ms]
2025-06-03 03:23:59.409 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:23:59.423 [info] > git status -z -uall [7ms]
2025-06-03 03:23:59.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:24:04.445 [info] > git config --get commit.template [6ms]
2025-06-03 03:24:04.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:24:04.468 [info] > git status -z -uall [9ms]
2025-06-03 03:24:04.469 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:24:09.487 [info] > git config --get commit.template [2ms]
2025-06-03 03:24:09.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:24:09.514 [info] > git status -z -uall [7ms]
2025-06-03 03:24:09.516 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:24:14.536 [info] > git config --get commit.template [6ms]
2025-06-03 03:24:14.537 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:24:14.553 [info] > git status -z -uall [7ms]
2025-06-03 03:24:14.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:24:19.634 [info] > git config --get commit.template [63ms]
2025-06-03 03:24:19.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [55ms]
2025-06-03 03:24:19.657 [info] > git status -z -uall [10ms]
2025-06-03 03:24:19.658 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:24:24.680 [info] > git config --get commit.template [7ms]
2025-06-03 03:24:24.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:24:24.699 [info] > git status -z -uall [6ms]
2025-06-03 03:24:24.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:24:29.725 [info] > git config --get commit.template [10ms]
2025-06-03 03:24:29.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:24:29.745 [info] > git status -z -uall [9ms]
2025-06-03 03:24:29.746 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:24:34.768 [info] > git config --get commit.template [3ms]
2025-06-03 03:24:34.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:24:34.792 [info] > git status -z -uall [6ms]
2025-06-03 03:24:34.793 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:25:41.285 [info] > git config --get commit.template [6ms]
2025-06-03 03:25:41.285 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:25:41.297 [info] > git status -z -uall [6ms]
2025-06-03 03:25:41.298 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:25:46.320 [info] > git config --get commit.template [8ms]
2025-06-03 03:25:46.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:25:46.337 [info] > git status -z -uall [9ms]
2025-06-03 03:25:46.339 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:25:51.361 [info] > git config --get commit.template [2ms]
2025-06-03 03:25:51.374 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:25:51.395 [info] > git status -z -uall [12ms]
2025-06-03 03:25:51.396 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:25:56.417 [info] > git config --get commit.template [7ms]
2025-06-03 03:25:56.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:25:56.430 [info] > git status -z -uall [7ms]
2025-06-03 03:25:56.434 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:26:01.457 [info] > git config --get commit.template [7ms]
2025-06-03 03:26:01.458 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:26:01.471 [info] > git status -z -uall [6ms]
2025-06-03 03:26:01.472 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:26:06.496 [info] > git config --get commit.template [9ms]
2025-06-03 03:26:06.497 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:26:06.514 [info] > git status -z -uall [9ms]
2025-06-03 03:26:06.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:26:11.616 [info] > git config --get commit.template [83ms]
2025-06-03 03:26:11.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:26:11.652 [info] > git status -z -uall [18ms]
2025-06-03 03:26:11.653 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-03 03:26:16.676 [info] > git config --get commit.template [2ms]
2025-06-03 03:26:16.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:26:16.718 [info] > git status -z -uall [9ms]
2025-06-03 03:26:16.719 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:26:21.742 [info] > git config --get commit.template [9ms]
2025-06-03 03:26:21.743 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:26:21.762 [info] > git status -z -uall [9ms]
2025-06-03 03:26:21.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:27:00.549 [info] > git config --get commit.template [2ms]
2025-06-03 03:27:00.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:27:00.609 [info] > git status -z -uall [21ms]
2025-06-03 03:27:00.613 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:27:05.639 [info] > git config --get commit.template [10ms]
2025-06-03 03:27:05.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-03 03:27:05.668 [info] > git status -z -uall [14ms]
2025-06-03 03:27:05.669 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:27:10.882 [info] > git config --get commit.template [4ms]
2025-06-03 03:27:10.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-03 03:27:11.049 [info] > git status -z -uall [37ms]
2025-06-03 03:27:11.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-03 03:29:40.907 [info] > git config --get commit.template [12ms]
2025-06-03 03:29:40.908 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:29:40.939 [info] > git status -z -uall [22ms]
2025-06-03 03:29:40.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-03 03:29:45.980 [info] > git config --get commit.template [21ms]
2025-06-03 03:29:45.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:29:46.018 [info] > git status -z -uall [20ms]
2025-06-03 03:29:46.023 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-03 03:30:45.013 [info] > git config --get commit.template [9ms]
2025-06-03 03:30:45.014 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:30:45.031 [info] > git status -z -uall [7ms]
2025-06-03 03:30:45.033 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:30:50.073 [info] > git config --get commit.template [13ms]
2025-06-03 03:30:50.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:30:50.093 [info] > git status -z -uall [8ms]
2025-06-03 03:30:50.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:31:05.679 [info] > git config --get commit.template [8ms]
2025-06-03 03:31:05.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:31:05.694 [info] > git status -z -uall [7ms]
2025-06-03 03:31:05.695 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:31:21.469 [info] > git config --get commit.template [7ms]
2025-06-03 03:31:21.470 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:31:21.491 [info] > git status -z -uall [13ms]
2025-06-03 03:31:21.493 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:31:26.554 [info] > git config --get commit.template [35ms]
2025-06-03 03:31:26.556 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:31:26.622 [info] > git status -z -uall [39ms]
2025-06-03 03:31:26.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-03 03:31:37.697 [info] > git config --get commit.template [8ms]
2025-06-03 03:31:37.697 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:31:37.712 [info] > git status -z -uall [8ms]
2025-06-03 03:31:37.713 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:31:37.756 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- client/src/components/ui/toast.tsx [3ms]
2025-06-03 03:31:42.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-03 03:31:42.952 [info] > git config --get commit.template [67ms]
2025-06-03 03:31:43.103 [info] > git status -z -uall [107ms]
2025-06-03 03:31:43.103 [info] > git check-ignore -v -z --stdin [58ms]
2025-06-03 03:31:43.104 [info] > git show --textconv :client/src/lib/api.ts [33ms]
2025-06-03 03:31:43.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [77ms]
2025-06-03 03:31:43.132 [info] > git ls-files --stage -- client/src/lib/api.ts [35ms]
2025-06-03 03:31:43.177 [info] > git cat-file -s 46737e1c6f82a208a09befa57f1d42140f720549 [11ms]
2025-06-03 03:31:43.485 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- client/src/lib/api.ts [55ms]
2025-06-03 03:31:48.153 [info] > git config --get commit.template [22ms]
2025-06-03 03:31:48.155 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:31:48.192 [info] > git status -z -uall [20ms]
2025-06-03 03:31:48.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:31:53.216 [info] > git config --get commit.template [1ms]
2025-06-03 03:31:53.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:31:53.289 [info] > git status -z -uall [19ms]
2025-06-03 03:31:53.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:31:58.314 [info] > git config --get commit.template [7ms]
2025-06-03 03:31:58.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:31:58.331 [info] > git status -z -uall [10ms]
2025-06-03 03:31:58.333 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:03.356 [info] > git config --get commit.template [3ms]
2025-06-03 03:32:03.371 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:32:03.418 [info] > git status -z -uall [32ms]
2025-06-03 03:32:03.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:32:08.481 [info] > git config --get commit.template [19ms]
2025-06-03 03:32:08.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:32:08.506 [info] > git status -z -uall [11ms]
2025-06-03 03:32:08.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:13.535 [info] > git config --get commit.template [12ms]
2025-06-03 03:32:13.536 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:32:13.553 [info] > git status -z -uall [7ms]
2025-06-03 03:32:13.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:18.584 [info] > git config --get commit.template [12ms]
2025-06-03 03:32:18.584 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:32:18.606 [info] > git status -z -uall [10ms]
2025-06-03 03:32:18.608 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:23.635 [info] > git config --get commit.template [10ms]
2025-06-03 03:32:23.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:32:23.651 [info] > git status -z -uall [7ms]
2025-06-03 03:32:23.652 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:32:29.047 [info] > git config --get commit.template [13ms]
2025-06-03 03:32:29.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:32:29.062 [info] > git status -z -uall [6ms]
2025-06-03 03:32:29.064 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:35.241 [info] > git config --get commit.template [32ms]
2025-06-03 03:32:35.499 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [238ms]
2025-06-03 03:32:35.531 [info] > git status -z -uall [10ms]
2025-06-03 03:32:35.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:40.554 [info] > git config --get commit.template [7ms]
2025-06-03 03:32:40.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:32:40.570 [info] > git status -z -uall [7ms]
2025-06-03 03:32:40.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:45.664 [info] > git config --get commit.template [11ms]
2025-06-03 03:32:45.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:32:45.684 [info] > git status -z -uall [11ms]
2025-06-03 03:32:45.685 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:32:50.709 [info] > git config --get commit.template [8ms]
2025-06-03 03:32:50.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:32:50.728 [info] > git status -z -uall [9ms]
2025-06-03 03:32:50.729 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:32:55.755 [info] > git config --get commit.template [8ms]
2025-06-03 03:32:55.755 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:32:55.832 [info] > git status -z -uall [64ms]
2025-06-03 03:32:55.833 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-06-03 03:33:00.855 [info] > git config --get commit.template [2ms]
2025-06-03 03:33:00.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:33:00.893 [info] > git status -z -uall [10ms]
2025-06-03 03:33:00.894 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:33:05.920 [info] > git config --get commit.template [9ms]
2025-06-03 03:33:05.921 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:33:05.938 [info] > git status -z -uall [10ms]
2025-06-03 03:33:05.939 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:33:13.103 [info] > git config --get commit.template [11ms]
2025-06-03 03:33:13.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:33:13.127 [info] > git status -z -uall [11ms]
2025-06-03 03:33:13.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:33:18.148 [info] > git config --get commit.template [2ms]
2025-06-03 03:33:18.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:33:18.182 [info] > git status -z -uall [13ms]
2025-06-03 03:33:18.183 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:33:23.217 [info] > git config --get commit.template [15ms]
2025-06-03 03:33:23.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-03 03:33:23.250 [info] > git status -z -uall [11ms]
2025-06-03 03:33:23.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:33:28.279 [info] > git config --get commit.template [7ms]
2025-06-03 03:33:28.279 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:33:28.292 [info] > git status -z -uall [6ms]
2025-06-03 03:33:28.293 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:33:33.322 [info] > git config --get commit.template [12ms]
2025-06-03 03:33:33.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:33:33.349 [info] > git status -z -uall [14ms]
2025-06-03 03:33:33.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:33:38.535 [info] > git config --get commit.template [51ms]
2025-06-03 03:33:38.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [29ms]
2025-06-03 03:33:38.599 [info] > git status -z -uall [33ms]
2025-06-03 03:33:38.605 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-03 03:33:44.555 [info] > git config --get commit.template [5ms]
2025-06-03 03:33:44.577 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-03 03:33:44.610 [info] > git status -z -uall [14ms]
2025-06-03 03:33:44.612 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:33:49.633 [info] > git config --get commit.template [7ms]
2025-06-03 03:33:49.634 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:33:49.650 [info] > git status -z -uall [8ms]
2025-06-03 03:33:49.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:33:54.673 [info] > git config --get commit.template [7ms]
2025-06-03 03:33:54.674 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:33:54.687 [info] > git status -z -uall [6ms]
2025-06-03 03:33:54.688 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:33:59.718 [info] > git config --get commit.template [12ms]
2025-06-03 03:33:59.718 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:33:59.749 [info] > git status -z -uall [16ms]
2025-06-03 03:33:59.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:34:04.820 [info] > git config --get commit.template [40ms]
2025-06-03 03:34:04.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:34:04.871 [info] > git status -z -uall [27ms]
2025-06-03 03:34:04.877 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-03 03:34:24.522 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:34:24.522 [info] > git config --get commit.template [18ms]
2025-06-03 03:34:24.555 [info] > git status -z -uall [12ms]
2025-06-03 03:34:24.555 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:34:32.012 [info] > git config --get commit.template [10ms]
2025-06-03 03:34:32.029 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [18ms]
2025-06-03 03:34:32.060 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-03 03:34:32.083 [info] > git status -z -uall [41ms]
2025-06-03 03:35:03.458 [info] > git config --get commit.template [2ms]
2025-06-03 03:35:03.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:03.485 [info] > git status -z -uall [7ms]
2025-06-03 03:35:03.486 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:08.514 [info] > git config --get commit.template [11ms]
2025-06-03 03:35:08.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:35:08.534 [info] > git status -z -uall [8ms]
2025-06-03 03:35:08.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:13.563 [info] > git config --get commit.template [11ms]
2025-06-03 03:35:13.564 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:13.584 [info] > git status -z -uall [7ms]
2025-06-03 03:35:13.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:18.618 [info] > git config --get commit.template [12ms]
2025-06-03 03:35:18.619 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:18.637 [info] > git status -z -uall [8ms]
2025-06-03 03:35:18.638 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:23.659 [info] > git config --get commit.template [7ms]
2025-06-03 03:35:23.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:23.672 [info] > git status -z -uall [6ms]
2025-06-03 03:35:23.673 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:35:28.697 [info] > git config --get commit.template [9ms]
2025-06-03 03:35:28.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:28.721 [info] > git status -z -uall [8ms]
2025-06-03 03:35:28.722 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:33.748 [info] > git config --get commit.template [12ms]
2025-06-03 03:35:33.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:35:33.766 [info] > git status -z -uall [9ms]
2025-06-03 03:35:33.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:35:38.796 [info] > git config --get commit.template [1ms]
2025-06-03 03:35:38.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:35:38.861 [info] > git status -z -uall [23ms]
2025-06-03 03:35:38.861 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:37:13.298 [info] > git config --get commit.template [9ms]
2025-06-03 03:37:13.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:37:13.314 [info] > git status -z -uall [7ms]
2025-06-03 03:37:13.315 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:37:18.345 [info] > git config --get commit.template [12ms]
2025-06-03 03:37:18.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:18.365 [info] > git status -z -uall [9ms]
2025-06-03 03:37:18.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:37:23.401 [info] > git config --get commit.template [14ms]
2025-06-03 03:37:23.403 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:37:23.425 [info] > git status -z -uall [11ms]
2025-06-03 03:37:23.428 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:37:28.451 [info] > git config --get commit.template [7ms]
2025-06-03 03:37:28.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:28.470 [info] > git status -z -uall [11ms]
2025-06-03 03:37:28.471 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:37:33.493 [info] > git config --get commit.template [1ms]
2025-06-03 03:37:33.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:33.544 [info] > git status -z -uall [17ms]
2025-06-03 03:37:33.545 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:37:38.562 [info] > git config --get commit.template [1ms]
2025-06-03 03:37:38.578 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:38.616 [info] > git status -z -uall [17ms]
2025-06-03 03:37:38.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:37:43.657 [info] > git config --get commit.template [17ms]
2025-06-03 03:37:43.657 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:43.690 [info] > git status -z -uall [18ms]
2025-06-03 03:37:43.691 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:37:48.715 [info] > git config --get commit.template [8ms]
2025-06-03 03:37:48.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:37:48.734 [info] > git status -z -uall [10ms]
2025-06-03 03:37:48.734 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:38:02.620 [info] > git config --get commit.template [11ms]
2025-06-03 03:38:02.622 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:38:02.650 [info] > git status -z -uall [14ms]
2025-06-03 03:38:02.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:38:07.684 [info] > git config --get commit.template [13ms]
2025-06-03 03:38:07.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:38:07.716 [info] > git status -z -uall [12ms]
2025-06-03 03:38:07.717 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:38:12.739 [info] > git config --get commit.template [6ms]
2025-06-03 03:38:12.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:38:12.753 [info] > git status -z -uall [6ms]
2025-06-03 03:38:12.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:38:17.775 [info] > git config --get commit.template [7ms]
2025-06-03 03:38:17.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:38:17.795 [info] > git status -z -uall [9ms]
2025-06-03 03:38:17.795 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:38:22.822 [info] > git config --get commit.template [9ms]
2025-06-03 03:38:22.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:38:22.857 [info] > git status -z -uall [21ms]
2025-06-03 03:38:22.859 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:38:27.906 [info] > git config --get commit.template [19ms]
2025-06-03 03:38:27.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:38:27.945 [info] > git status -z -uall [18ms]
2025-06-03 03:38:27.946 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:38:32.972 [info] > git config --get commit.template [2ms]
2025-06-03 03:38:32.988 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:38:33.025 [info] > git status -z -uall [19ms]
2025-06-03 03:38:33.029 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-03 03:38:38.095 [info] > git config --get commit.template [33ms]
2025-06-03 03:38:38.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-03 03:38:38.146 [info] > git status -z -uall [27ms]
2025-06-03 03:38:38.153 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-03 03:38:43.180 [info] > git config --get commit.template [1ms]
2025-06-03 03:38:43.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:38:43.250 [info] > git status -z -uall [24ms]
2025-06-03 03:38:43.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:38:48.289 [info] > git config --get commit.template [12ms]
2025-06-03 03:38:48.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:38:48.307 [info] > git status -z -uall [7ms]
2025-06-03 03:38:48.309 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:38:53.344 [info] > git config --get commit.template [13ms]
2025-06-03 03:38:53.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:38:53.371 [info] > git status -z -uall [8ms]
2025-06-03 03:38:53.373 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:39:23.096 [info] > git config --get commit.template [14ms]
2025-06-03 03:39:23.099 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:39:23.128 [info] > git status -z -uall [14ms]
2025-06-03 03:39:23.130 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:39:28.155 [info] > git config --get commit.template [10ms]
2025-06-03 03:39:28.156 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:39:28.176 [info] > git status -z -uall [13ms]
2025-06-03 03:39:28.177 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:39:33.203 [info] > git config --get commit.template [8ms]
2025-06-03 03:39:33.204 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:39:33.221 [info] > git status -z -uall [8ms]
2025-06-03 03:39:33.222 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:39:38.251 [info] > git config --get commit.template [10ms]
2025-06-03 03:39:38.252 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:39:38.286 [info] > git status -z -uall [21ms]
2025-06-03 03:39:38.286 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:40:32.970 [info] > git config --get commit.template [8ms]
2025-06-03 03:40:32.983 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:40:33.002 [info] > git status -z -uall [9ms]
2025-06-03 03:40:33.003 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:40:38.050 [info] > git config --get commit.template [11ms]
2025-06-03 03:40:38.050 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:40:38.074 [info] > git status -z -uall [12ms]
2025-06-03 03:40:38.075 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:40:43.105 [info] > git config --get commit.template [11ms]
2025-06-03 03:40:43.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:40:43.130 [info] > git status -z -uall [13ms]
2025-06-03 03:40:43.132 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:40:48.162 [info] > git config --get commit.template [12ms]
2025-06-03 03:40:48.163 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:40:48.194 [info] > git status -z -uall [18ms]
2025-06-03 03:40:48.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:40:53.223 [info] > git config --get commit.template [11ms]
2025-06-03 03:40:53.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:40:53.241 [info] > git status -z -uall [9ms]
2025-06-03 03:40:53.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:40:58.266 [info] > git config --get commit.template [7ms]
2025-06-03 03:40:58.267 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:40:58.283 [info] > git status -z -uall [7ms]
2025-06-03 03:40:58.284 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:41:03.315 [info] > git config --get commit.template [12ms]
2025-06-03 03:41:03.316 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:41:03.332 [info] > git status -z -uall [7ms]
2025-06-03 03:41:03.333 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:41:08.358 [info] > git config --get commit.template [9ms]
2025-06-03 03:41:08.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:41:08.373 [info] > git status -z -uall [8ms]
2025-06-03 03:41:08.375 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:41:13.405 [info] > git config --get commit.template [15ms]
2025-06-03 03:41:13.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:41:13.429 [info] > git status -z -uall [12ms]
2025-06-03 03:41:13.430 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:41:18.458 [info] > git config --get commit.template [11ms]
2025-06-03 03:41:18.459 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:41:18.474 [info] > git status -z -uall [7ms]
2025-06-03 03:41:18.475 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:41:23.518 [info] > git config --get commit.template [20ms]
2025-06-03 03:41:23.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:41:23.550 [info] > git status -z -uall [17ms]
2025-06-03 03:41:23.551 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:41:28.574 [info] > git config --get commit.template [8ms]
2025-06-03 03:41:28.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:41:28.606 [info] > git status -z -uall [18ms]
2025-06-03 03:41:28.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:41:33.629 [info] > git config --get commit.template [7ms]
2025-06-03 03:41:33.629 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:41:33.645 [info] > git status -z -uall [7ms]
2025-06-03 03:41:33.647 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:41:38.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:41:38.681 [info] > git config --get commit.template [15ms]
2025-06-03 03:41:38.714 [info] > git status -z -uall [15ms]
2025-06-03 03:41:38.715 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:41:43.743 [info] > git config --get commit.template [11ms]
2025-06-03 03:41:43.744 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:41:43.763 [info] > git status -z -uall [8ms]
2025-06-03 03:41:43.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:41:48.812 [info] > git config --get commit.template [19ms]
2025-06-03 03:41:48.814 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:41:48.844 [info] > git status -z -uall [13ms]
2025-06-03 03:41:48.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:41:53.866 [info] > git config --get commit.template [7ms]
2025-06-03 03:41:53.867 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:41:53.881 [info] > git status -z -uall [8ms]
2025-06-03 03:41:53.882 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:41:58.910 [info] > git config --get commit.template [1ms]
2025-06-03 03:41:58.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:41:59.037 [info] > git status -z -uall [74ms]
2025-06-03 03:41:59.037 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [56ms]
2025-06-03 03:42:04.063 [info] > git config --get commit.template [10ms]
2025-06-03 03:42:04.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:42:04.084 [info] > git status -z -uall [9ms]
2025-06-03 03:42:04.085 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:42:09.192 [info] > git config --get commit.template [4ms]
2025-06-03 03:42:09.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:42:09.337 [info] > git status -z -uall [89ms]
2025-06-03 03:42:09.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [68ms]
2025-06-03 03:42:14.371 [info] > git config --get commit.template [15ms]
2025-06-03 03:42:14.372 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:42:14.402 [info] > git status -z -uall [15ms]
2025-06-03 03:42:14.404 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:42:19.429 [info] > git config --get commit.template [9ms]
2025-06-03 03:42:19.430 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:42:19.445 [info] > git status -z -uall [7ms]
2025-06-03 03:42:19.446 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:44:17.225 [info] > git config --get commit.template [7ms]
2025-06-03 03:44:17.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:44:17.242 [info] > git status -z -uall [6ms]
2025-06-03 03:44:17.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:44:22.266 [info] > git config --get commit.template [8ms]
2025-06-03 03:44:22.267 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:44:22.281 [info] > git status -z -uall [8ms]
2025-06-03 03:44:22.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:44:27.306 [info] > git config --get commit.template [8ms]
2025-06-03 03:44:27.307 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:44:27.322 [info] > git status -z -uall [6ms]
2025-06-03 03:44:27.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:44:32.343 [info] > git config --get commit.template [6ms]
2025-06-03 03:44:32.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:44:32.357 [info] > git status -z -uall [6ms]
2025-06-03 03:44:32.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:44:37.383 [info] > git config --get commit.template [10ms]
2025-06-03 03:44:37.384 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:44:37.399 [info] > git status -z -uall [7ms]
2025-06-03 03:44:37.400 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:44:42.420 [info] > git config --get commit.template [6ms]
2025-06-03 03:44:42.421 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:44:42.438 [info] > git status -z -uall [8ms]
2025-06-03 03:44:42.439 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:44:47.461 [info] > git config --get commit.template [7ms]
2025-06-03 03:44:47.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:44:47.476 [info] > git status -z -uall [8ms]
2025-06-03 03:44:47.477 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:44:52.504 [info] > git config --get commit.template [9ms]
2025-06-03 03:44:52.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:44:52.526 [info] > git status -z -uall [10ms]
2025-06-03 03:44:52.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:44:57.556 [info] > git config --get commit.template [10ms]
2025-06-03 03:44:57.557 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:44:57.577 [info] > git status -z -uall [12ms]
2025-06-03 03:44:57.579 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:45:12.968 [info] > git config --get commit.template [14ms]
2025-06-03 03:45:12.969 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:45:12.994 [info] > git status -z -uall [13ms]
2025-06-03 03:45:12.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:45:18.023 [info] > git config --get commit.template [12ms]
2025-06-03 03:45:18.024 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:45:18.094 [info] > git status -z -uall [61ms]
2025-06-03 03:45:18.095 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [55ms]
2025-06-03 03:45:23.126 [info] > git config --get commit.template [13ms]
2025-06-03 03:45:23.127 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:45:23.146 [info] > git status -z -uall [9ms]
2025-06-03 03:45:23.147 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:45:28.186 [info] > git config --get commit.template [21ms]
2025-06-03 03:45:28.187 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:45:28.232 [info] > git status -z -uall [21ms]
2025-06-03 03:45:28.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:45:33.258 [info] > git config --get commit.template [9ms]
2025-06-03 03:45:33.259 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:45:33.275 [info] > git status -z -uall [7ms]
2025-06-03 03:45:33.276 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:45:38.304 [info] > git config --get commit.template [13ms]
2025-06-03 03:45:38.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-03 03:45:38.334 [info] > git status -z -uall [9ms]
2025-06-03 03:45:38.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:45:43.358 [info] > git config --get commit.template [1ms]
2025-06-03 03:45:43.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:45:43.414 [info] > git status -z -uall [16ms]
2025-06-03 03:45:43.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:45:48.442 [info] > git config --get commit.template [10ms]
2025-06-03 03:45:48.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:45:48.459 [info] > git status -z -uall [7ms]
2025-06-03 03:45:48.460 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:45:53.484 [info] > git config --get commit.template [8ms]
2025-06-03 03:45:53.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:45:53.514 [info] > git status -z -uall [12ms]
2025-06-03 03:45:53.516 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:45:58.542 [info] > git config --get commit.template [11ms]
2025-06-03 03:45:58.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:45:58.558 [info] > git status -z -uall [7ms]
2025-06-03 03:45:58.559 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:46:03.581 [info] > git config --get commit.template [7ms]
2025-06-03 03:46:03.582 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:46:03.594 [info] > git status -z -uall [6ms]
2025-06-03 03:46:03.596 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:46:08.631 [info] > git config --get commit.template [15ms]
2025-06-03 03:46:08.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:46:08.664 [info] > git status -z -uall [13ms]
2025-06-03 03:46:08.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:46:13.692 [info] > git config --get commit.template [11ms]
2025-06-03 03:46:13.693 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:46:13.712 [info] > git status -z -uall [9ms]
2025-06-03 03:46:13.713 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:46:18.738 [info] > git config --get commit.template [10ms]
2025-06-03 03:46:18.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:46:18.763 [info] > git status -z -uall [13ms]
2025-06-03 03:46:18.764 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-03 03:46:25.074 [info] > git config --get commit.template [9ms]
2025-06-03 03:46:25.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:46:25.090 [info] > git status -z -uall [7ms]
2025-06-03 03:46:25.092 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:46:30.113 [info] > git config --get commit.template [8ms]
2025-06-03 03:46:30.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:46:30.129 [info] > git status -z -uall [7ms]
2025-06-03 03:46:30.130 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:46:35.168 [info] > git config --get commit.template [12ms]
2025-06-03 03:46:35.168 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:46:35.184 [info] > git status -z -uall [6ms]
2025-06-03 03:46:35.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:46:40.208 [info] > git config --get commit.template [7ms]
2025-06-03 03:46:40.209 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:46:40.222 [info] > git status -z -uall [6ms]
2025-06-03 03:46:40.224 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:46:45.244 [info] > git config --get commit.template [7ms]
2025-06-03 03:46:45.245 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:46:45.267 [info] > git status -z -uall [7ms]
2025-06-03 03:46:45.269 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:46:50.291 [info] > git config --get commit.template [7ms]
2025-06-03 03:46:50.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:46:50.306 [info] > git status -z -uall [6ms]
2025-06-03 03:46:50.307 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:46:55.333 [info] > git config --get commit.template [9ms]
2025-06-03 03:46:55.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:46:55.349 [info] > git status -z -uall [6ms]
2025-06-03 03:46:55.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:47:00.374 [info] > git config --get commit.template [7ms]
2025-06-03 03:47:00.376 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:47:00.393 [info] > git status -z -uall [9ms]
2025-06-03 03:47:00.394 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:47:05.417 [info] > git config --get commit.template [7ms]
2025-06-03 03:47:05.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:47:05.431 [info] > git status -z -uall [7ms]
2025-06-03 03:47:05.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:47:10.467 [info] > git config --get commit.template [15ms]
2025-06-03 03:47:10.468 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:47:10.580 [info] > git status -z -uall [104ms]
2025-06-03 03:47:10.581 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [99ms]
2025-06-03 03:47:15.599 [info] > git config --get commit.template [3ms]
2025-06-03 03:47:15.610 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:47:15.628 [info] > git status -z -uall [8ms]
2025-06-03 03:47:15.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:47:20.647 [info] > git config --get commit.template [2ms]
2025-06-03 03:47:20.664 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-03 03:47:20.681 [info] > git status -z -uall [7ms]
2025-06-03 03:47:20.683 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:47:25.703 [info] > git config --get commit.template [6ms]
2025-06-03 03:47:25.704 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:47:25.766 [info] > git status -z -uall [54ms]
2025-06-03 03:47:25.766 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [46ms]
2025-06-03 03:47:30.792 [info] > git config --get commit.template [10ms]
2025-06-03 03:47:30.793 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:47:30.812 [info] > git status -z -uall [10ms]
2025-06-03 03:47:30.814 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:47:35.861 [info] > git config --get commit.template [20ms]
2025-06-03 03:47:35.863 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:47:35.897 [info] > git status -z -uall [19ms]
2025-06-03 03:47:35.902 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:47:40.924 [info] > git config --get commit.template [8ms]
2025-06-03 03:47:40.926 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:47:40.943 [info] > git status -z -uall [8ms]
2025-06-03 03:47:40.946 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:47:45.968 [info] > git config --get commit.template [7ms]
2025-06-03 03:47:45.969 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:47:45.986 [info] > git status -z -uall [7ms]
2025-06-03 03:47:45.988 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:47:53.279 [info] > git config --get commit.template [12ms]
2025-06-03 03:47:53.282 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-03 03:47:53.303 [info] > git status -z -uall [11ms]
2025-06-03 03:47:53.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:47:58.337 [info] > git config --get commit.template [17ms]
2025-06-03 03:47:58.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:47:58.360 [info] > git status -z -uall [10ms]
2025-06-03 03:47:58.362 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:48:03.391 [info] > git config --get commit.template [9ms]
2025-06-03 03:48:03.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:48:03.411 [info] > git status -z -uall [9ms]
2025-06-03 03:48:03.413 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:48:08.449 [info] > git config --get commit.template [12ms]
2025-06-03 03:48:08.451 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:48:08.479 [info] > git status -z -uall [11ms]
2025-06-03 03:48:08.480 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:48:13.515 [info] > git config --get commit.template [15ms]
2025-06-03 03:48:13.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:48:13.539 [info] > git status -z -uall [11ms]
2025-06-03 03:48:13.540 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:48:18.574 [info] > git config --get commit.template [15ms]
2025-06-03 03:48:18.576 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:48:18.605 [info] > git status -z -uall [14ms]
2025-06-03 03:48:18.606 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:48:23.637 [info] > git config --get commit.template [12ms]
2025-06-03 03:48:23.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:48:23.653 [info] > git status -z -uall [6ms]
2025-06-03 03:48:23.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:48:28.698 [info] > git config --get commit.template [27ms]
2025-06-03 03:48:28.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:48:28.752 [info] > git status -z -uall [23ms]
2025-06-03 03:48:28.753 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:48:33.782 [info] > git config --get commit.template [12ms]
2025-06-03 03:48:33.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:48:33.807 [info] > git status -z -uall [10ms]
2025-06-03 03:48:33.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:48:38.832 [info] > git config --get commit.template [1ms]
2025-06-03 03:48:38.856 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:48:38.887 [info] > git status -z -uall [19ms]
2025-06-03 03:48:38.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:48:59.185 [info] > git config --get commit.template [6ms]
2025-06-03 03:48:59.186 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:48:59.201 [info] > git status -z -uall [8ms]
2025-06-03 03:48:59.202 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:49:04.238 [info] > git config --get commit.template [16ms]
2025-06-03 03:49:04.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:49:04.267 [info] > git status -z -uall [15ms]
2025-06-03 03:49:04.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:49:09.297 [info] > git config --get commit.template [9ms]
2025-06-03 03:49:09.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:49:09.315 [info] > git status -z -uall [7ms]
2025-06-03 03:49:09.316 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:49:14.342 [info] > git config --get commit.template [10ms]
2025-06-03 03:49:14.343 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:49:14.357 [info] > git status -z -uall [7ms]
2025-06-03 03:49:14.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:49:19.380 [info] > git config --get commit.template [9ms]
2025-06-03 03:49:19.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:49:19.402 [info] > git status -z -uall [9ms]
2025-06-03 03:49:19.403 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:49:24.429 [info] > git config --get commit.template [0ms]
2025-06-03 03:49:24.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:49:24.466 [info] > git status -z -uall [8ms]
2025-06-03 03:49:24.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:49:29.491 [info] > git config --get commit.template [8ms]
2025-06-03 03:49:29.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:49:29.505 [info] > git status -z -uall [6ms]
2025-06-03 03:49:29.506 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:49:34.543 [info] > git config --get commit.template [17ms]
2025-06-03 03:49:34.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:49:34.564 [info] > git status -z -uall [9ms]
2025-06-03 03:49:34.566 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:49:36.802 [info] > git show --textconv :client/src/components/ui/toast.tsx [22ms]
2025-06-03 03:49:36.804 [info] > git ls-files --stage -- client/src/components/ui/toast.tsx [5ms]
2025-06-03 03:49:36.823 [info] > git cat-file -s a822477534192c4df5073e4015f7461e739d3344 [4ms]
2025-06-03 03:49:42.354 [info] > git config --get commit.template [12ms]
2025-06-03 03:49:42.355 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:49:42.368 [info] > git status -z -uall [6ms]
2025-06-03 03:49:42.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:49:47.408 [info] > git config --get commit.template [21ms]
2025-06-03 03:49:47.411 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:49:47.455 [info] > git status -z -uall [16ms]
2025-06-03 03:49:47.456 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:49:52.479 [info] > git config --get commit.template [6ms]
2025-06-03 03:49:52.480 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:49:52.497 [info] > git status -z -uall [9ms]
2025-06-03 03:49:52.498 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:49:57.529 [info] > git config --get commit.template [13ms]
2025-06-03 03:49:57.530 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:49:57.546 [info] > git status -z -uall [8ms]
2025-06-03 03:49:57.547 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:50:02.582 [info] > git config --get commit.template [18ms]
2025-06-03 03:50:02.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:50:02.616 [info] > git status -z -uall [15ms]
2025-06-03 03:50:02.618 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:50:07.646 [info] > git config --get commit.template [11ms]
2025-06-03 03:50:07.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:50:07.663 [info] > git status -z -uall [8ms]
2025-06-03 03:50:07.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:50:12.690 [info] > git config --get commit.template [11ms]
2025-06-03 03:50:12.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:50:12.705 [info] > git status -z -uall [6ms]
2025-06-03 03:50:12.707 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:50:17.729 [info] > git config --get commit.template [8ms]
2025-06-03 03:50:17.730 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:50:17.742 [info] > git status -z -uall [6ms]
2025-06-03 03:50:17.743 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:50:22.765 [info] > git config --get commit.template [7ms]
2025-06-03 03:50:22.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:50:22.779 [info] > git status -z -uall [6ms]
2025-06-03 03:50:22.781 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:50:27.807 [info] > git config --get commit.template [11ms]
2025-06-03 03:50:27.808 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:50:27.825 [info] > git status -z -uall [9ms]
2025-06-03 03:50:27.825 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-03 03:50:32.849 [info] > git config --get commit.template [9ms]
2025-06-03 03:50:32.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:50:32.865 [info] > git status -z -uall [7ms]
2025-06-03 03:50:32.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:50:37.893 [info] > git config --get commit.template [11ms]
2025-06-03 03:50:37.895 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:50:37.914 [info] > git status -z -uall [9ms]
2025-06-03 03:50:37.914 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:50:42.947 [info] > git config --get commit.template [16ms]
2025-06-03 03:50:42.948 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:50:42.976 [info] > git status -z -uall [14ms]
2025-06-03 03:50:42.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:52:49.892 [info] > git config --get commit.template [16ms]
2025-06-03 03:52:49.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:52:49.931 [info] > git status -z -uall [21ms]
2025-06-03 03:52:49.932 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:52:54.970 [info] > git config --get commit.template [14ms]
2025-06-03 03:52:54.972 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:52:55.026 [info] > git status -z -uall [22ms]
2025-06-03 03:52:55.029 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 03:53:00.063 [info] > git config --get commit.template [10ms]
2025-06-03 03:53:00.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:53:00.091 [info] > git status -z -uall [11ms]
2025-06-03 03:53:00.093 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 03:53:05.131 [info] > git config --get commit.template [16ms]
2025-06-03 03:53:05.132 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:53:05.218 [info] > git status -z -uall [70ms]
2025-06-03 03:53:05.219 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [58ms]
2025-06-03 03:53:10.242 [info] > git config --get commit.template [7ms]
2025-06-03 03:53:10.243 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:53:10.256 [info] > git status -z -uall [7ms]
2025-06-03 03:53:10.257 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:53:15.279 [info] > git config --get commit.template [8ms]
2025-06-03 03:53:15.279 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:53:15.294 [info] > git status -z -uall [7ms]
2025-06-03 03:53:15.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:53:20.323 [info] > git config --get commit.template [9ms]
2025-06-03 03:53:20.324 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:53:20.347 [info] > git status -z -uall [12ms]
2025-06-03 03:53:20.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:53:25.382 [info] > git config --get commit.template [16ms]
2025-06-03 03:53:25.383 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:53:25.411 [info] > git status -z -uall [12ms]
2025-06-03 03:53:25.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:53:30.434 [info] > git config --get commit.template [7ms]
2025-06-03 03:53:30.435 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:53:30.450 [info] > git status -z -uall [7ms]
2025-06-03 03:53:30.451 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:53:35.506 [info] > git config --get commit.template [3ms]
2025-06-03 03:53:35.518 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 03:53:35.533 [info] > git status -z -uall [7ms]
2025-06-03 03:53:35.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:53:40.557 [info] > git config --get commit.template [9ms]
2025-06-03 03:53:40.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:53:40.575 [info] > git status -z -uall [10ms]
2025-06-03 03:53:40.576 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 03:53:45.606 [info] > git config --get commit.template [14ms]
2025-06-03 03:53:45.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 03:53:45.630 [info] > git status -z -uall [8ms]
2025-06-03 03:53:45.631 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:53:50.655 [info] > git config --get commit.template [7ms]
2025-06-03 03:53:50.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 03:53:50.669 [info] > git status -z -uall [6ms]
2025-06-03 03:53:50.670 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 03:54:24.912 [info] > git config --get commit.template [8ms]
2025-06-03 03:54:24.913 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 03:54:24.932 [info] > git status -z -uall [10ms]
2025-06-03 03:54:24.933 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
