import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import Spinner from "@/components/ui/Spinner";

interface QuizGenerationOptions {
  numberOfQuestions: number;
  customPrompt: string;
}

interface DashboardQuizGenerationPopupProps {
  trigger: React.ReactNode;
  onGenerate: (options: QuizGenerationOptions) => Promise<void>;
  isGenerating: boolean;
  disabled?: boolean;
  maxQuestions?: number;
}

export const DashboardQuizGenerationPopup: React.FC<DashboardQuizGenerationPopupProps> = ({
  trigger,
  onGenerate,
  isGenerating,
  disabled = false,
  maxQuestions = 20,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<QuizGenerationOptions>({
    numberOfQuestions: 5,
    customPrompt: "",
  });

  const handleGenerate = async () => {
    if (options.numberOfQuestions <= 0) {
      return; // Validation handled by UI
    }

    try {
      await onGenerate(options);
      setIsOpen(false);
    } catch (error) {
      // Error handling is done by parent component
      console.error("Quiz generation failed:", error);
    }
  };

  const isValidOptions = options.numberOfQuestions > 0 && options.numberOfQuestions <= maxQuestions;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild disabled={disabled}>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] bg-card border-border">
        <DialogHeader>
          <DialogTitle className="text-card-foreground">Generate Quiz Options</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* Number of Questions */}
          <div className="space-y-2">
            <Label htmlFor="numberOfQuestions" className="text-card-foreground">
              Number of Questions
            </Label>
            <Input
              id="numberOfQuestions"
              type="number"
              min="1"
              max={maxQuestions}
              value={options.numberOfQuestions}
              onChange={(e) =>
                setOptions((prev) => ({
                  ...prev,
                  numberOfQuestions: Math.max(1, Math.min(maxQuestions, parseInt(e.target.value) || 1)),
                }))
              }
              className="bg-background border-border text-foreground"
            />
            <p className="text-xs text-muted-foreground">
              Choose between 1 and {maxQuestions} questions
            </p>
          </div>

          {/* Custom Prompt */}
          <div className="space-y-2">
            <Label htmlFor="customPrompt" className="text-card-foreground">
              Custom Prompt (Optional)
            </Label>
            <Textarea
              id="customPrompt"
              placeholder="e.g., 'Focus on definitions', 'Create scenario-based questions', 'Make questions suitable for beginners'"
              value={options.customPrompt}
              onChange={(e) =>
                setOptions((prev) => ({
                  ...prev,
                  customPrompt: e.target.value,
                }))
              }
              className="bg-background border-border text-foreground min-h-[80px]"
            />
            <p className="text-xs text-muted-foreground">
              Add specific instructions for the AI on what kind of questions you want.
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isGenerating}
          >
            Cancel
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !isValidOptions}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            {isGenerating ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Generating...
              </>
            ) : (
              "Generate Quiz"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
