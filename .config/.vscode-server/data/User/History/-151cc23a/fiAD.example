# =============================================================================
# ChewyAI Environment Configuration
# =============================================================================
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# =============================================================================
# CLIENT ENVIRONMENT VARIABLES (VITE_ prefix for Vite)
# =============================================================================
# These are exposed to the browser - only include non-sensitive values

# Supabase Configuration (Client)
VITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0qS0Nh1aV4GjZYniANoUZKDDcuCcQxzTm-DnTeZsNlQ

# API Configuration
VITE_API_BASE_URL=http://localhost:5000/api

# =============================================================================
# SERVER ENVIRONMENT VARIABLES
# =============================================================================
# These are only available on the server - include sensitive values here

# Application Configuration
NODE_ENV=development
PORT=5000

# Supabase Configuration (Server)
SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0

# Database Configuration (if using direct database access)
VITE_DATABASE_PASSWORD=Aloha808!

# =============================================================================
# AI PROVIDER CONFIGURATION (Optional)
# =============================================================================
# Default AI provider settings - users will configure their own in the app

DEFAULT_AI_PROVIDER=openrouter
DEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash-preview-05-20
DEFAULT_GENERATION_MODEL=google/gemini-2.5-pro-preview

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Generate secure random strings for production

JWT_SECRET=michelle
SESSION_SECRET=2019

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Frontend URL for CORS configuration

FRONTEND_URL=http://localhost:3000

# =============================================================================
# ADDITIONAL DATABASE CONFIGURATION (Optional)
# =============================================================================
# For local SQLite storage if needed

DATABASE_URL=./data/chewyai.sqlite

# =============================================================================
# PRODUCTION DEPLOYMENT NOTES
# =============================================================================
# For Replit deployment, set these in Replit Secrets:
# - SUPABASE_URL
# - SUPABASE_SERVICE_ROLE_KEY
# - VITE_SUPABASE_URL
# - VITE_SUPABASE_ANON_KEY
# - NODE_ENV=production
# - PORT=80
#
# The VITE_ variables will be automatically included in the client build
# The server variables will only be available to the backend