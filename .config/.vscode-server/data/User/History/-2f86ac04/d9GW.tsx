import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import 'highlight.js/styles/github-dark.css';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

// Function to preprocess content and improve formatting
const preprocessContent = (content: string): string => {
  if (!content) return '';

  // Split content into sentences and add proper spacing
  let processed = content
    // Handle specific patterns first
    // Convert "Question for the semester:" style headers
    .replace(/(Question for the [^:]+:)/g, '\n\n## $1\n\n')
    // Convert section headers (words in all caps)
    .replace(/\b([A-Z][A-Z\s]{4,}[A-Z])\b/g, '\n\n## $1\n\n')
    // Handle bullet points (● symbol) - make sure they're on new lines
    .replace(/●\s*/g, '\n\n• ')
    // Handle "O" bullet points (common in academic texts)
    .replace(/\sO\s+([A-Z])/g, '\n\n• $1')
    // Handle numbered lists
    .replace(/(\d+)\.\s+/g, '\n\n$1. ')
    // Add line breaks after sentences that end with periods followed by capital letters
    .replace(/\.\s+([A-Z][a-z])/g, '.\n\n$1')
    // Add line breaks after question marks followed by capital letters
    .replace(/\?\s+([A-Z][a-z])/g, '?\n\n$1')
    // Add line breaks after exclamation marks followed by capital letters
    .replace(/!\s+([A-Z][a-z])/g, '!\n\n$1')
    // Handle "Also:" style connectors
    .replace(/\bAlso:\s*/g, '\n\n**Also:**\n\n')
    // Clean up multiple consecutive line breaks
    .replace(/\n{3,}/g, '\n\n')
    // Trim whitespace
    .trim();

  return processed;
};

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = ''
}) => {
  // If content is empty or just whitespace, show a fallback
  if (!content || content.trim() === '') {
    return (
      <div className={`text-slate-400 italic ${className}`}>
        No content available
      </div>
    );
  }

  // Preprocess the content to improve formatting
  const processedContent = preprocessContent(content);

  return (
    <div className={`markdown-content max-w-none ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight, rehypeRaw]}
        components={{
          // Headings
          h1: ({ children }) => (
            <h1 className="text-2xl font-bold text-slate-100 mb-4 mt-6 border-b border-slate-600 pb-2">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-xl font-semibold text-slate-200 mb-3 mt-5">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-lg font-medium text-slate-200 mb-2 mt-4">
              {children}
            </h3>
          ),
          h4: ({ children }) => (
            <h4 className="text-base font-medium text-slate-300 mb-2 mt-3">
              {children}
            </h4>
          ),
          h5: ({ children }) => (
            <h5 className="text-sm font-medium text-slate-300 mb-1 mt-2">
              {children}
            </h5>
          ),
          h6: ({ children }) => (
            <h6 className="text-sm font-medium text-slate-400 mb-1 mt-2">
              {children}
            </h6>
          ),
          
          // Paragraphs
          p: ({ children }) => {
            // Handle long text blocks by adding better spacing and line height
            return (
              <p className="text-slate-200 mb-4 leading-relaxed text-base">
                {children}
              </p>
            );
          },
          
          // Lists
          ul: ({ children }) => (
            <ul className="list-disc list-inside text-slate-200 mb-3 space-y-1 ml-4">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside text-slate-200 mb-3 space-y-1 ml-4">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="text-slate-200 leading-relaxed">
              {children}
            </li>
          ),
          
          // Code
          code: ({ inline, children, className }: any) => {
            if (inline) {
              return (
                <code className="bg-slate-800 text-purple-300 px-1.5 py-0.5 rounded text-sm font-mono">
                  {children}
                </code>
              );
            }
            return (
              <code className={`${className} text-sm`}>
                {children}
              </code>
            );
          },
          pre: ({ children }) => (
            <pre className="bg-slate-900 border border-slate-700 rounded-lg p-4 mb-4 overflow-x-auto">
              {children}
            </pre>
          ),
          
          // Blockquotes
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-purple-500 pl-4 py-2 mb-4 bg-slate-800/50 rounded-r-lg">
              <div className="text-slate-300 italic">
                {children}
              </div>
            </blockquote>
          ),
          
          // Tables
          table: ({ children }) => (
            <div className="overflow-x-auto mb-4">
              <table className="min-w-full border border-slate-600 rounded-lg overflow-hidden">
                {children}
              </table>
            </div>
          ),
          thead: ({ children }) => (
            <thead className="bg-slate-700">
              {children}
            </thead>
          ),
          tbody: ({ children }) => (
            <tbody className="bg-slate-800">
              {children}
            </tbody>
          ),
          tr: ({ children }) => (
            <tr className="border-b border-slate-600">
              {children}
            </tr>
          ),
          th: ({ children }) => (
            <th className="px-4 py-2 text-left text-slate-200 font-medium">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-4 py-2 text-slate-300">
              {children}
            </td>
          ),
          
          // Links
          a: ({ href, children }) => (
            <a 
              href={href} 
              className="text-purple-400 hover:text-purple-300 underline transition-colors"
              target="_blank"
              rel="noopener noreferrer"
            >
              {children}
            </a>
          ),
          
          // Horizontal rules
          hr: () => (
            <hr className="border-slate-600 my-6" />
          ),
          
          // Strong/Bold
          strong: ({ children }) => (
            <strong className="font-semibold text-slate-100">
              {children}
            </strong>
          ),
          
          // Emphasis/Italic
          em: ({ children }) => (
            <em className="italic text-slate-200">
              {children}
            </em>
          ),
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
