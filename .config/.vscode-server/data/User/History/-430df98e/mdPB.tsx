import React, { useState, useEffect } from "react";
import { useAuth } from "../hooks/useAuth";
import { DocumentUploadForm } from "../components/document/DocumentUploadForm";
import { DocumentList } from "../components/document/DocumentList";
import { DocumentViewer } from "../components/document/DocumentViewer";
import { QuizList } from "../components/quiz/QuizList";
import { CreateQuizForm } from "../components/quiz/CreateQuizForm";
import { QuizQuestionManager } from "../components/quiz/QuizQuestionManager";
import { QuizPlayer } from "../components/quiz/QuizPlayer";
import { FlashcardSetList } from "../components/flashcards/FlashcardSetList";
import { CreateFlashcardSetForm } from "../components/flashcards/CreateFlashcardSetForm";
import { FlashcardManager } from "../components/flashcards/FlashcardManager";
import { Tables } from "../types/supabase";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>Header,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";

type StudyDocument = Tables<"study_documents">;

type DashboardView =
  | "dashboard-main"
  | "documents"
  | "flashcards"
  | "quizzes"
  | "view-document"
  | "manage-quiz"
  | "manage-flashcards"
  | "play-quiz";

export const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const [currentView, setCurrentView] =
    useState<DashboardView>("dashboard-main");
  const [selectedDocument, setSelectedDocument] =
    useState<StudyDocument | null>(null);
  const [selectedQuizId, setSelectedQuizId] = useState<string | null>(null);
  const [selectedQuizName, setSelectedQuizName] = useState<string | null>(null);
  const [quizToPlayId, setQuizToPlayId] = useState<string | null>(null);
  const [selectedFlashcardSetId, setSelectedFlashcardSetId] = useState<
    string | null
  >(null);
  const [selectedFlashcardSetName, setSelectedFlashcardSetName] = useState<
    string | null
  >(null);

  useEffect(() => {
    const handleOpenUploadDialog = () => {
      setCurrentView("documents");
    };
    window.addEventListener("openDocumentUploadDialog", handleOpenUploadDialog);
    return () => {
      window.removeEventListener(
        "openDocumentUploadDialog",
        handleOpenUploadDialog
      );
    };
  }, []);

  const handleSelectDocument = (document: StudyDocument) => {
    setSelectedDocument(document);
    setCurrentView("view-document");
  };

  const handleCloseViewer = () => {
    setSelectedDocument(null);
    setCurrentView("documents");
  };

  const handleGenerateQuiz = async (
    document: StudyDocument,
    numberOfQuestions: number = 5,
    customPrompt: string = ""
  ) => {
    if (!user) {
      alert("You must be logged in to generate a quiz.");
      return;
    }
    const quizName = `Quiz for ${document.file_name}`;
    const quizDescription = `Generated from document: ${document.file_name}`;

    try {
      const response = await fetch('/api/quizzes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: JSON.stringify({
          name: quizName,
          description: quizDescription,
          study_document_id: document.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to create quiz');
      }

      const data = await response.json();

      if (data?.id) {
        setSelectedQuizId(data.id);
        setSelectedQuizName(quizName);
        setCurrentView("manage-quiz");
        alert(`Quiz "${quizName}" created successfully!`);
      }
    } catch (err: any) {
      console.error("Error creating quiz from document:", err);
      alert(`Failed to create quiz: ${err.message}`);
    }
  };

  const handleGenerateFlashcards = async (
    document: StudyDocument,
    numberOfCards: number = 10,
    customPrompt: string = ""
  ) => {
    if (!user) {
      alert("You must be logged in to generate flashcards.");
      return;
    }
    const flashcardSetName = `Flashcards for ${document.file_name}`;
    const flashcardSetDescription = `Generated from document: ${document.file_name}`;

    try {
      const response = await fetch('/api/flashcard-sets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: JSON.stringify({
          name: flashcardSetName,
          description: flashcardSetDescription,
          study_document_id: document.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to create flashcard set');
      }

      const data = await response.json();

      if (data?.id) {
        setSelectedFlashcardSetId(data.id);
        setSelectedFlashcardSetName(flashcardSetName);
        setCurrentView("manage-flashcards");
        alert(`Flashcard set "${flashcardSetName}" created successfully!`);
      }
    } catch (err: any) {
      console.error("Error creating flashcard set from document:", err);
      alert(`Failed to create flashcard set: ${err.message}`);
    }
  };

  const handleSelectQuiz = (quizId: string, quizName: string) => {
    setSelectedQuizId(quizId);
    setSelectedQuizName(quizName);
    setCurrentView("manage-quiz");
  };

  const handlePlayQuiz = (quizId: string) => {
    setQuizToPlayId(quizId);
    setCurrentView("play-quiz");
  };

  const handleExitQuizPlayer = () => {
    setQuizToPlayId(null);
    setCurrentView("quizzes");
  };

  const handleCloseQuizManager = () => {
    setSelectedQuizId(null);
    setSelectedQuizName(null);
    setCurrentView("quizzes");
  };

  const handleFlashcardSetSelected = (setId: string, setName: string) => {
    setSelectedFlashcardSetId(setId);
    setSelectedFlashcardSetName(setName);
    setCurrentView("manage-flashcards");
  };

  const handleFlashcardSetCreated = (setId: string, setName: string) => {
    setSelectedFlashcardSetId(setId);
    setSelectedFlashcardSetName(setName);
    setCurrentView("manage-flashcards");
  };

  const handleCloseFlashcardManager = () => {
    setSelectedFlashcardSetId(null);
    setSelectedFlashcardSetName(null);
    setCurrentView("flashcards");
  };

  const handleQuizCreated = (quizId: string, quizName?: string) => {
    const nameToUse = quizName || "Newly Created Quiz";
    setSelectedQuizId(quizId);
    setSelectedQuizName(nameToUse);
    setCurrentView("manage-quiz");
    alert(
      `Quiz "${nameToUse}" created successfully! You can now add questions.`
    );
  };

  const renderContent = () => {
    const cardBaseClass = "shadow-lg rounded-lg";
    const cardTitleClass = "text-2xl font-semibold text-card-foreground mb-2";

    switch (currentView) {
      case "documents":
        return (
          <Card className={cardBaseClass}>
            <CardHeader>
              <CardTitle className={cardTitleClass}>
                My Study Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <DocumentUploadForm />
              <DocumentList
                onSelectDocument={handleSelectDocument}
                onGenerateQuiz={handleGenerateQuiz}
                onGenerateFlashcards={handleGenerateFlashcards}
              />
            </CardContent>
          </Card>
        );
      case "view-document":
        return selectedDocument ? (
          <DocumentViewer
            document={selectedDocument}
            onClose={handleCloseViewer}
          />
        ) : (
          <Card className={`${cardBaseClass} text-center`}>
            <CardHeader>
              <CardTitle className={cardTitleClass}>Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Document not found or no longer selected.
              </p>
              <Button
                variant="link"
                onClick={() => setCurrentView("documents")}
              >
                Back to Documents
              </Button>
            </CardContent>
          </Card>
        );
      case "flashcards":
        return (
          <Card className={cardBaseClass}>
            <CardHeader>
              <CardTitle className={cardTitleClass}>
                My Flashcard Sets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CreateFlashcardSetForm
                onSetCreated={handleFlashcardSetCreated}
              />
              <FlashcardSetList onSelectSet={handleFlashcardSetSelected} />
            </CardContent>
          </Card>
        );
      case "manage-flashcards":
        return selectedFlashcardSetId && selectedFlashcardSetName ? (
          <Card className={cardBaseClass}>
            <FlashcardManager
              selectedSetId={selectedFlashcardSetId}
              selectedSetName={selectedFlashcardSetName}
              onClose={handleCloseFlashcardManager}
            />
          </Card>
        ) : (
          <Card className={`${cardBaseClass} text-center`}>
            <CardHeader>
              <CardTitle className={cardTitleClass}>Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Flashcard set not found or no longer selected.
              </p>
              <Button
                variant="link"
                onClick={() => setCurrentView("flashcards")}
              >
                Back to Flashcard Sets
              </Button>
            </CardContent>
          </Card>
        );
      case "quizzes":
        return (
          <Card className={cardBaseClass}>
            <CardHeader>
              <CardTitle className={cardTitleClass}>My Quizzes</CardTitle>
            </CardHeader>
            <CardContent>
              <CreateQuizForm
                studyDocumentId={selectedDocument?.id}
                onQuizCreated={handleQuizCreated}
              />
              <QuizList
                onSelectQuiz={handleSelectQuiz}
                onPlayQuiz={handlePlayQuiz}
              />
            </CardContent>
          </Card>
        );
      case "manage-quiz":
        return selectedQuizId && selectedQuizName ? (
          <Card className={cardBaseClass}>
            <QuizQuestionManager
              selectedQuizId={selectedQuizId}
              selectedQuizName={selectedQuizName}
              studyDocumentId={selectedDocument?.id}
              onClose={handleCloseQuizManager}
            />
          </Card>
        ) : (
          <Card className={`${cardBaseClass} text-center`}>
            <CardHeader>
              <CardTitle className={cardTitleClass}>Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Quiz not found or no longer selected.
              </p>
              <Button variant="link" onClick={() => setCurrentView("quizzes")}>
                Back to Quizzes
              </Button>
            </CardContent>
          </Card>
        );
      case "play-quiz":
        return quizToPlayId ? (
          <Card className={cardBaseClass}>
            <QuizPlayer quizId={quizToPlayId} onExit={handleExitQuizPlayer} />
          </Card>
        ) : (
          <Card className={`${cardBaseClass} text-center`}>
            <CardHeader>
              <CardTitle className={cardTitleClass}>Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                No quiz selected to play.
              </p>
              <Button variant="link" onClick={() => setCurrentView("quizzes")}>
                Back to Quizzes
              </Button>
            </CardContent>
          </Card>
        );
      default:
        setCurrentView("dashboard-main");
        return null;
    }
  };

  if (currentView === "dashboard-main") {
    return (
      <div className="animate-fade-in-down space-y-8">
        <div className="text-center md:text-left">
          <h1 className="text-3xl md:text-4xl font-bold text-foreground">
            Welcome, {user?.user_metadata?.full_name || user?.email}!
          </h1>
          <p className="text-lg text-muted-foreground mt-1">
            Let's make studying more effective!
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl font-semibold text-card-foreground flex items-center">
                <span className="material-icons mr-2 text-primary">
                  description
                </span>
                Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-card-foreground">5</p>
              <p className="text-sm text-muted-foreground">Total uploaded</p>
              <Button
                onClick={() => setCurrentView("documents")}
                className="w-full mt-4"
                variant="outline"
              >
                Manage Documents
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl font-semibold text-card-foreground flex items-center">
                <span className="material-icons mr-2 text-primary">style</span>
                Flashcards
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-card-foreground">30</p>
              <p className="text-sm text-muted-foreground">
                Created from your documents
              </p>
              <Button
                onClick={() => setCurrentView("flashcards")}
                className="w-full mt-4"
                variant="outline"
              >
                Manage Flashcards
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl font-semibold text-card-foreground flex items-center">
                <span className="material-icons mr-2 text-primary">quiz</span>
                Quizzes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-card-foreground">0</p>
              <p className="text-sm text-muted-foreground">
                Ready to test your knowledge
              </p>
              <Button
                onClick={() => setCurrentView("quizzes")}
                className="w-full mt-4"
                variant="outline"
              >
                Manage Quizzes
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8">
          <DocumentUploadForm />
        </div>

        <div className="mt-8">
          <h2 className="text-2xl font-semibold text-foreground mb-4">
            Study Materials
          </h2>
          <p className="text-muted-foreground">
            (Content for Flashcards, Quizzes, Summaries, Documents tabs will go
            here, similar to the image lower section)
          </p>
        </div>
      </div>
    );
  }

  return <div className="w-full animate-fade-in">{renderContent()}</div>;
};
