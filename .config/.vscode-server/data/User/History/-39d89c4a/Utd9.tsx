import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  exportFlashcardsAsJSON,
  exportFlashcardsAsCSV,
  exportQuizzesAsJSON,
  exportQuizzesAsCSV,
} from "@/lib/storage";
import { useToast } from "@/hooks/use-toast";
import {
  Download,
  FileJson,
  FileSpreadsheet,
  Info,
  Layers,
  FlaskConical,
  ExternalLink,
} from "lucide-react";

const ExportSection: React.FC = () => {
  const { toast } = useToast();

  const commonCardClasses =
    "bg-slate-800 border-slate-700 text-slate-100 shadow-md";
  const textPrimaryClass = "text-slate-50"; // Lighter for primary titles/text
  const textSecondaryClass = "text-slate-300"; // Slightly dimmer for less emphasis
  const textMutedClass = "text-slate-400"; // For muted/description text
  const accentColor = "purple-400";
  const accentTextClass = `text-${accentColor}`;
  const accentBorderClass = `border-${accentColor}`;
  const infoBoxBgClass = "bg-sky-900/40"; // Using sky for info to differentiate from purple accent
  const infoBoxBorderClass = "border-sky-700";
  const infoBoxTextClass = "text-sky-300";
  const infoBoxIconClass = "text-sky-400";

  const handleExport = async (
    exportFn: () => Promise<string>,
    filename: string
  ) => {
    try {
      const data = await exportFn();
      const blob = new Blob([data], {
        type: filename.endsWith(".csv") ? "text/csv" : "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);
      toast({
        title: "Export Successful",
        description: `Your data has been exported to ${filename}`,
        className: "bg-slate-700 border-slate-600 text-slate-200",
      });
    } catch (error) {
      console.error("Export failed:", error);
      toast({
        title: "Export Failed",
        description: "There was an error exporting your data.",
        variant: "destructive", // This will use the destructive colors from theme
      });
    }
  };

  const exportItems = [
    {
      id: "flashcards-json",
      title: "Flashcards (JSON)",
      description: "Export all your flashcards with review history.",
      icon: FileJson,
      format: "JSON",
      disabled: false,
      action: () =>
        handleExport(exportFlashcardsAsJSON, "chewyai-flashcards.json"),
    },
    {
      id: "flashcards-csv",
      title: "Flashcards (CSV)",
      description: "Compatible with Anki and other SRS tools.",
      icon: FileSpreadsheet,
      format: "CSV",
      disabled: false,
      action: () =>
        handleExport(exportFlashcardsAsCSV, "chewyai-flashcards.csv"),
    },
    {
      id: "all-data-json",
      title: "All Data (JSON)",
      description: "Complete backup of all your study materials.",
      icon: Layers,
      format: "JSON",
      disabled: false,
      action: () =>
        handleExport(exportFlashcardsAsJSON, "chewyai-all-data.json"), // Assuming all data is also JSON for now
    },
    {
      id: "quizzes-json",
      title: "Quizzes (JSON)",
      description: "Export all your practice quizzes with SRS data.",
      icon: FlaskConical,
      format: "JSON",
      disabled: false,
      action: () => handleExport(exportQuizzesAsJSON, "chewyai-quizzes.json"),
    },
    {
      id: "quizzes-csv",
      title: "Quizzes (CSV)",
      description: "Compatible with spreadsheet applications.",
      icon: FileSpreadsheet,
      format: "CSV",
      disabled: false,
      action: () => handleExport(exportQuizzesAsCSV, "chewyai-quizzes.csv"),
    },
  ];

  return (
    <Card className={`${commonCardClasses} w-full`}>
      <CardHeader className="border-b border-slate-700">
        <CardTitle
          className={`flex items-center gap-2 text-xl ${textSecondaryClass}`}
        >
          <Download className={`h-5 w-5 ${accentTextClass}`} /> Export Data
        </CardTitle>
      </CardHeader>

      <CardContent className="pt-6 space-y-6">
        <p className={`${textMutedClass} text-sm`}>
          Export your study materials in various formats to use them anywhere or
          create backups.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {exportItems.map((item) => (
            <div
              key={item.id}
              className={`border border-slate-700 rounded-lg p-4 transition-colors duration-150 
                          ${
                            item.disabled
                              ? "opacity-50 cursor-not-allowed bg-slate-800/50"
                              : "hover:border-purple-500 hover:bg-slate-700/50 cursor-pointer"
                          }`}
              onClick={!item.disabled ? item.action : undefined}
              role={item.disabled ? undefined : "button"}
              tabIndex={item.disabled ? -1 : 0}
              onKeyDown={(e) => {
                if (!item.disabled && (e.key === "Enter" || e.key === " ")) {
                  item.action();
                }
              }}
            >
              <div className="flex items-center justify-between mb-2">
                <item.icon className={`${accentTextClass} h-6 w-6`} />
                <span
                  className={`text-xs font-medium ${textMutedClass} bg-slate-700 px-2 py-0.5 rounded`}
                >
                  {item.format}
                </span>
              </div>
              <h3 className={`font-medium ${textSecondaryClass} mb-1`}>
                {item.title}
              </h3>
              <p className={`${textMutedClass} text-sm`}>{item.description}</p>
            </div>
          ))}
        </div>

        <div
          className={`${infoBoxBgClass} p-4 rounded-md ${infoBoxBorderClass} border`}
        >
          <div className="flex items-start gap-3">
            <Info
              className={`${infoBoxIconClass} h-5 w-5 mt-0.5 flex-shrink-0`}
            />
            <p className={`${infoBoxTextClass} text-sm`}>
              Your study data is stored in your browser. Exporting creates a
              backup you can use to restore your data or transfer it to another
              device. We recommend creating backups regularly.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ExportSection;
