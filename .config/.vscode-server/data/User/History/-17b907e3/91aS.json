{"mcpServers": {}, "workbench.colorCustomizations": {}, "codeium.enableConfig": {"*": false}, "mcp": {"servers": {}}, "explorer.confirmDelete": false, "[csharp]": {"editor.defaultFormatter": "csharpier.csharpier-vscode"}, "terminal.integrated.defaultProfile.windows": "<PERSON><PERSON>", "chat.editing.confirmEditRequestRetry": false, "roo-cline.allowedCommands": ["npm test", "npm install", "tsc", "git log", "git diff", "git show", "dotnet", "mkdir", "touch", "mv", "cp", "del", "rm"], "files.autoSave": "after<PERSON>elay", "cline.modelSettings.o3Mini.reasoningEffort": "high", "cline.vsCodeLmModelSelector": {}, "terminal.explorerKind": "both", "terminal.external.windowsExec": "C:\\Program Files\\Git\\usr\\bin\\bash.exe", "security.promptForRemoteFileProtocolHandling": false, "remote.SSH.remotePlatform": {"project-parlay-replit": "linux", "304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev": "linux"}, "terminal.integrated.enableMultiLinePasteWarning": "never", "git.autofetch": true, "git.confirmSync": false, "chat.editing.confirmEditRequestRemoval": false, "workbench.iconTheme": "material-icon-theme", "workbench.colorTheme": "Default Light Modern", "explorer.confirmDragAndDrop": false}