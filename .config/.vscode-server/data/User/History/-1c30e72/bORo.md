# ChewyAI Documentation

Welcome to the ChewyAI documentation. This folder contains comprehensive documentation for the ChewyAI full-stack application.

## 📁 Documentation Structure

- **[RULES.md](./RULES.md)** - Development rules, coding standards, and best practices
- **[MEMORIES.md](./MEMORIES.md)** - Important system decisions and architectural choices
- **[DEPLOYMENT.md](./DEPLOYMENT.md)** - Production deployment guide and configuration
- **[SECURITY.md](./SECURITY.md)** - Security practices and API key management
- **[API.md](./API.md)** - API documentation and endpoint reference

## 🚀 Quick Start

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm run start
```

### Testing Build
```bash
npm run test:build
```

## 🏗️ Architecture Overview

ChewyAI is a full-stack application with:

- **Frontend**: React + Vite + TypeScript + Tailwindcss
- **Backend**: Express.js + Node.js
- **Database**: Supabase (PostgreSQL)
- **AI Integration**: OpenRouter API (user-configured)
- **Deployment**: Replit (production-ready)

## 🔐 Security

- All API keys are handled ephemerally by the backend
- No sensitive credentials are exposed to the client
- Proper CORS and security headers implemented
- Environment variables used for all configuration

## 📝 Contributing

Please read [RULES.md](./RULES.md) before contributing to understand our development standards and practices.
