"use strict";(()=>{var J,_,be,sn,P,ge,we,te,xe,B={},ke=[],an=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,ie=Array.isArray;function I(e,n){for(var t in n)e[t]=n[t];return e}function Te(e){var n=e.parentNode;n&&n.removeChild(e)}function s(e,n,t){var o,r,i,l={};for(i in n)i=="key"?o=n[i]:i=="ref"?r=n[i]:l[i]=n[i];if(arguments.length>2&&(l.children=arguments.length>3?J.call(arguments,2):t),typeof e=="function"&&e.defaultProps!=null)for(i in e.defaultProps)l[i]===void 0&&(l[i]=e.defaultProps[i]);return q(e,l,o,r,null)}function q(e,n,t,o,r){var i={type:e,props:n,key:t,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:r??++be,__i:-1,__u:0};return r==null&&_.vnode!=null&&_.vnode(i),i}function k(e){return e.children}function $(e,n){this.props=e,this.context=n}function O(e,n){if(n==null)return e.__?O(e.__,e.__i+1):null;for(var t;n<e.__k.length;n++)if((t=e.__k[n])!=null&&t.__e!=null)return t.__e;return typeof e.type=="function"?O(e):null}function Le(e){var n,t;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,n=0;n<e.__k.length;n++)if((t=e.__k[n])!=null&&t.__e!=null){e.__e=e.__c.base=t.__e;break}return Le(e)}}function oe(e){(!e.__d&&(e.__d=!0)&&P.push(e)&&!j.__r++||ge!==_.debounceRendering)&&((ge=_.debounceRendering)||we)(j)}function j(){var e,n,t,o,r,i,l,u,c;for(P.sort(te);e=P.shift();)e.__d&&(n=P.length,o=void 0,i=(r=(t=e).__v).__e,u=[],c=[],(l=t.__P)&&((o=I({},r)).__v=r.__v+1,_.vnode&&_.vnode(o),se(l,o,r,t.__n,l.ownerSVGElement!==void 0,32&r.__u?[i]:null,u,i??O(r),!!(32&r.__u),c),o.__.__k[o.__i]=o,Fe(u,o,c),o.__e!=i&&Le(o)),P.length>n&&P.sort(te));j.__r=0}function Ce(e,n,t,o,r,i,l,u,c,d,p){var a,m,f,g,x,y=o&&o.__k||ke,v=n.length;for(t.__d=c,un(t,n,y),c=t.__d,a=0;a<v;a++)(f=t.__k[a])!=null&&typeof f!="boolean"&&typeof f!="function"&&(m=f.__i===-1?B:y[f.__i]||B,f.__i=a,se(e,f,m,r,i,l,u,c,d,p),g=f.__e,f.ref&&m.ref!=f.ref&&(m.ref&&ae(m.ref,null,f),p.push(f.ref,f.__c||g,f)),x==null&&g!=null&&(x=g),65536&f.__u||m.__k===f.__k?c=Se(f,c,e):typeof f.type=="function"&&f.__d!==void 0?c=f.__d:g&&(c=g.nextSibling),f.__d=void 0,f.__u&=-196609);t.__d=c,t.__e=x}function un(e,n,t){var o,r,i,l,u,c=n.length,d=t.length,p=d,a=0;for(e.__k=[],o=0;o<c;o++)(r=e.__k[o]=(r=n[o])==null||typeof r=="boolean"||typeof r=="function"?null:typeof r=="string"||typeof r=="number"||typeof r=="bigint"||r.constructor==String?q(null,r,null,null,r):ie(r)?q(k,{children:r},null,null,null):r.constructor===void 0&&r.__b>0?q(r.type,r.props,r.key,r.ref?r.ref:null,r.__v):r)!=null?(r.__=e,r.__b=e.__b+1,u=ln(r,t,l=o+a,p),r.__i=u,i=null,u!==-1&&(p--,(i=t[u])&&(i.__u|=131072)),i==null||i.__v===null?(u==-1&&a--,typeof r.type!="function"&&(r.__u|=65536)):u!==l&&(u===l+1?a++:u>l?p>c-l?a+=u-l:a--:a=u<l&&u==l-1?u-l:0,u!==o+a&&(r.__u|=65536))):(i=t[o])&&i.key==null&&i.__e&&(i.__e==e.__d&&(e.__d=O(i)),re(i,i,!1),t[o]=null,p--);if(p)for(o=0;o<d;o++)(i=t[o])!=null&&(131072&i.__u)==0&&(i.__e==e.__d&&(e.__d=O(i)),re(i,i))}function Se(e,n,t){var o,r;if(typeof e.type=="function"){for(o=e.__k,r=0;o&&r<o.length;r++)o[r]&&(o[r].__=e,n=Se(o[r],n,t));return n}return e.__e!=n&&(t.insertBefore(e.__e,n||null),n=e.__e),n&&n.nextSibling}function ln(e,n,t,o){var r=e.key,i=e.type,l=t-1,u=t+1,c=n[t];if(c===null||c&&r==c.key&&i===c.type)return t;if(o>(c!=null&&(131072&c.__u)==0?1:0))for(;l>=0||u<n.length;){if(l>=0){if((c=n[l])&&(131072&c.__u)==0&&r==c.key&&i===c.type)return l;l--}if(u<n.length){if((c=n[u])&&(131072&c.__u)==0&&r==c.key&&i===c.type)return u;u++}}return-1}function ve(e,n,t){n[0]==="-"?e.setProperty(n,t??""):e[n]=t==null?"":typeof t!="number"||an.test(n)?t:t+"px"}function z(e,n,t,o,r){var i;e:if(n==="style")if(typeof t=="string")e.style.cssText=t;else{if(typeof o=="string"&&(e.style.cssText=o=""),o)for(n in o)t&&n in t||ve(e.style,n,"");if(t)for(n in t)o&&t[n]===o[n]||ve(e.style,n,t[n])}else if(n[0]==="o"&&n[1]==="n")i=n!==(n=n.replace(/(PointerCapture)$|Capture$/,"$1")),n=n.toLowerCase()in e?n.toLowerCase().slice(2):n.slice(2),e.l||(e.l={}),e.l[n+i]=t,t?o?t.u=o.u:(t.u=Date.now(),e.addEventListener(n,i?he:ye,i)):e.removeEventListener(n,i?he:ye,i);else{if(r)n=n.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(n!=="width"&&n!=="height"&&n!=="href"&&n!=="list"&&n!=="form"&&n!=="tabIndex"&&n!=="download"&&n!=="rowSpan"&&n!=="colSpan"&&n!=="role"&&n in e)try{e[n]=t??"";break e}catch{}typeof t=="function"||(t==null||t===!1&&n[4]!=="-"?e.removeAttribute(n):e.setAttribute(n,t))}}function ye(e){var n=this.l[e.type+!1];if(e.t){if(e.t<=n.u)return}else e.t=Date.now();return n(_.event?_.event(e):e)}function he(e){return this.l[e.type+!0](_.event?_.event(e):e)}function se(e,n,t,o,r,i,l,u,c,d){var p,a,m,f,g,x,y,v,w,S,b,L,D,F,R,T=n.type;if(n.constructor!==void 0)return null;128&t.__u&&(c=!!(32&t.__u),i=[u=n.__e=t.__e]),(p=_.__b)&&p(n);e:if(typeof T=="function")try{if(v=n.props,w=(p=T.contextType)&&o[p.__c],S=p?w?w.props.value:p.__:o,t.__c?y=(a=n.__c=t.__c).__=a.__E:("prototype"in T&&T.prototype.render?n.__c=a=new T(v,S):(n.__c=a=new $(v,S),a.constructor=T,a.render=dn),w&&w.sub(a),a.props=v,a.state||(a.state={}),a.context=S,a.__n=o,m=a.__d=!0,a.__h=[],a._sb=[]),a.__s==null&&(a.__s=a.state),T.getDerivedStateFromProps!=null&&(a.__s==a.state&&(a.__s=I({},a.__s)),I(a.__s,T.getDerivedStateFromProps(v,a.__s))),f=a.props,g=a.state,a.__v=n,m)T.getDerivedStateFromProps==null&&a.componentWillMount!=null&&a.componentWillMount(),a.componentDidMount!=null&&a.__h.push(a.componentDidMount);else{if(T.getDerivedStateFromProps==null&&v!==f&&a.componentWillReceiveProps!=null&&a.componentWillReceiveProps(v,S),!a.__e&&(a.shouldComponentUpdate!=null&&a.shouldComponentUpdate(v,a.__s,S)===!1||n.__v===t.__v)){for(n.__v!==t.__v&&(a.props=v,a.state=a.__s,a.__d=!1),n.__e=t.__e,n.__k=t.__k,n.__k.forEach(function(E){E&&(E.__=n)}),b=0;b<a._sb.length;b++)a.__h.push(a._sb[b]);a._sb=[],a.__h.length&&l.push(a);break e}a.componentWillUpdate!=null&&a.componentWillUpdate(v,a.__s,S),a.componentDidUpdate!=null&&a.__h.push(function(){a.componentDidUpdate(f,g,x)})}if(a.context=S,a.props=v,a.__P=e,a.__e=!1,L=_.__r,D=0,"prototype"in T&&T.prototype.render){for(a.state=a.__s,a.__d=!1,L&&L(n),p=a.render(a.props,a.state,a.context),F=0;F<a._sb.length;F++)a.__h.push(a._sb[F]);a._sb=[]}else do a.__d=!1,L&&L(n),p=a.render(a.props,a.state,a.context),a.state=a.__s;while(a.__d&&++D<25);a.state=a.__s,a.getChildContext!=null&&(o=I(I({},o),a.getChildContext())),m||a.getSnapshotBeforeUpdate==null||(x=a.getSnapshotBeforeUpdate(f,g)),Ce(e,ie(R=p!=null&&p.type===k&&p.key==null?p.props.children:p)?R:[R],n,t,o,r,i,l,u,c,d),a.base=n.__e,n.__u&=-161,a.__h.length&&l.push(a),y&&(a.__E=a.__=null)}catch(E){n.__v=null,c||i!=null?(n.__e=u,n.__u|=c?160:32,i[i.indexOf(u)]=null):(n.__e=t.__e,n.__k=t.__k),_.__e(E,n,t)}else i==null&&n.__v===t.__v?(n.__k=t.__k,n.__e=t.__e):n.__e=cn(t.__e,n,t,o,r,i,l,c,d);(p=_.diffed)&&p(n)}function Fe(e,n,t){n.__d=void 0;for(var o=0;o<t.length;o++)ae(t[o],t[++o],t[++o]);_.__c&&_.__c(n,e),e.some(function(r){try{e=r.__h,r.__h=[],e.some(function(i){i.call(r)})}catch(i){_.__e(i,r.__v)}})}function cn(e,n,t,o,r,i,l,u,c){var d,p,a,m,f,g,x,y=t.props,v=n.props,w=n.type;if(w==="svg"&&(r=!0),i!=null){for(d=0;d<i.length;d++)if((f=i[d])&&"setAttribute"in f==!!w&&(w?f.localName===w:f.nodeType===3)){e=f,i[d]=null;break}}if(e==null){if(w===null)return document.createTextNode(v);e=r?document.createElementNS("http://www.w3.org/2000/svg",w):document.createElement(w,v.is&&v),i=null,u=!1}if(w===null)y===v||u&&e.data===v||(e.data=v);else{if(i=i&&J.call(e.childNodes),y=t.props||B,!u&&i!=null)for(y={},d=0;d<e.attributes.length;d++)y[(f=e.attributes[d]).name]=f.value;for(d in y)f=y[d],d=="children"||(d=="dangerouslySetInnerHTML"?a=f:d==="key"||d in v||z(e,d,null,f,r));for(d in v)f=v[d],d=="children"?m=f:d=="dangerouslySetInnerHTML"?p=f:d=="value"?g=f:d=="checked"?x=f:d==="key"||u&&typeof f!="function"||y[d]===f||z(e,d,f,y[d],r);if(p)u||a&&(p.__html===a.__html||p.__html===e.innerHTML)||(e.innerHTML=p.__html),n.__k=[];else if(a&&(e.innerHTML=""),Ce(e,ie(m)?m:[m],n,t,o,r&&w!=="foreignObject",i,l,i?i[0]:t.__k&&O(t,0),u,c),i!=null)for(d=i.length;d--;)i[d]!=null&&Te(i[d]);u||(d="value",g!==void 0&&(g!==e[d]||w==="progress"&&!g||w==="option"&&g!==y[d])&&z(e,d,g,y[d],!1),d="checked",x!==void 0&&x!==e[d]&&z(e,d,x,y[d],!1))}return e}function ae(e,n,t){try{typeof e=="function"?e(n):e.current=n}catch(o){_.__e(o,t)}}function re(e,n,t){var o,r;if(_.unmount&&_.unmount(e),(o=e.ref)&&(o.current&&o.current!==e.__e||ae(o,null,n)),(o=e.__c)!=null){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(i){_.__e(i,n)}o.base=o.__P=null,e.__c=void 0}if(o=e.__k)for(r=0;r<o.length;r++)o[r]&&re(o[r],n,t||typeof e.type!="function");t||e.__e==null||Te(e.__e),e.__=e.__e=e.__d=void 0}function dn(e,n,t){return this.constructor(e,t)}function ue(e,n,t){var o,r,i,l;_.__&&_.__(e,n),r=(o=typeof t=="function")?null:t&&t.__k||n.__k,i=[],l=[],se(n,e=(!o&&t||n).__k=s(k,null,[e]),r||B,B,n.ownerSVGElement!==void 0,!o&&t?[t]:r?null:n.firstChild?J.call(n.childNodes):null,i,!o&&t?t:r?r.__e:n.firstChild,o,l),Fe(i,e,l)}function Ne(e,n){var t={__c:n="__cC"+xe++,__:e,Consumer:function(o,r){return o.children(r)},Provider:function(o){var r,i;return this.getChildContext||(r=[],(i={})[n]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(l){this.props.value!==l.value&&r.some(function(u){u.__e=!0,oe(u)})},this.sub=function(l){r.push(l);var u=l.componentWillUnmount;l.componentWillUnmount=function(){r.splice(r.indexOf(l),1),u&&u.call(l)}}),o.children}};return t.Provider.__=t.Consumer.contextType=t}J=ke.slice,_={__e:function(e,n,t,o){for(var r,i,l;n=n.__;)if((r=n.__c)&&!r.__)try{if((i=r.constructor)&&i.getDerivedStateFromError!=null&&(r.setState(i.getDerivedStateFromError(e)),l=r.__d),r.componentDidCatch!=null&&(r.componentDidCatch(e,o||{}),l=r.__d),l)return r.__E=r}catch(u){e=u}throw e}},be=0,sn=function(e){return e!=null&&e.constructor==null},$.prototype.setState=function(e,n){var t;t=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=I({},this.state),typeof e=="function"&&(e=e(I({},t),this.props)),e&&I(t,e),e!=null&&this.__v&&(n&&this._sb.push(n),oe(this))},$.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),oe(this))},$.prototype.render=k,P=[],we=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,te=function(e,n){return e.__v.__b-n.__v.__b},j.__r=0,xe=0;function N(){}N.prototype={diff:function(n,t){var o,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=r.callback;typeof r=="function"&&(i=r,r={}),this.options=r;var l=this;function u(b){return i?(setTimeout(function(){i(void 0,b)},0),!0):b}n=this.castInput(n),t=this.castInput(t),n=this.removeEmpty(this.tokenize(n)),t=this.removeEmpty(this.tokenize(t));var c=t.length,d=n.length,p=1,a=c+d;r.maxEditLength&&(a=Math.min(a,r.maxEditLength));var m=(o=r.timeout)!==null&&o!==void 0?o:1/0,f=Date.now()+m,g=[{oldPos:-1,lastComponent:void 0}],x=this.extractCommon(g[0],t,n,0);if(g[0].oldPos+1>=d&&x+1>=c)return u([{value:this.join(t),count:t.length}]);var y=-1/0,v=1/0;function w(){for(var b=Math.max(y,-p);b<=Math.min(v,p);b+=2){var L=void 0,D=g[b-1],F=g[b+1];D&&(g[b-1]=void 0);var R=!1;if(F){var T=F.oldPos-b;R=F&&0<=T&&T<c}var E=D&&D.oldPos+1<d;if(!R&&!E){g[b]=void 0;continue}if(!E||R&&D.oldPos+1<F.oldPos?L=l.addToPath(F,!0,void 0,0):L=l.addToPath(D,void 0,!0,1),x=l.extractCommon(L,t,n,b),L.oldPos+1>=d&&x+1>=c)return u(fn(l,L.lastComponent,t,n,l.useLongestToken));g[b]=L,L.oldPos+1>=d&&(v=Math.min(v,b-1)),x+1>=c&&(y=Math.max(y,b+1))}p++}if(i)(function b(){setTimeout(function(){if(p>a||Date.now()>f)return i();w()||b()},0)})();else for(;p<=a&&Date.now()<=f;){var S=w();if(S)return S}},addToPath:function(n,t,o,r){var i=n.lastComponent;return i&&i.added===t&&i.removed===o?{oldPos:n.oldPos+r,lastComponent:{count:i.count+1,added:t,removed:o,previousComponent:i.previousComponent}}:{oldPos:n.oldPos+r,lastComponent:{count:1,added:t,removed:o,previousComponent:i}}},extractCommon:function(n,t,o,r){for(var i=t.length,l=o.length,u=n.oldPos,c=u-r,d=0;c+1<i&&u+1<l&&this.equals(t[c+1],o[u+1]);)c++,u++,d++;return d&&(n.lastComponent={count:d,previousComponent:n.lastComponent}),n.oldPos=u,c},equals:function(n,t){return this.options.comparator?this.options.comparator(n,t):n===t||this.options.ignoreCase&&n.toLowerCase()===t.toLowerCase()},removeEmpty:function(n){for(var t=[],o=0;o<n.length;o++)n[o]&&t.push(n[o]);return t},castInput:function(n){return n},tokenize:function(n){return n.split("")},join:function(n){return n.join("")}};function fn(e,n,t,o,r){for(var i=[],l;n;)i.push(n),l=n.previousComponent,delete n.previousComponent,n=l;i.reverse();for(var u=0,c=i.length,d=0,p=0;u<c;u++){var a=i[u];if(a.removed){if(a.value=e.join(o.slice(p,p+a.count)),p+=a.count,u&&i[u-1].added){var f=i[u-1];i[u-1]=i[u],i[u]=f}}else{if(!a.added&&r){var m=t.slice(d,d+a.count);m=m.map(function(x,y){var v=o[p+y];return v.length>x.length?v:x}),a.value=e.join(m)}else a.value=e.join(t.slice(d,d+a.count));d+=a.count,a.added||(p+=a.count)}}var g=i[c-1];return c>1&&typeof g.value=="string"&&(g.added||g.removed)&&e.equals("",g.value)&&(i[c-2].value+=g.value,i.pop()),i}var Un=new N;var De=/^[A-Za-z\xC0-\u02C6\u02C8-\u02D7\u02DE-\u02FF\u1E00-\u1EFF]+$/,Ie=/\S/,He=new N;He.equals=function(e,n){return this.options.ignoreCase&&(e=e.toLowerCase(),n=n.toLowerCase()),e===n||this.options.ignoreWhitespace&&!Ie.test(e)&&!Ie.test(n)};He.tokenize=function(e){for(var n=e.split(/([^\S\r\n]+|[()[\]{}'"\r\n]|\b)/),t=0;t<n.length-1;t++)!n[t+1]&&n[t+2]&&De.test(n[t])&&De.test(n[t+2])&&(n[t]+=n[t+2],n.splice(t+1,2),t--);return n};var Re=new N;Re.tokenize=function(e){this.options.stripTrailingCr&&(e=e.replace(/\r\n/g,`
`));var n=[],t=e.split(/(\n|\r\n)/);t[t.length-1]||t.pop();for(var o=0;o<t.length;o++){var r=t[o];o%2&&!this.options.newlineIsToken?n[n.length-1]+=r:(this.options.ignoreWhitespace&&(r=r.trim()),n.push(r))}return n};var pn=new N;pn.tokenize=function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)};var _n=new N;_n.tokenize=function(e){return e.split(/([{}:;,]|\s+)/)};function K(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?K=function(n){return typeof n}:K=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},K(e)}var mn=Object.prototype.toString,W=new N;W.useLongestToken=!0;W.tokenize=Re.tokenize;W.castInput=function(e){var n=this.options,t=n.undefinedReplacement,o=n.stringifyReplacer,r=o===void 0?function(i,l){return typeof l>"u"?t:l}:o;return typeof e=="string"?e:JSON.stringify(le(e,null,null,r),r,"  ")};W.equals=function(e,n){return N.prototype.equals.call(W,e.replace(/,([\r\n])/g,"$1"),n.replace(/,([\r\n])/g,"$1"))};function le(e,n,t,o,r){n=n||[],t=t||[],o&&(e=o(r,e));var i;for(i=0;i<n.length;i+=1)if(n[i]===e)return t[i];var l;if(mn.call(e)==="[object Array]"){for(n.push(e),l=new Array(e.length),t.push(l),i=0;i<e.length;i+=1)l[i]=le(e[i],n,t,o,r);return n.pop(),t.pop(),l}if(e&&e.toJSON&&(e=e.toJSON()),K(e)==="object"&&e!==null){n.push(e),l={},t.push(l);var u=[],c;for(c in e)e.hasOwnProperty(c)&&u.push(c);for(u.sort(),i=0;i<u.length;i+=1)c=u[i],l[c]=le(e[c],n,t,o,c);n.pop(),t.pop()}else l=e;return l}var G=new N;G.tokenize=function(e){return e.slice()};G.join=G.removeEmpty=function(e){return e};function Ee(e,n,t){return G.diff(e,n,t)}var U,h,ce,Pe,X=0,Ue=[],Y=[],Ae=_.__b,Me=_.__r,Oe=_.diffed,Ve=_.__c,Be=_.unmount;function fe(e,n){_.__h&&_.__h(h,e,X||n),X=0;var t=h.__H||(h.__H={__:[],__h:[]});return e>=t.__.length&&t.__.push({__V:Y}),t.__[e]}function Q(e){return X=1,gn(qe,e)}function gn(e,n,t){var o=fe(U++,2);if(o.t=e,!o.__c&&(o.__=[t?t(n):qe(void 0,n),function(u){var c=o.__N?o.__N[0]:o.__[0],d=o.t(c,u);c!==d&&(o.__N=[d,o.__[1]],o.__c.setState({}))}],o.__c=h,!h.u)){var r=function(u,c,d){if(!o.__c.__H)return!0;var p=o.__c.__H.__.filter(function(m){return m.__c});if(p.every(function(m){return!m.__N}))return!i||i.call(this,u,c,d);var a=!1;return p.forEach(function(m){if(m.__N){var f=m.__[0];m.__=m.__N,m.__N=void 0,f!==m.__[0]&&(a=!0)}}),!(!a&&o.__c.props===u)&&(!i||i.call(this,u,c,d))};h.u=!0;var i=h.shouldComponentUpdate,l=h.componentWillUpdate;h.componentWillUpdate=function(u,c,d){if(this.__e){var p=i;i=void 0,r(u,c,d),i=p}l&&l.call(this,u,c,d)},h.shouldComponentUpdate=r}return o.__N||o.__}function V(e,n){var t=fe(U++,7);return hn(t.__H,n)?(t.__V=e(),t.i=n,t.__h=e,t.__V):t.__}function A(e,n){return X=8,V(function(){return e},n)}function ze(e){var n=h.context[e.__c],t=fe(U++,9);return t.c=e,n?(t.__==null&&(t.__=!0,n.sub(h)),n.props.value):e.__}function vn(){for(var e;e=Ue.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(Z),e.__H.__h.forEach(de),e.__H.__h=[]}catch(n){e.__H.__h=[],_.__e(n,e.__v)}}_.__b=function(e){h=null,Ae&&Ae(e)},_.__r=function(e){Me&&Me(e),U=0;var n=(h=e.__c).__H;n&&(ce===h?(n.__h=[],h.__h=[],n.__.forEach(function(t){t.__N&&(t.__=t.__N),t.__V=Y,t.__N=t.i=void 0})):(n.__h.forEach(Z),n.__h.forEach(de),n.__h=[],U=0)),ce=h},_.diffed=function(e){Oe&&Oe(e);var n=e.__c;n&&n.__H&&(n.__H.__h.length&&(Ue.push(n)!==1&&Pe===_.requestAnimationFrame||((Pe=_.requestAnimationFrame)||yn)(vn)),n.__H.__.forEach(function(t){t.i&&(t.__H=t.i),t.__V!==Y&&(t.__=t.__V),t.i=void 0,t.__V=Y})),ce=h=null},_.__c=function(e,n){n.some(function(t){try{t.__h.forEach(Z),t.__h=t.__h.filter(function(o){return!o.__||de(o)})}catch(o){n.some(function(r){r.__h&&(r.__h=[])}),n=[],_.__e(o,t.__v)}}),Ve&&Ve(e,n)},_.unmount=function(e){Be&&Be(e);var n,t=e.__c;t&&t.__H&&(t.__H.__.forEach(function(o){try{Z(o)}catch(r){n=r}}),t.__H=void 0,n&&_.__e(n,t.__v))};var We=typeof requestAnimationFrame=="function";function yn(e){var n,t=function(){clearTimeout(o),We&&cancelAnimationFrame(n),setTimeout(e)},o=setTimeout(t,100);We&&(n=requestAnimationFrame(t))}function Z(e){var n=h,t=e.__c;typeof t=="function"&&(e.__c=void 0,t()),h=n}function de(e){var n=h;e.__c=e.__(),h=n}function hn(e,n){return!e||e.length!==n.length||n.some(function(t,o){return t!==e[o]})}function qe(e,n){return typeof n=="function"?n(e):n}var $e=e=>!!e;var jn=Symbol("unset");function je(e){let n=[];for(let t of e)n=n.concat(t);return n}var Jn=2**31-1;var Je=e=>function({value:t,onChange:o}){return s("div",{className:"decision-buttons"},e.map(r=>s("button",{key:r,onClick:()=>o(r),className:t===r?"active":""},r)))};var Ze="<node_internals>",Xe="node:",Qe=e=>e.config.type==="pwa-node"||e.config.type==="pwa-extensionHost"||e.config.type==="node-terminal",ee=e=>e.config.type==="pwa-chrome"||e.config.type==="pwa-msedge",pe=e=>e.absolutePath.startsWith(Ze)||e.url.startsWith(Xe)?2:e.absolutePath.includes("node_modules")?1:0,ne=(e,n)=>e.url.startsWith(Xe)?e.url:e.absolutePath.startsWith(Ze)?e.absolutePath:bn(e.absolutePath)&&n.config.__workspaceFolder?xn(n.config.__workspaceFolder,e.absolutePath):e.absolutePath||e.url,M=e=>{let n=(e.prettyName||e.url).split(/\\|\//g);return n[n.length-1]},bn=e=>en(e)||wn(e),en=e=>e.startsWith("/"),wn=e=>/^[a-z]:/i.test(e),Ke=(e,n)=>{let t=e.split("/"),o=n.split("/");for(;t.length&&o[0]===t[0];)t.shift(),o.shift();return(t.length?new Array(t.length).fill(".."):["."]).concat(o).join("/")},xn=(e,n)=>en(e)?Ke(e,n):Ke(Ge(Ye(e)),Ge(Ye(n))),Ge=e=>e.replace(/\\\//g,"/").replace(/\\/g,"/"),Ye=e=>e.slice(0,1).toUpperCase()+e.slice(1);var _e=Ne(void 0),C=()=>ze(_e);var me=acquireVsCodeApi(),kn=(e,n)=>{let t=me.getState()?.componentState||{};return t.hasOwnProperty(e)?t[e]:n},Tn=(e,n)=>{let t=me.getState();me.setState({...t,componentState:{...t?.componentState,[e]:n}})},H=(e,n)=>{let[t,o]=Q(()=>kn(e,n)),r=A(i=>{Tn(e,i),o(i)},[e,o]);return[t,r]};var nn=()=>{let e=C();return s(k,null,e.breakpoints.map((n,t)=>s(Fn,{bp:n,key:t})))},Ln=(e,n)=>e.cdp.some(t=>{if("location"in t.args)return!0;if(t.args.url){let o=t.args.url;return n.sources.some(r=>r.url===o)}if(t.args.urlRegex){let o=new RegExp(t.args.urlRegex);return n.sources.some(r=>o.test(r.url))}return!1}),Cn=(e,n)=>{let t=0,o=[s("li",{key:t++},s("p",null,"\u2705 This breakpoint was initially set in:"),s("p",null,s("code",null,e.source.path)," line ",e.params.line," column ",e.params.column||1))];if(!Ln(e,n))return o.push(s(Nn,{bp:e,key:t++})),o;o.push(s("li",{key:t++},s("p",null,"\u2705 In the runtime, the breakpoint was set in:"),s("p",null,s("ul",null,e.cdp.map((l,u)=>s(Hn,{cdp:l,index:u,key:u}))))));let r=e.cdp.filter(l=>l.state===1),i=je(r.map(l=>l.state===1?l.uiLocations:[]));return i.length?(o.push(s("li",{key:t++},s("p",null,"\u2705 The runtime acknowledged and adjusted the breakpoint, and it mapped back to the following locations:"),s("ul",null,i.map((l,u)=>s(In,{loc:l,key:u})))),s("li",{key:t++},s("p",null,"If this is not right, your compiled code might be out of date with your sources. If you don't think this is the case and something else is wrong, please"," ",s("a",{href:"https://github.com/microsoft/vscode-js-debug/issues/new/choose"},"open an issue"),"!"))),o):(o.push(s("li",{key:t++},s(Sn,null))),o)},Sn=()=>{let e=C();return s("p",null,"\u2753 We sent the breakpoint, but it didn't bind to any locations. If this is unexpected:",s("ul",null,s("li",null,"Make sure that your program is loading or running this script. You can add a"," ",s("code",null,"debugger;")," statement to check this: your program will pause when it hits it."),s("li",null,"If your breakpoint is set in certain places, such as on the last empty line of a file, the runtime might not be able to find anywhere to place it."),Qe(e)&&s("li",null,"Unless you"," ",s("a",{href:"https://code.visualstudio.com/docs/nodejs/nodejs-debugging#_breakpoint-validation"},"run with --nolazy"),", Node.js might not resolve breakpoints for code it hasn't parsed yet."),s("li",null,"If necessary, make sure your compiled files are up-to-date with your source files.")))},Fn=({bp:e})=>{if(!e.source.path)return null;let n=C();return s("div",{className:"content source-container"},s("h2",null,ne({absolutePath:e.source.path,url:e.source.path},n),":",e.params.line,":",e.params.column||1),s("ul",{className:"bp-tracing"},Cn(e,n)))},Nn=({bp:e})=>{let n=C(),t=M({url:e.source.path}),o=n.sources.filter(r=>M(r).toLowerCase()===t.toLowerCase());return o.length?s("li",null,s("p",null,"\u2753 We couldn't find a corresponding source location, but found some other files with the same name:"),s("ul",null,o.map(r=>s("li",{key:r},s(Dn,{original:e.source.path,updated:r.absolutePath||r.url})))),ee(n)?s("p",null,"You may need to adjust the ",s("code",null,"webRoot")," in your ",s("code",null,"launch.json")," ","if you're building from a subfolder, or tweak your ",s("code",null,"sourceMapPathOverrides"),"."):s("p",null,"If this is the same file, you may need to adjust your build tool"," ",ee(n)&&s(k,null,"or ",s("code",null,"webRoot")," in the launch.json")," to correct the paths.")):s("li",null,s("p",null,s(Pn,{basename:t})))},Dn=({original:e,updated:n})=>s("span",{className:"text-diff"},Ee(e.split(/[/\\]/g),n.split(/[/\\]/g),{ignoreCase:!0}).map((t,o)=>s("span",{className:t.added?"add":t.removed?"rm":"",key:o},o>0?"/":"",t.value.join("/")))),In=({loc:e})=>{let t=C().sources.find(o=>o.sourceReference===e.sourceReference);return s(k,null,s("code",null,t?.absolutePath??t?.url??"unknown")," line ",e.lineNumber," column"," ",e.columnNumber)},Hn=({cdp:e,index:n})=>{let t=C(),[o,r]=H(`showCdpBp${n}`,!1),{url:i,line:l,col:u,regex:c}="location"in e.args?{url:t.sources.find(d=>!d.compiledSourceRefToUrl&&d.scriptIds.includes(e.args.location.scriptId))?.url,regex:void 0,line:e.args.location.lineNumber+1,col:(e.args.location.columnNumber||0)+1}:{url:e.args.urlRegex?Rn(e.args.urlRegex):e.args.url,regex:e.args.urlRegex,line:e.args.lineNumber+1,col:(e.args.columnNumber||0)+1};return s("li",null,s("p",null,s("code",null,i)," line ",l," column ",u," ",c&&s("a",{onClick:()=>r(!o)},"via this regex")),o&&s("p",null,s("code",null,c)))},Rn=e=>e.replace(/\[([[a-z])[A-Z]\]/g,(n,t)=>t).replace(/\\\\/,"\\").replace(/\\\//g,"/").replace(/\|.+$/g,"").replace(/\\\./g,".");var En=Je(["Loaded in directly","Be parsed from a sourcemap"]),Pn=({basename:e})=>{let n=C(),[t,o]=Q(e.endsWith(".js")?void 0:"Be parsed from a sourcemap");return s(k,null,s("p",null,"\u2753 We couldn't find a corresponding source location, and didn't find any source with the name ",s("code",null,e),"."),s("p",null,"How did you expect this file to be loaded? (If you have a compilation step, you should pick 'sourcemap')",s(En,{onChange:o,value:t}),t==="Loaded in directly"&&(ee(n)?s("p",null,"It looks like your webpage didn't load this script; breakpoints won't be bound until the file they're set in is loaded. Make sure your script is imported from the right location using a ",s("code",null,"<script>")," tag."):s("p",null,"It looks like your program didn't load this script; breakpoints won't be bound until the file they're set in is loaded. Make sure your script is imported with a"," ",s("code",null,"require()")," or ",s("code",null,"import")," statement, such as"," ",s("code",null,"require('./",e,"')"),".")),t==="Be parsed from a sourcemap"&&s("p",null,"Here's some hints that might help you:",s("ul",null,/\.tsx?$/.test(e)?s("li",null,"Make sure you have ",s("code",null,'"sourceMap": true')," ","in your tsconfig to generate sourcemaps."):s("li",null,"Make sure your build tool is set up to create sourcemaps."),!n.config.outFiles.includes("!**/node_modules/**")&&s("li",null,"It looks like you narrowed the ",s("code",null,"outFiles")," ","in your launch.json. Try removing this: it now defaults to the whole workspace, and overspecifying it can unnecessarily narrow places where we'll resolve sourcemaps.")))))};var tn=({onPick:e})=>s("div",{className:"intro"},s("div",null,s("header",null,"Debug Doctor"),s("div",{className:"intro-content"},s("p",null,"What are you trying to find out?"),s("ul",null,s("li",null,s("a",{role:"button",onClick:()=>e(1)},"Why my breakpoints don't bind")),s("li",null,s("a",{role:"button",onClick:()=>e(2)},"What scripts and sourcemaps are loaded")),s("li",null,s("a",{href:"https://github.com/microsoft/vscode-js-debug/issues/new/choose"},"Something else..."))))));var on=()=>{let e=C(),n=V(()=>{let u=new Map;for(let c of e.sources)u.set(c.uniqueId,c);return u},[e.sources]),t=V(()=>e.sources.map(u=>[[u.url,u.absolutePath,u.prettyName].join(" ").toLowerCase(),u]).sort((u,c)=>pe(u[1])-pe(c[1])),[e.sources]),[o,r]=H("filter",""),i=V(()=>o?t.filter(([u])=>u.includes(o.toLowerCase())).map(([,u])=>u):t.map(u=>u[1]),[t,o]),l=A(u=>r(u.target.value),[]);return s(k,null,s("input",{placeholder:"Filter sources...",className:"source-filter",value:o,onChange:l,onKeyUp:l}),s("small",{style:{marginBottom:"1rem"}},"Showing ",i.length," of ",e.sources.length," sources..."),i.map(u=>s(An,{source:u,allSources:n,key:u.sourceReference})))},An=({source:e,allSources:n})=>{let[t,o]=H(`sourceBreadCrumbs${e.uniqueId}`,[e.uniqueId]),r=V(()=>t.map(d=>n.get(d)).filter($e),[n,t]),[i,l]=H(`sourceExpanded${e.uniqueId}`,!1),u=C(),c=A(()=>l(!i),[i]);return s("div",{className:`source-container ${i?" expanded":""}`},s("h2",{onClick:c},ne(e,u)),i&&s(k,null,r.length>1&&s(Mn,{sources:r,update:o}),s(On,{source:r[r.length-1],open:d=>{let p=u.sources.find(a=>a.sourceReference===d);p&&o(t.concat(p.uniqueId))}})))},Mn=({sources:e,update:n})=>s("ol",{className:"source-breadcrumbs"},e.map((t,o)=>{let r=`${M(t)} (#${t.sourceReference})`;return o===e.length-1?s("li",null,r):s("li",{key:o},s("a",{key:o,onClick:()=>n(e.slice(0,o+1).map(i=>i.uniqueId))},r)," ","\xBB"," ")})),On=({source:e,open:n})=>s("dl",{className:"source-data-grid"},s("dt",null,"url"),s("dd",null,s("code",null,e.url)),s("dt",null,"sourceReference"),s("dd",null,s("code",null,e.sourceReference)),s("dt",null,"absolutePath"),s("dd",null,s("code",null,e.absolutePath)),s("dt",null,"absolutePath verified?"),s("dd",null,e.compiledSourceRefToUrl?"\u2705 From sourcemap, assumed correct":e.actualAbsolutePath?"\u2705 Verified on disk":"\u274C Disk verification failed (does not exist or different content)"),s("dt",null,"sourcemap children:"),s("dd",null,e.sourceMap?s("ul",null,Object.entries(e.sourceMap.sources).map(([t,o])=>s("li",{key:t},s(Vn,{url:t,sourceRef:o,pick:n})))):"None (does not have a sourcemap)"),s("dt",null,"referenced from sourcemap:"),s("dd",null,e.compiledSourceRefToUrl?s("ul",null,e.compiledSourceRefToUrl.map(([t,o])=>s("li",{key:o},s(Bn,{url:o,sourceRef:t,pick:n})))):"None (not from a sourcemap)")),Vn=({url:e,sourceRef:n,pick:t})=>{let r=C().sources.find(l=>l.sourceReference===n),i=A(()=>t(n),[n]);return s(k,null,e," \u2192 ",s("a",{onClick:i},r?`${M(r)} (#${n})`:"unknown"))},Bn=({url:e,sourceRef:n,pick:t})=>{let r=C().sources.find(l=>l.sourceReference===n),i=A(()=>t(n),[n]);return s(k,null,s("a",{onClick:i},r?`${M(r)} (#${n})`:"unknown")," as ",e," ","\u2192 this")};var rn=({dump:e})=>{let[n,t]=H("experience",0);return s(_e.Provider,{value:e},n===0?s(tn,{onPick:t}):s(k,null,s("a",{role:"button",onClick:()=>t(0),className:"back"},"\u2190 Back"),n===1?s(nn,null):s(on,null)))};typeof DUMP<"u"?ue(s(rn,{dump:DUMP}),document.body):fetch(document.location.search.slice(1)).then(e=>e.json()).then(e=>ue(s(rn,{dump:e}),document.body));})();
//# sourceMappingURL=diagnosticTool.js.map
