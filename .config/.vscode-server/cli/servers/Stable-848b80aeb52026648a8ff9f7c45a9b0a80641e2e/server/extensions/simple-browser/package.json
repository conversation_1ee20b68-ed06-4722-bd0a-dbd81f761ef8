{"name": "simple-browser", "displayName": "%displayName%", "description": "%description%", "enabledApiProposals": ["externalUriOpener"], "version": "1.0.0", "icon": "media/icon.png", "publisher": "vscode", "license": "MIT", "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "engines": {"vscode": "^1.70.0"}, "main": "./dist/extension", "browser": "./dist/browser/extension", "categories": ["Other"], "extensionKind": ["ui", "workspace"], "activationEvents": ["onCommand:simpleBrowser.api.open", "onOpenExternalUri:http", "onOpenExternalUri:https", "onWebviewPanel:simpleBrowser.view"], "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "contributes": {"commands": [{"command": "simpleBrowser.show", "title": "Show", "category": "Simple Browser"}], "configuration": [{"title": "Simple Browser", "properties": {"simpleBrowser.focusLockIndicator.enabled": {"type": "boolean", "default": true, "title": "Focus Lock Indicator Enabled", "description": "%configuration.focusLockIndicator.enabled.description%"}}}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}