/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var js=function(e,t){return js=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,i){s.__proto__=i}||function(s,i){for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(s[r]=i[r])},js(e,t)};export function __extends(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");js(e,t);function s(){this.constructor=e}e.prototype=t===null?Object.create(t):(s.prototype=t.prototype,new s)}export var __assign=function(){return __assign=Object.assign||function(t){for(var s,i=1,r=arguments.length;i<r;i++){s=arguments[i];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(t[n]=s[n])}return t},__assign.apply(this,arguments)};export function __rest(e,t){var s={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(s[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)t.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(s[i[r]]=e[i[r]]);return s}export function __decorate(e,t,s,i){var r=arguments.length,n=r<3?t:i===null?i=Object.getOwnPropertyDescriptor(t,s):i,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")n=Reflect.decorate(e,t,s,i);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(n=(r<3?o(n):r>3?o(t,s,n):o(t,s))||n);return r>3&&n&&Object.defineProperty(t,s,n),n}export function __param(e,t){return function(s,i){t(s,i,e)}}export function __esDecorate(e,t,s,i,r,n){function o(b){if(b!==void 0&&typeof b!="function")throw new TypeError("Function expected");return b}for(var a=i.kind,l=a==="getter"?"get":a==="setter"?"set":"value",c=!t&&e?i.static?e:e.prototype:null,u=t||(c?Object.getOwnPropertyDescriptor(c,i.name):{}),h,f=!1,d=s.length-1;d>=0;d--){var p={};for(var v in i)p[v]=v==="access"?{}:i[v];for(var v in i.access)p.access[v]=i.access[v];p.addInitializer=function(b){if(f)throw new TypeError("Cannot add initializers after decoration has completed");n.push(o(b||null))};var g=(0,s[d])(a==="accessor"?{get:u.get,set:u.set}:u[l],p);if(a==="accessor"){if(g===void 0)continue;if(g===null||typeof g!="object")throw new TypeError("Object expected");(h=o(g.get))&&(u.get=h),(h=o(g.set))&&(u.set=h),(h=o(g.init))&&r.unshift(h)}else(h=o(g))&&(a==="field"?r.unshift(h):u[l]=h)}c&&Object.defineProperty(c,i.name,u),f=!0}export function __runInitializers(e,t,s){for(var i=arguments.length>2,r=0;r<t.length;r++)s=i?t[r].call(e,s):t[r].call(e);return i?s:void 0}export function __propKey(e){return typeof e=="symbol"?e:"".concat(e)}export function __setFunctionName(e,t,s){return typeof t=="symbol"&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:s?"".concat(s," ",t):t})}export function __metadata(e,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(e,t)}export function __awaiter(e,t,s,i){function r(n){return n instanceof s?n:new s(function(o){o(n)})}return new(s||(s=Promise))(function(n,o){function a(u){try{c(i.next(u))}catch(h){o(h)}}function l(u){try{c(i.throw(u))}catch(h){o(h)}}function c(u){u.done?n(u.value):r(u.value).then(a,l)}c((i=i.apply(e,t||[])).next())})}export function __generator(e,t){var s={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},i,r,n,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(c){return function(u){return l([c,u])}}function l(c){if(i)throw new TypeError("Generator is already executing.");for(;o&&(o=0,c[0]&&(s=0)),s;)try{if(i=1,r&&(n=c[0]&2?r.return:c[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,c[1])).done)return n;switch(r=0,n&&(c=[c[0]&2,n.value]),c[0]){case 0:case 1:n=c;break;case 4:return s.label++,{value:c[1],done:!1};case 5:s.label++,r=c[1],c=[0];continue;case 7:c=s.ops.pop(),s.trys.pop();continue;default:if(n=s.trys,!(n=n.length>0&&n[n.length-1])&&(c[0]===6||c[0]===2)){s=0;continue}if(c[0]===3&&(!n||c[1]>n[0]&&c[1]<n[3])){s.label=c[1];break}if(c[0]===6&&s.label<n[1]){s.label=n[1],n=c;break}if(n&&s.label<n[2]){s.label=n[2],s.ops.push(c);break}n[2]&&s.ops.pop(),s.trys.pop();continue}c=t.call(e,s)}catch(u){c=[6,u],r=0}finally{i=n=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}export var __createBinding=Object.create?function(e,t,s,i){i===void 0&&(i=s);var r=Object.getOwnPropertyDescriptor(t,s);(!r||("get"in r?!t.__esModule:r.writable||r.configurable))&&(r={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,i,r)}:function(e,t,s,i){i===void 0&&(i=s),e[i]=t[s]};export function __exportStar(e,t){for(var s in e)s!=="default"&&!Object.prototype.hasOwnProperty.call(t,s)&&__createBinding(t,e,s)}export function __values(e){var t=typeof Symbol=="function"&&Symbol.iterator,s=t&&e[t],i=0;if(s)return s.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}export function __read(e,t){var s=typeof Symbol=="function"&&e[Symbol.iterator];if(!s)return e;var i=s.call(e),r,n=[],o;try{for(;(t===void 0||t-- >0)&&!(r=i.next()).done;)n.push(r.value)}catch(a){o={error:a}}finally{try{r&&!r.done&&(s=i.return)&&s.call(i)}finally{if(o)throw o.error}}return n}export function __spread(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(__read(arguments[t]));return e}export function __spreadArrays(){for(var e=0,t=0,s=arguments.length;t<s;t++)e+=arguments[t].length;for(var i=Array(e),r=0,t=0;t<s;t++)for(var n=arguments[t],o=0,a=n.length;o<a;o++,r++)i[r]=n[o];return i}export function __spreadArray(e,t,s){if(s||arguments.length===2)for(var i=0,r=t.length,n;i<r;i++)(n||!(i in t))&&(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}export function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}export function __asyncGenerator(e,t,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i=s.apply(e,t||[]),r,n=[];return r={},a("next"),a("throw"),a("return",o),r[Symbol.asyncIterator]=function(){return this},r;function o(d){return function(p){return Promise.resolve(p).then(d,h)}}function a(d,p){i[d]&&(r[d]=function(v){return new Promise(function(g,b){n.push([d,v,g,b])>1||l(d,v)})},p&&(r[d]=p(r[d])))}function l(d,p){try{c(i[d](p))}catch(v){f(n[0][3],v)}}function c(d){d.value instanceof __await?Promise.resolve(d.value.v).then(u,h):f(n[0][2],d)}function u(d){l("next",d)}function h(d){l("throw",d)}function f(d,p){d(p),n.shift(),n.length&&l(n[0][0],n[0][1])}}export function __asyncDelegator(e){var t,s;return t={},i("next"),i("throw",function(r){throw r}),i("return"),t[Symbol.iterator]=function(){return this},t;function i(r,n){t[r]=e[r]?function(o){return(s=!s)?{value:__await(e[r](o)),done:!1}:n?n(o):o}:n}}export function __asyncValues(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],s;return t?t.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),s={},i("next"),i("throw"),i("return"),s[Symbol.asyncIterator]=function(){return this},s);function i(n){s[n]=e[n]&&function(o){return new Promise(function(a,l){o=e[n](o),r(a,l,o.done,o.value)})}}function r(n,o,a,l){Promise.resolve(l).then(function(c){n({value:c,done:a})},o)}}export function __makeTemplateObject(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var ga=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};export function __importStar(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var s in e)s!=="default"&&Object.prototype.hasOwnProperty.call(e,s)&&__createBinding(t,e,s);return ga(t,e),t}export function __importDefault(e){return e&&e.__esModule?e:{default:e}}export function __classPrivateFieldGet(e,t,s,i){if(s==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return s==="m"?i:s==="a"?i.call(e):i?i.value:t.get(e)}export function __classPrivateFieldSet(e,t,s,i,r){if(i==="m")throw new TypeError("Private method is not writable");if(i==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i==="a"?r.call(e,s):r?r.value=s:t.set(e,s),s}export function __classPrivateFieldIn(e,t){if(t===null||typeof t!="object"&&typeof t!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof e=="function"?t===e:e.has(t)}export function __addDisposableResource(e,t,s){if(t!=null){if(typeof t!="object"&&typeof t!="function")throw new TypeError("Object expected.");var i,r;if(s){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(i===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose],s&&(r=i)}if(typeof i!="function")throw new TypeError("Object not disposable.");r&&(i=function(){try{r.call(this)}catch(n){return Promise.reject(n)}}),e.stack.push({value:t,dispose:i,async:s})}else s&&e.stack.push({async:!0});return t}var va=typeof SuppressedError=="function"?SuppressedError:function(e,t,s){var i=new Error(s);return i.name="SuppressedError",i.error=e,i.suppressed=t,i};export function __disposeResources(e){function t(i){e.error=e.hasError?new va(i,e.error,"An error was suppressed during disposal."):i,e.hasError=!0}function s(){for(;e.stack.length;){var i=e.stack.pop();try{var r=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(r).then(s,function(n){return t(n),s()})}catch(n){t(n)}}if(e.hasError)throw e.error}return s()}export default{__extends,__assign,__rest,__decorate,__param,__metadata,__awaiter,__generator,__createBinding,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources};var ba=Object.create,Vi=Object.defineProperty,ya=Object.getOwnPropertyDescriptor,qi=Object.getOwnPropertyNames,wa=Object.getPrototypeOf,Ca=Object.prototype.hasOwnProperty,xa=(e,t)=>function(){return t||(0,e[qi(e)[0]])((t={exports:{}}).exports,t),t.exports},Ea=(e,t,s,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of qi(t))!Ca.call(e,r)&&r!==s&&Vi(e,r,{get:()=>t[r],enumerable:!(i=ya(t,r))||i.enumerable});return e},ka=(e,t,s)=>(s=e!=null?ba(wa(e)):{},Ea(t||!e||!e.__esModule?Vi(s,"default",{value:e,enumerable:!0}):s,e)),Sa=xa({"node_modules/minimist/index.js"(e,t){"use strict";function s(n,o){var a=n;o.slice(0,-1).forEach(function(c){a=a[c]||{}});var l=o[o.length-1];return l in a}function i(n){return typeof n=="number"||/^0x[0-9a-f]+$/i.test(n)?!0:/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(n)}function r(n,o){return o==="constructor"&&typeof n[o]=="function"||o==="__proto__"}t.exports=function(n,o){o||(o={});var a={bools:{},strings:{},unknownFn:null};typeof o.unknown=="function"&&(a.unknownFn=o.unknown),typeof o.boolean=="boolean"&&o.boolean?a.allBools=!0:[].concat(o.boolean).filter(Boolean).forEach(function(D){a.bools[D]=!0});var l={};function c(D){return l[D].some(function(Q){return a.bools[Q]})}Object.keys(o.alias||{}).forEach(function(D){l[D]=[].concat(o.alias[D]),l[D].forEach(function(Q){l[Q]=[D].concat(l[D].filter(function(ve){return Q!==ve}))})}),[].concat(o.string).filter(Boolean).forEach(function(D){a.strings[D]=!0,l[D]&&[].concat(l[D]).forEach(function(Q){a.strings[Q]=!0})});var u=o.default||{},h={_:[]};function f(D,Q){return a.allBools&&/^--[^=]+$/.test(Q)||a.strings[D]||a.bools[D]||l[D]}function d(D,Q,ve){for(var W=D,We=0;We<Q.length-1;We++){var y=Q[We];if(r(W,y))return;W[y]===void 0&&(W[y]={}),(W[y]===Object.prototype||W[y]===Number.prototype||W[y]===String.prototype)&&(W[y]={}),W[y]===Array.prototype&&(W[y]=[]),W=W[y]}var m=Q[Q.length-1];r(W,m)||((W===Object.prototype||W===Number.prototype||W===String.prototype)&&(W={}),W===Array.prototype&&(W=[]),W[m]===void 0||a.bools[m]||typeof W[m]=="boolean"?W[m]=ve:Array.isArray(W[m])?W[m].push(ve):W[m]=[W[m],ve])}function p(D,Q,ve){if(!(ve&&a.unknownFn&&!f(D,ve)&&a.unknownFn(ve)===!1)){var W=!a.strings[D]&&i(Q)?Number(Q):Q;d(h,D.split("."),W),(l[D]||[]).forEach(function(We){d(h,We.split("."),W)})}}Object.keys(a.bools).forEach(function(D){p(D,u[D]===void 0?!1:u[D])});var v=[];n.indexOf("--")!==-1&&(v=n.slice(n.indexOf("--")+1),n=n.slice(0,n.indexOf("--")));for(var g=0;g<n.length;g++){var b=n[g],$,R;if(/^--.+=/.test(b)){var tt=b.match(/^--([^=]+)=([\s\S]*)$/);$=tt[1];var k=tt[2];a.bools[$]&&(k=k!=="false"),p($,k,b)}else if(/^--no-.+/.test(b))$=b.match(/^--no-(.+)/)[1],p($,!1,b);else if(/^--.+/.test(b))$=b.match(/^--(.+)/)[1],R=n[g+1],R!==void 0&&!/^(-|--)[^-]/.test(R)&&!a.bools[$]&&!a.allBools&&(!l[$]||!c($))?(p($,R,b),g+=1):/^(true|false)$/.test(R)?(p($,R==="true",b),g+=1):p($,a.strings[$]?"":!0,b);else if(/^-[^-]+/.test(b)){for(var P=b.slice(1,-1).split(""),F=!1,q=0;q<P.length;q++){if(R=b.slice(q+2),R==="-"){p(P[q],R,b);continue}if(/[A-Za-z]/.test(P[q])&&R[0]==="="){p(P[q],R.slice(1),b),F=!0;break}if(/[A-Za-z]/.test(P[q])&&/-?\d+(\.\d*)?(e-?\d+)?$/.test(R)){p(P[q],R,b),F=!0;break}if(P[q+1]&&P[q+1].match(/\W/)){p(P[q],b.slice(q+2),b),F=!0;break}else p(P[q],a.strings[P[q]]?"":!0,b)}$=b.slice(-1)[0],!F&&$!=="-"&&(n[g+1]&&!/^(-|--)[^-]/.test(n[g+1])&&!a.bools[$]&&(!l[$]||!c($))?(p($,n[g+1],b),g+=1):n[g+1]&&/^(true|false)$/.test(n[g+1])?(p($,n[g+1]==="true",b),g+=1):p($,a.strings[$]?"":!0,b))}else if((!a.unknownFn||a.unknownFn(b)!==!1)&&h._.push(a.strings._||!i(b)?b:Number(b)),o.stopEarly){h._.push.apply(h._,n.slice(g+1));break}}return Object.keys(u).forEach(function(D){s(h,D.split("."))||(d(h,D.split("."),u[D]),(l[D]||[]).forEach(function(Q){d(h,Q.split("."),u[D])}))}),o["--"]?h["--"]=v.slice():v.forEach(function(D){h._.push(D)}),h}}}),it=class{constructor(e){this.d=e,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(e){this.c=e}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}},Pa=class{constructor(){this.b=[],this.a=function(e){setTimeout(()=>{throw e.stack?rt.isErrorNoTelemetry(e)?new rt(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.b.push(e),()=>{this.d(e)}}c(e){this.b.forEach(t=>{t(e)})}d(e){this.b.splice(this.b.indexOf(e),1)}setUnexpectedErrorHandler(e){this.a=e}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(e){this.a(e),this.c(e)}onUnexpectedExternalError(e){this.a(e)}},Aa=new Pa;function is(e){Da(e)||Aa.onUnexpectedError(e)}var Us="Canceled";function Da(e){return e instanceof Re?!0:e instanceof Error&&e.name===Us&&e.message===Us}var Re=class extends Error{constructor(){super(Us),this.name=this.message}};function La(e){return e?new Error(`Illegal state: ${e}`):new Error("Illegal state")}var rt=class ji extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof ji)return t;const s=new ji;return s.message=t.message,s.stack=t.stack,s}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}},$a=class Vo extends Error{constructor(t){super(t||"An unexpected bug occurred."),Object.setPrototypeOf(this,Vo.prototype)}};function Ma(e,t,s=0,i=e.length){let r=s,n=i;for(;r<n;){const o=Math.floor((r+n)/2);t(e[o])?r=o+1:n=o}return r-1}var vu=class qo{static{this.assertInvariants=!1}constructor(t){this.e=t,this.c=0}findLastMonotonous(t){if(qo.assertInvariants){if(this.d){for(const i of this.e)if(this.d(i)&&!t(i))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=t}const s=Ma(this.e,t,this.c);return this.c=s+1,s===-1?void 0:this.e[s]}};function Ia(e){return e.filter(t=>!!t)}function Gi(e,t){let s;if(typeof t=="number"){let i=t;s=()=>{const r=Math.sin(i++)*179426549;return r-Math.floor(r)}}else s=Math.random;for(let i=e.length-1;i>0;i-=1){const r=Math.floor(s()*(i+1)),n=e[i];e[i]=e[r],e[r]=n}}function Oa(e){return e[Math.floor(Math.random()*e.length)]}var Bs;(function(e){function t(n){return n<0}e.isLessThan=t;function s(n){return n<=0}e.isLessThanOrEqual=s;function i(n){return n>0}e.isGreaterThan=i;function r(n){return n===0}e.isNeitherLessOrGreaterThan=r,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(Bs||(Bs={}));function _a(e,t){return(s,i)=>t(e(s),e(i))}var Ra=(e,t)=>e-t,bu=class Os{static{this.empty=new Os(t=>{})}constructor(t){this.iterate=t}forEach(t){this.iterate(s=>(t(s),!0))}toArray(){const t=[];return this.iterate(s=>(t.push(s),!0)),t}filter(t){return new Os(s=>this.iterate(i=>t(i)?s(i):!0))}map(t){return new Os(s=>this.iterate(i=>s(t(i))))}some(t){let s=!1;return this.iterate(i=>(s=t(i),!s)),s}findFirst(t){let s;return this.iterate(i=>t(i)?(s=i,!1):!0),s}findLast(t){let s;return this.iterate(i=>(t(i)&&(s=i),!0)),s}findLastMaxBy(t){let s,i=!0;return this.iterate(r=>((i||Bs.isGreaterThan(t(r,s)))&&(i=!1,s=r),!0)),s}},Xi;function Na(e,t){const s=Object.create(null);for(const i of e){const r=t(i);let n=s[r];n||(n=s[r]=[]),n.push(i)}return s}var yu=class{static{Xi=Symbol.toStringTag}constructor(e,t){this.b=t,this.a=new Map,this[Xi]="SetWithKey";for(const s of e)this.add(s)}get size(){return this.a.size}add(e){const t=this.b(e);return this.a.set(t,e),this}delete(e){return this.a.delete(this.b(e))}has(e){return this.a.has(this.b(e))}*entries(){for(const e of this.a.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this.a.values())yield e}clear(){this.a.clear()}forEach(e,t){this.a.forEach(s=>e.call(t,s,s,this))}[Symbol.iterator](){return this.values()}},Ji,Yi,Qi,Fa=class{constructor(e,t){this.uri=e,this.value=t}};function Ta(e){return Array.isArray(e)}var zs=class Jt{static{this.c=t=>t.toString()}constructor(t,s){if(this[Ji]="ResourceMap",t instanceof Jt)this.d=new Map(t.d),this.e=s??Jt.c;else if(Ta(t)){this.d=new Map,this.e=s??Jt.c;for(const[i,r]of t)this.set(i,r)}else this.d=new Map,this.e=t??Jt.c}set(t,s){return this.d.set(this.e(t),new Fa(t,s)),this}get(t){return this.d.get(this.e(t))?.value}has(t){return this.d.has(this.e(t))}get size(){return this.d.size}clear(){this.d.clear()}delete(t){return this.d.delete(this.e(t))}forEach(t,s){typeof s<"u"&&(t=t.bind(s));for(const[i,r]of this.d)t(r.value,r.uri,this)}*values(){for(const t of this.d.values())yield t.value}*keys(){for(const t of this.d.values())yield t.uri}*entries(){for(const t of this.d.values())yield[t.uri,t.value]}*[(Ji=Symbol.toStringTag,Symbol.iterator)](){for(const[,t]of this.d)yield[t.uri,t.value]}},wu=class{constructor(e,t){this[Yi]="ResourceSet",!e||typeof e=="function"?this.c=new zs(e):(this.c=new zs(t),e.forEach(this.add,this))}get size(){return this.c.size}add(e){return this.c.set(e,e),this}clear(){this.c.clear()}delete(e){return this.c.delete(e)}forEach(e,t){this.c.forEach((s,i)=>e.call(t,i,i,this))}has(e){return this.c.has(e)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(Yi=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},Ki;(function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"})(Ki||(Ki={}));var ja=class{constructor(){this[Qi]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(e){return this.c.has(e)}get(e,t=0){const s=this.c.get(e);if(s)return t!==0&&this.n(s,t),s.value}set(e,t,s=0){let i=this.c.get(e);if(i)i.value=t,s!==0&&this.n(i,s);else{switch(i={key:e,value:t,next:void 0,previous:void 0},s){case 0:this.l(i);break;case 1:this.k(i);break;case 2:this.l(i);break;default:this.l(i);break}this.c.set(e,i),this.f++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this.c.get(e);if(t)return this.c.delete(e),this.m(t),this.f--,t.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const e=this.d;return this.c.delete(e.key),this.m(e),this.f--,e.value}forEach(e,t){const s=this.g;let i=this.d;for(;i;){if(t?e.bind(t)(i.value,i.key,this):e(i.value,i.key,this),this.g!==s)throw new Error("LinkedMap got modified during iteration.");i=i.next}}keys(){const e=this,t=this.g;let s=this.d;const i={[Symbol.iterator](){return i},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(s){const r={value:s.key,done:!1};return s=s.next,r}else return{value:void 0,done:!0}}};return i}values(){const e=this,t=this.g;let s=this.d;const i={[Symbol.iterator](){return i},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(s){const r={value:s.value,done:!1};return s=s.next,r}else return{value:void 0,done:!0}}};return i}entries(){const e=this,t=this.g;let s=this.d;const i={[Symbol.iterator](){return i},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(s){const r={value:[s.key,s.value],done:!1};return s=s.next,r}else return{value:void 0,done:!0}}};return i}[(Qi=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.d,s=this.size;for(;t&&s>e;)this.c.delete(t.key),t=t.next,s--;this.d=t,this.f=s,t&&(t.previous=void 0),this.g++}j(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.e,s=this.size;for(;t&&s>e;)this.c.delete(t.key),t=t.previous,s--;this.e=t,this.f=s,t&&(t.next=void 0),this.g++}k(e){if(!this.d&&!this.e)this.e=e;else if(this.d)e.next=this.d,this.d.previous=e;else throw new Error("Invalid list");this.d=e,this.g++}l(e){if(!this.d&&!this.e)this.d=e;else if(this.e)e.previous=this.e,this.e.next=e;else throw new Error("Invalid list");this.e=e,this.g++}m(e){if(e===this.d&&e===this.e)this.d=void 0,this.e=void 0;else if(e===this.d){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this.d=e.next}else if(e===this.e){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this.e=e.previous}else{const t=e.next,s=e.previous;if(!t||!s)throw new Error("Invalid list");t.previous=s,s.next=t}e.next=void 0,e.previous=void 0,this.g++}n(e,t){if(!this.d||!this.e)throw new Error("Invalid list");if(!(t!==1&&t!==2)){if(t===1){if(e===this.d)return;const s=e.next,i=e.previous;e===this.e?(i.next=void 0,this.e=i):(s.previous=i,i.next=s),e.previous=void 0,e.next=this.d,this.d.previous=e,this.d=e,this.g++}else if(t===2){if(e===this.e)return;const s=e.next,i=e.previous;e===this.d?(s.previous=void 0,this.d=s):(s.previous=i,i.next=s),e.next=void 0,e.previous=this.e,this.e.next=e,this.e=e,this.g++}}}toJSON(){const e=[];return this.forEach((t,s)=>{e.push([s,t])}),e}fromJSON(e){this.clear();for(const[t,s]of e)this.set(t,s)}},Ua=class extends ja{constructor(e,t=1){super(),this.o=e,this.p=Math.min(Math.max(0,t),1)}get limit(){return this.o}set limit(e){this.o=e,this.q()}get ratio(){return this.p}set ratio(e){this.p=Math.min(Math.max(0,e),1),this.q()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this}q(){this.size>this.o&&this.r(Math.round(this.o*this.p))}},Zi=class extends Ua{constructor(e,t=1){super(e,t)}r(e){this.h(e)}set(e,t){return super.set(e,t),this.q(),this}},Ba=class{constructor(){this.c=new Map}add(e,t){let s=this.c.get(e);s||(s=new Set,this.c.set(e,s)),s.add(t)}delete(e,t){const s=this.c.get(e);s&&(s.delete(t),s.size===0&&this.c.delete(e))}forEach(e,t){const s=this.c.get(e);s&&s.forEach(t)}get(e){const t=this.c.get(e);return t||new Set}};function za(e,t){if(e===t)return!0;if(e.size!==t.size)return!1;for(const[s,i]of e)if(!t.has(s)||t.get(s)!==i)return!1;for(const[s]of t)if(!e.has(s))return!1;return!0}function Ws(e,t){const s=this;let i=!1,r;return function(){if(i)return r;if(i=!0,t)try{r=e.apply(s,arguments)}finally{t()}else r=e.apply(s,arguments);return r}}function Hs(e,t){if(!e)throw new Error(t?`Assertion failed (${t})`:"Assertion Failed")}function er(e,t="unexpected state"){if(!e)throw typeof t=="string"?new $a(`Assertion Failed: ${t}`):t}function nt(e){return typeof e=="string"}function Wa(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)&&!(e instanceof RegExp)&&!(e instanceof Date)}function tr(e){return typeof e=="number"&&!isNaN(e)}function Ha(e){return!!e&&typeof e[Symbol.iterator]=="function"}function Va(e){return typeof e>"u"}function qa(e){return Va(e)||e===null}function Ga(e,t){if(!e)throw new Error(t?`Unexpected type, expected '${t}'`:"Unexpected type")}function Vs(e){return typeof e=="function"}var qs;(function(e){function t(k){return k&&typeof k=="object"&&typeof k[Symbol.iterator]=="function"}e.is=t;const s=Object.freeze([]);function i(){return s}e.empty=i;function*r(k){yield k}e.single=r;function n(k){return t(k)?k:r(k)}e.wrap=n;function o(k){return k||s}e.from=o;function*a(k){for(let P=k.length-1;P>=0;P--)yield k[P]}e.reverse=a;function l(k){return!k||k[Symbol.iterator]().next().done===!0}e.isEmpty=l;function c(k){return k[Symbol.iterator]().next().value}e.first=c;function u(k,P){let F=0;for(const q of k)if(P(q,F++))return!0;return!1}e.some=u;function h(k,P){for(const F of k)if(P(F))return F}e.find=h;function*f(k,P){for(const F of k)P(F)&&(yield F)}e.filter=f;function*d(k,P){let F=0;for(const q of k)yield P(q,F++)}e.map=d;function*p(k,P){let F=0;for(const q of k)yield*P(q,F++)}e.flatMap=p;function*v(...k){for(const P of k)Ha(P)?yield*P:yield P}e.concat=v;function g(k,P,F){let q=F;for(const D of k)q=P(q,D);return q}e.reduce=g;function b(k){let P=0;for(const F of k)P++;return P}e.length=b;function*$(k,P,F=k.length){for(P<-k.length&&(P=0),P<0&&(P+=k.length),F<0?F+=k.length:F>k.length&&(F=k.length);P<F;P++)yield k[P]}e.slice=$;function R(k,P=Number.POSITIVE_INFINITY){const F=[];if(P===0)return[F,k];const q=k[Symbol.iterator]();for(let D=0;D<P;D++){const Q=q.next();if(Q.done)return[F,e.empty()];F.push(Q.value)}return[F,{[Symbol.iterator](){return q}}]}e.consume=R;async function tt(k){const P=[];for await(const F of k)P.push(F);return Promise.resolve(P)}e.asyncToArray=tt})(qs||(qs={}));var Xa=!1,ot=null,Cu=class Go{constructor(){this.b=new Map}static{this.a=0}c(t){let s=this.b.get(t);return s||(s={parent:null,source:null,isSingleton:!1,value:t,idx:Go.a++},this.b.set(t,s)),s}trackDisposable(t){const s=this.c(t);s.source||(s.source=new Error().stack)}setParent(t,s){const i=this.c(t);i.parent=s}markAsDisposed(t){this.b.delete(t)}markAsSingleton(t){this.c(t).isSingleton=!0}f(t,s){const i=s.get(t);if(i)return i;const r=t.parent?this.f(this.c(t.parent),s):t;return s.set(t,r),r}getTrackedDisposables(){const t=new Map;return[...this.b.entries()].filter(([,i])=>i.source!==null&&!this.f(i,t).isSingleton).flatMap(([i])=>i)}computeLeakingDisposables(t=10,s){let i;if(s)i=s;else{const l=new Map,c=[...this.b.values()].filter(h=>h.source!==null&&!this.f(h,l).isSingleton);if(c.length===0)return;const u=new Set(c.map(h=>h.value));if(i=c.filter(h=>!(h.parent&&u.has(h.parent))),i.length===0)throw new Error("There are cyclic diposable chains!")}if(!i)return;function r(l){function c(h,f){for(;h.length>0&&f.some(d=>typeof d=="string"?d===h[0]:h[0].match(d));)h.shift()}const u=l.source.split(`
`).map(h=>h.trim().replace("at ","")).filter(h=>h!=="");return c(u,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),u.reverse()}const n=new Ba;for(const l of i){const c=r(l);for(let u=0;u<=c.length;u++)n.add(c.slice(0,u).join(`
`),l)}i.sort(_a(l=>l.idx,Ra));let o="",a=0;for(const l of i.slice(0,t)){a++;const c=r(l),u=[];for(let h=0;h<c.length;h++){let f=c[h];f=`(shared with ${n.get(c.slice(0,h+1).join(`
`)).size}/${i.length} leaks) at ${f}`;const p=n.get(c.slice(0,h).join(`
`)),v=Na([...p].map(g=>r(g)[h]),g=>g);delete v[c[h]];for(const[g,b]of Object.entries(v))u.unshift(`    - stacktraces of ${b.length} other leaks continue with ${g}`);u.unshift(f)}o+=`


==================== Leaking disposable ${a}/${i.length}: ${l.value.constructor.name} ====================
${u.join(`
`)}
============================================================

`}return i.length>t&&(o+=`


... and ${i.length-t} more leaking disposables

`),{leaks:i,details:o}}};function Ja(e){ot=e}if(Xa){const e="__is_disposable_tracked__";Ja(new class{trackDisposable(t){const s=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(s)},3e3)}setParent(t,s){if(t&&t!==G.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==G.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}function yt(e){return ot?.trackDisposable(e),e}function wt(e){ot?.markAsDisposed(e)}function Ct(e,t){ot?.setParent(e,t)}function Ya(e,t){if(ot)for(const s of e)ot.setParent(s,t)}function Qa(e){return typeof e=="object"&&e!==null&&typeof e.dispose=="function"&&e.dispose.length===0}function Ne(e){if(qs.is(e)){const t=[];for(const s of e)if(s)try{s.dispose()}catch(i){t.push(i)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function Ka(...e){const t=be(()=>Ne(e));return Ya(e,t),t}function be(e){const t=yt({dispose:Ws(()=>{wt(t),e()})});return t}var He=class Xo{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,yt(this)}dispose(){this.g||(wt(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{Ne(this.f)}finally{this.f.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return Ct(t,this),this.g?Xo.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(t),t.dispose()}}deleteAndLeak(t){t&&this.f.has(t)&&(this.f.delete(t),Ct(t,null))}},G=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new He,yt(this),Ct(this.q,this)}dispose(){wt(this),this.q.dispose()}B(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(e)}},sr=class{constructor(){this.b=!1,yt(this)}get value(){return this.b?void 0:this.a}set value(e){this.b||e===this.a||(this.a?.dispose(),e&&Ct(e,this),this.a=e)}clear(){this.value=void 0}dispose(){this.b=!0,wt(this),this.a?.dispose(),this.a=void 0}clearAndLeak(){const e=this.a;return this.a=void 0,e&&Ct(e,null),e}},Za=class{constructor(e){this.a=new sr,this.b=!1,this.a.value=e}get value(){return this.a.value}set value(e){this.b||e===this.a.value||(this.a.value=e)}dispose(){this.b=!0,this.a.dispose()}},e1=class{constructor(){this.a=new Map,this.b=!1,yt(this)}dispose(){wt(this),this.b=!0,this.clearAndDisposeAll()}clearAndDisposeAll(){if(this.a.size)try{Ne(this.a.values())}finally{this.a.clear()}}has(e){return this.a.has(e)}get size(){return this.a.size}get(e){return this.a.get(e)}set(e,t,s=!1){this.b&&console.warn(new Error("Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!").stack),s||this.a.get(e)?.dispose(),this.a.set(e,t)}deleteAndDispose(e){this.a.get(e)?.dispose(),this.a.delete(e)}deleteAndLeak(e){const t=this.a.get(e);return this.a.delete(e),t}keys(){return this.a.keys()}values(){return this.a.values()}[Symbol.iterator](){return this.a[Symbol.iterator]()}},rs=typeof Buffer<"u",t1=new it(()=>new Uint8Array(256)),Gs,Xs,ye=class ge{static alloc(t){return rs?new ge(Buffer.allocUnsafe(t)):new ge(new Uint8Array(t))}static wrap(t){return rs&&!Buffer.isBuffer(t)&&(t=Buffer.from(t.buffer,t.byteOffset,t.byteLength)),new ge(t)}static fromString(t,s){return!(s?.dontUseNodeBuffer||!1)&&rs?new ge(Buffer.from(t)):(Gs||(Gs=new TextEncoder),new ge(Gs.encode(t)))}static fromByteArray(t){const s=ge.alloc(t.length);for(let i=0,r=t.length;i<r;i++)s.buffer[i]=t[i];return s}static concat(t,s){if(typeof s>"u"){s=0;for(let n=0,o=t.length;n<o;n++)s+=t[n].byteLength}const i=ge.alloc(s);let r=0;for(let n=0,o=t.length;n<o;n++){const a=t[n];i.set(a,r),r+=a.byteLength}return i}constructor(t){this.buffer=t,this.byteLength=this.buffer.byteLength}clone(){const t=ge.alloc(this.byteLength);return t.set(this),t}toString(){return rs?this.buffer.toString():(Xs||(Xs=new TextDecoder),Xs.decode(this.buffer))}slice(t,s){return new ge(this.buffer.subarray(t,s))}set(t,s){if(t instanceof ge)this.buffer.set(t.buffer,s);else if(t instanceof Uint8Array)this.buffer.set(t,s);else if(t instanceof ArrayBuffer)this.buffer.set(new Uint8Array(t),s);else if(ArrayBuffer.isView(t))this.buffer.set(new Uint8Array(t.buffer,t.byteOffset,t.byteLength),s);else throw new Error("Unknown argument 'array'")}readUInt32BE(t){return i1(this.buffer,t)}writeUInt32BE(t,s){r1(this.buffer,t,s)}readUInt32LE(t){return n1(this.buffer,t)}writeUInt32LE(t,s){o1(this.buffer,t,s)}readUInt8(t){return a1(this.buffer,t)}writeUInt8(t,s){c1(this.buffer,t,s)}indexOf(t,s=0){return s1(this.buffer,t instanceof ge?t.buffer:t,s)}equals(t){return this===t?!0:this.byteLength!==t.byteLength?!1:this.buffer.every((s,i)=>s===t.buffer[i])}};function s1(e,t,s=0){const i=t.byteLength,r=e.byteLength;if(i===0)return 0;if(i===1)return e.indexOf(t[0]);if(i>r-s)return-1;const n=t1.value;n.fill(t.length);for(let c=0;c<t.length;c++)n[t[c]]=t.length-c-1;let o=s+t.length-1,a=o,l=-1;for(;o<r;)if(e[o]===t[a]){if(a===0){l=o;break}o--,a--}else o+=Math.max(t.length-a,n[e[o]]),a=t.length-1;return l}function i1(e,t){return e[t]*2**24+e[t+1]*2**16+e[t+2]*2**8+e[t+3]}function r1(e,t,s){e[s+3]=t,t=t>>>8,e[s+2]=t,t=t>>>8,e[s+1]=t,t=t>>>8,e[s]=t}function n1(e,t){return e[t+0]<<0>>>0|e[t+1]<<8>>>0|e[t+2]<<16>>>0|e[t+3]<<24>>>0}function o1(e,t,s){e[s+0]=t&255,t=t>>>8,e[s+1]=t&255,t=t>>>8,e[s+2]=t&255,t=t>>>8,e[s+3]=t&255}function a1(e,t){return e[t]}function c1(e,t,s){e[s]=t}function l1(){return globalThis._VSCODE_NLS_MESSAGES}function ir(){return globalThis._VSCODE_NLS_LANGUAGE}var h1=ir()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0;function rr(e,t){let s;return t.length===0?s=e:s=e.replace(/\{(\d+)\}/g,(i,r)=>{const n=r[0],o=t[n];let a=i;return typeof o=="string"?a=o:(typeof o=="number"||typeof o=="boolean"||o===void 0||o===null)&&(a=String(o)),a}),h1&&(s="\uFF3B"+s.replace(/[aouei]/g,"$&$&")+"\uFF3D"),s}function w(e,t,...s){return rr(typeof e=="number"?u1(e,t):t,s)}function u1(e,t){const s=l1()?.[e];if(typeof s!="string"){if(typeof t=="string")return t;throw new Error(`!!! NLS MISSING: ${e} !!!`)}return s}var at="en",xt=!1,Et=!1,kt=!1,f1=!1,nr=!1,Js=!1,d1=!1,or=!1,p1=!1,m1=!1,ns=void 0,os=at,ar=at,g1=void 0,ke=void 0,Se=globalThis,pe=void 0;typeof Se.vscode<"u"&&typeof Se.vscode.process<"u"?pe=Se.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(pe=process);var cr=typeof pe?.versions?.electron=="string",v1=cr&&pe?.type==="renderer";if(typeof pe=="object"){xt=pe.platform==="win32",Et=pe.platform==="darwin",kt=pe.platform==="linux",f1=kt&&!!pe.env.SNAP&&!!pe.env.SNAP_REVISION,d1=cr,p1=!!pe.env.CI||!!pe.env.BUILD_ARTIFACTSTAGINGDIRECTORY,ns=at,os=at;const e=pe.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e);ns=t.userLocale,ar=t.osLocale,os=t.resolvedLanguage||at,g1=t.languagePack?.translationsConfigFile}catch{}nr=!0}else typeof navigator=="object"&&!v1?(ke=navigator.userAgent,xt=ke.indexOf("Windows")>=0,Et=ke.indexOf("Macintosh")>=0,or=(ke.indexOf("Macintosh")>=0||ke.indexOf("iPad")>=0||ke.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,kt=ke.indexOf("Linux")>=0,m1=ke?.indexOf("Mobi")>=0,Js=!0,os=ir()||at,ns=navigator.language.toLowerCase(),ar=ns):console.error("Unable to resolve platform.");var lr;(function(e){e[e.Web=0]="Web",e[e.Mac=1]="Mac",e[e.Linux=2]="Linux",e[e.Windows=3]="Windows"})(lr||(lr={}));var as=0;Et?as=1:xt?as=3:kt&&(as=2);var N=xt,Fe=Et,St=kt,b1=nr,Ys=Js,y1=Js&&typeof Se.importScripts=="function",w1=y1?Se.origin:void 0,C1=as,xe=ke,Te=os,hr;(function(e){function t(){return Te}e.value=t;function s(){return Te.length===2?Te==="en":Te.length>=3?Te[0]==="e"&&Te[1]==="n"&&Te[2]==="-":!1}e.isDefaultVariant=s;function i(){return Te==="en"}e.isDefault=i})(hr||(hr={}));var x1=typeof Se.postMessage=="function"&&!Se.importScripts,E1=(()=>{if(x1){const e=[];Se.addEventListener("message",s=>{if(s.data&&s.data.vscodeScheduleAsyncWork)for(let i=0,r=e.length;i<r;i++){const n=e[i];if(n.id===s.data.vscodeScheduleAsyncWork){e.splice(i,1),n.callback();return}}});let t=0;return s=>{const i=++t;e.push({id:i,callback:s}),Se.postMessage({vscodeScheduleAsyncWork:i},"*")}}return e=>setTimeout(e)})(),ur;(function(e){e[e.Windows=1]="Windows",e[e.Macintosh=2]="Macintosh",e[e.Linux=3]="Linux"})(ur||(ur={}));var fr=Et||or?2:xt?1:3,dr=!!(xe&&xe.indexOf("Chrome")>=0),k1=!!(xe&&xe.indexOf("Firefox")>=0),S1=!!(!dr&&xe&&xe.indexOf("Safari")>=0),P1=!!(xe&&xe.indexOf("Edg/")>=0),xu=!!(xe&&xe.indexOf("Android")>=0),Ve,Qs=globalThis.vscode;if(typeof Qs<"u"&&typeof Qs.process<"u"){const e=Qs.process;Ve={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?Ve={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:Ve={get platform(){return N?"win32":Fe?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var Pt=Ve.cwd,Pe=Ve.env,pr=Ve.platform,Eu=Ve.arch,A1=65,D1=97,L1=90,$1=122,qe=46,re=47,he=92,Ae=58,M1=63,mr=class extends Error{constructor(e,t,s){let i;typeof t=="string"&&t.indexOf("not ")===0?(i="must not be",t=t.replace(/^not /,"")):i="must be";const r=e.indexOf(".")!==-1?"property":"argument";let n=`The "${e}" ${r} ${i} of type ${t}`;n+=`. Received type ${typeof s}`,super(n),this.code="ERR_INVALID_ARG_TYPE"}};function I1(e,t){if(e===null||typeof e!="object")throw new mr(t,"Object",e)}function Z(e,t){if(typeof e!="string")throw new mr(t,"string",e)}var ue=pr==="win32";function L(e){return e===re||e===he}function Ks(e){return e===re}function De(e){return e>=A1&&e<=L1||e>=D1&&e<=$1}function cs(e,t,s,i){let r="",n=0,o=-1,a=0,l=0;for(let c=0;c<=e.length;++c){if(c<e.length)l=e.charCodeAt(c);else{if(i(l))break;l=re}if(i(l)){if(!(o===c-1||a===1))if(a===2){if(r.length<2||n!==2||r.charCodeAt(r.length-1)!==qe||r.charCodeAt(r.length-2)!==qe){if(r.length>2){const u=r.lastIndexOf(s);u===-1?(r="",n=0):(r=r.slice(0,u),n=r.length-1-r.lastIndexOf(s)),o=c,a=0;continue}else if(r.length!==0){r="",n=0,o=c,a=0;continue}}t&&(r+=r.length>0?`${s}..`:"..",n=2)}else r.length>0?r+=`${s}${e.slice(o+1,c)}`:r=e.slice(o+1,c),n=c-o-1;o=c,a=0}else l===qe&&a!==-1?++a:a=-1}return r}function O1(e){return e?`${e[0]==="."?"":"."}${e}`:""}function gr(e,t){I1(t,"pathObject");const s=t.dir||t.root,i=t.base||`${t.name||""}${O1(t.ext)}`;return s?s===t.root?`${s}${i}`:`${s}${e}${i}`:i}var ee={resolve(...e){let t="",s="",i=!1;for(let r=e.length-1;r>=-1;r--){let n;if(r>=0){if(n=e[r],Z(n,`paths[${r}]`),n.length===0)continue}else t.length===0?n=Pt():(n=Pe[`=${t}`]||Pt(),(n===void 0||n.slice(0,2).toLowerCase()!==t.toLowerCase()&&n.charCodeAt(2)===he)&&(n=`${t}\\`));const o=n.length;let a=0,l="",c=!1;const u=n.charCodeAt(0);if(o===1)L(u)&&(a=1,c=!0);else if(L(u))if(c=!0,L(n.charCodeAt(1))){let h=2,f=h;for(;h<o&&!L(n.charCodeAt(h));)h++;if(h<o&&h!==f){const d=n.slice(f,h);for(f=h;h<o&&L(n.charCodeAt(h));)h++;if(h<o&&h!==f){for(f=h;h<o&&!L(n.charCodeAt(h));)h++;(h===o||h!==f)&&(l=`\\\\${d}\\${n.slice(f,h)}`,a=h)}}}else a=1;else De(u)&&n.charCodeAt(1)===Ae&&(l=n.slice(0,2),a=2,o>2&&L(n.charCodeAt(2))&&(c=!0,a=3));if(l.length>0)if(t.length>0){if(l.toLowerCase()!==t.toLowerCase())continue}else t=l;if(i){if(t.length>0)break}else if(s=`${n.slice(a)}\\${s}`,i=c,c&&t.length>0)break}return s=cs(s,!i,"\\",L),i?`${t}\\${s}`:`${t}${s}`||"."},normalize(e){Z(e,"path");const t=e.length;if(t===0)return".";let s=0,i,r=!1;const n=e.charCodeAt(0);if(t===1)return Ks(n)?"\\":e;if(L(n))if(r=!0,L(e.charCodeAt(1))){let a=2,l=a;for(;a<t&&!L(e.charCodeAt(a));)a++;if(a<t&&a!==l){const c=e.slice(l,a);for(l=a;a<t&&L(e.charCodeAt(a));)a++;if(a<t&&a!==l){for(l=a;a<t&&!L(e.charCodeAt(a));)a++;if(a===t)return`\\\\${c}\\${e.slice(l)}\\`;a!==l&&(i=`\\\\${c}\\${e.slice(l,a)}`,s=a)}}}else s=1;else De(n)&&e.charCodeAt(1)===Ae&&(i=e.slice(0,2),s=2,t>2&&L(e.charCodeAt(2))&&(r=!0,s=3));let o=s<t?cs(e.slice(s),!r,"\\",L):"";if(o.length===0&&!r&&(o="."),o.length>0&&L(e.charCodeAt(t-1))&&(o+="\\"),!r&&i===void 0&&e.includes(":")){if(o.length>=2&&De(o.charCodeAt(0))&&o.charCodeAt(1)===Ae)return`.\\${o}`;let a=e.indexOf(":");do if(a===t-1||L(e.charCodeAt(a+1)))return`.\\${o}`;while((a=e.indexOf(":",a+1))!==-1)}return i===void 0?r?`\\${o}`:o:r?`${i}\\${o}`:`${i}${o}`},isAbsolute(e){Z(e,"path");const t=e.length;if(t===0)return!1;const s=e.charCodeAt(0);return L(s)||t>2&&De(s)&&e.charCodeAt(1)===Ae&&L(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,s;for(let n=0;n<e.length;++n){const o=e[n];Z(o,"path"),o.length>0&&(t===void 0?t=s=o:t+=`\\${o}`)}if(t===void 0)return".";let i=!0,r=0;if(typeof s=="string"&&L(s.charCodeAt(0))){++r;const n=s.length;n>1&&L(s.charCodeAt(1))&&(++r,n>2&&(L(s.charCodeAt(2))?++r:i=!1))}if(i){for(;r<t.length&&L(t.charCodeAt(r));)r++;r>=2&&(t=`\\${t.slice(r)}`)}return ee.normalize(t)},relative(e,t){if(Z(e,"from"),Z(t,"to"),e===t)return"";const s=ee.resolve(e),i=ee.resolve(t);if(s===i||(e=s.toLowerCase(),t=i.toLowerCase(),e===t))return"";if(s.length!==e.length||i.length!==t.length){const p=s.split("\\"),v=i.split("\\");p[p.length-1]===""&&p.pop(),v[v.length-1]===""&&v.pop();const g=p.length,b=v.length,$=g<b?g:b;let R;for(R=0;R<$&&p[R].toLowerCase()===v[R].toLowerCase();R++);return R===0?i:R===$?b>$?v.slice(R).join("\\"):g>$?"..\\".repeat(g-1-R)+"..":"":"..\\".repeat(g-R)+v.slice(R).join("\\")}let r=0;for(;r<e.length&&e.charCodeAt(r)===he;)r++;let n=e.length;for(;n-1>r&&e.charCodeAt(n-1)===he;)n--;const o=n-r;let a=0;for(;a<t.length&&t.charCodeAt(a)===he;)a++;let l=t.length;for(;l-1>a&&t.charCodeAt(l-1)===he;)l--;const c=l-a,u=o<c?o:c;let h=-1,f=0;for(;f<u;f++){const p=e.charCodeAt(r+f);if(p!==t.charCodeAt(a+f))break;p===he&&(h=f)}if(f!==u){if(h===-1)return i}else{if(c>u){if(t.charCodeAt(a+f)===he)return i.slice(a+f+1);if(f===2)return i.slice(a+f)}o>u&&(e.charCodeAt(r+f)===he?h=f:f===2&&(h=3)),h===-1&&(h=0)}let d="";for(f=r+h+1;f<=n;++f)(f===n||e.charCodeAt(f)===he)&&(d+=d.length===0?"..":"\\..");return a+=h,d.length>0?`${d}${i.slice(a,l)}`:(i.charCodeAt(a)===he&&++a,i.slice(a,l))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=ee.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===he){if(t.charCodeAt(1)===he){const s=t.charCodeAt(2);if(s!==M1&&s!==qe)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(De(t.charCodeAt(0))&&t.charCodeAt(1)===Ae&&t.charCodeAt(2)===he)return`\\\\?\\${t}`;return t},dirname(e){Z(e,"path");const t=e.length;if(t===0)return".";let s=-1,i=0;const r=e.charCodeAt(0);if(t===1)return L(r)?e:".";if(L(r)){if(s=i=1,L(e.charCodeAt(1))){let a=2,l=a;for(;a<t&&!L(e.charCodeAt(a));)a++;if(a<t&&a!==l){for(l=a;a<t&&L(e.charCodeAt(a));)a++;if(a<t&&a!==l){for(l=a;a<t&&!L(e.charCodeAt(a));)a++;if(a===t)return e;a!==l&&(s=i=a+1)}}}}else De(r)&&e.charCodeAt(1)===Ae&&(s=t>2&&L(e.charCodeAt(2))?3:2,i=s);let n=-1,o=!0;for(let a=t-1;a>=i;--a)if(L(e.charCodeAt(a))){if(!o){n=a;break}}else o=!1;if(n===-1){if(s===-1)return".";n=s}return e.slice(0,n)},basename(e,t){t!==void 0&&Z(t,"suffix"),Z(e,"path");let s=0,i=-1,r=!0,n;if(e.length>=2&&De(e.charCodeAt(0))&&e.charCodeAt(1)===Ae&&(s=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=s;--n){const l=e.charCodeAt(n);if(L(l)){if(!r){s=n+1;break}}else a===-1&&(r=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?--o===-1&&(i=n):(o=-1,i=a))}return s===i?i=a:i===-1&&(i=e.length),e.slice(s,i)}for(n=e.length-1;n>=s;--n)if(L(e.charCodeAt(n))){if(!r){s=n+1;break}}else i===-1&&(r=!1,i=n+1);return i===-1?"":e.slice(s,i)},extname(e){Z(e,"path");let t=0,s=-1,i=0,r=-1,n=!0,o=0;e.length>=2&&e.charCodeAt(1)===Ae&&De(e.charCodeAt(0))&&(t=i=2);for(let a=e.length-1;a>=t;--a){const l=e.charCodeAt(a);if(L(l)){if(!n){i=a+1;break}continue}r===-1&&(n=!1,r=a+1),l===qe?s===-1?s=a:o!==1&&(o=1):s!==-1&&(o=-1)}return s===-1||r===-1||o===0||o===1&&s===r-1&&s===i+1?"":e.slice(s,r)},format:gr.bind(null,"\\"),parse(e){Z(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const s=e.length;let i=0,r=e.charCodeAt(0);if(s===1)return L(r)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(L(r)){if(i=1,L(e.charCodeAt(1))){let h=2,f=h;for(;h<s&&!L(e.charCodeAt(h));)h++;if(h<s&&h!==f){for(f=h;h<s&&L(e.charCodeAt(h));)h++;if(h<s&&h!==f){for(f=h;h<s&&!L(e.charCodeAt(h));)h++;h===s?i=h:h!==f&&(i=h+1)}}}}else if(De(r)&&e.charCodeAt(1)===Ae){if(s<=2)return t.root=t.dir=e,t;if(i=2,L(e.charCodeAt(2))){if(s===3)return t.root=t.dir=e,t;i=3}}i>0&&(t.root=e.slice(0,i));let n=-1,o=i,a=-1,l=!0,c=e.length-1,u=0;for(;c>=i;--c){if(r=e.charCodeAt(c),L(r)){if(!l){o=c+1;break}continue}a===-1&&(l=!1,a=c+1),r===qe?n===-1?n=c:u!==1&&(u=1):n!==-1&&(u=-1)}return a!==-1&&(n===-1||u===0||u===1&&n===a-1&&n===o+1?t.base=t.name=e.slice(o,a):(t.name=e.slice(o,n),t.base=e.slice(o,a),t.ext=e.slice(n,a))),o>0&&o!==i?t.dir=e.slice(0,o-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},_1=(()=>{if(ue){const e=/\\/g;return()=>{const t=Pt().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>Pt()})(),B={resolve(...e){let t="",s=!1;for(let i=e.length-1;i>=0&&!s;i--){const r=e[i];Z(r,`paths[${i}]`),r.length!==0&&(t=`${r}/${t}`,s=r.charCodeAt(0)===re)}if(!s){const i=_1();t=`${i}/${t}`,s=i.charCodeAt(0)===re}return t=cs(t,!s,"/",Ks),s?`/${t}`:t.length>0?t:"."},normalize(e){if(Z(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===re,s=e.charCodeAt(e.length-1)===re;return e=cs(e,!t,"/",Ks),e.length===0?t?"/":s?"./":".":(s&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return Z(e,"path"),e.length>0&&e.charCodeAt(0)===re},join(...e){if(e.length===0)return".";const t=[];for(let s=0;s<e.length;++s){const i=e[s];Z(i,"path"),i.length>0&&t.push(i)}return t.length===0?".":B.normalize(t.join("/"))},relative(e,t){if(Z(e,"from"),Z(t,"to"),e===t||(e=B.resolve(e),t=B.resolve(t),e===t))return"";const s=1,i=e.length,r=i-s,n=1,o=t.length-n,a=r<o?r:o;let l=-1,c=0;for(;c<a;c++){const h=e.charCodeAt(s+c);if(h!==t.charCodeAt(n+c))break;h===re&&(l=c)}if(c===a)if(o>a){if(t.charCodeAt(n+c)===re)return t.slice(n+c+1);if(c===0)return t.slice(n+c)}else r>a&&(e.charCodeAt(s+c)===re?l=c:c===0&&(l=0));let u="";for(c=s+l+1;c<=i;++c)(c===i||e.charCodeAt(c)===re)&&(u+=u.length===0?"..":"/..");return`${u}${t.slice(n+l)}`},toNamespacedPath(e){return e},dirname(e){if(Z(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===re;let s=-1,i=!0;for(let r=e.length-1;r>=1;--r)if(e.charCodeAt(r)===re){if(!i){s=r;break}}else i=!1;return s===-1?t?"/":".":t&&s===1?"//":e.slice(0,s)},basename(e,t){t!==void 0&&Z(t,"suffix"),Z(e,"path");let s=0,i=-1,r=!0,n;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(n=e.length-1;n>=0;--n){const l=e.charCodeAt(n);if(l===re){if(!r){s=n+1;break}}else a===-1&&(r=!1,a=n+1),o>=0&&(l===t.charCodeAt(o)?--o===-1&&(i=n):(o=-1,i=a))}return s===i?i=a:i===-1&&(i=e.length),e.slice(s,i)}for(n=e.length-1;n>=0;--n)if(e.charCodeAt(n)===re){if(!r){s=n+1;break}}else i===-1&&(r=!1,i=n+1);return i===-1?"":e.slice(s,i)},extname(e){Z(e,"path");let t=-1,s=0,i=-1,r=!0,n=0;for(let o=e.length-1;o>=0;--o){const a=e[o];if(a==="/"){if(!r){s=o+1;break}continue}i===-1&&(r=!1,i=o+1),a==="."?t===-1?t=o:n!==1&&(n=1):t!==-1&&(n=-1)}return t===-1||i===-1||n===0||n===1&&t===i-1&&t===s+1?"":e.slice(t,i)},format:gr.bind(null,"/"),parse(e){Z(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const s=e.charCodeAt(0)===re;let i;s?(t.root="/",i=1):i=0;let r=-1,n=0,o=-1,a=!0,l=e.length-1,c=0;for(;l>=i;--l){const u=e.charCodeAt(l);if(u===re){if(!a){n=l+1;break}continue}o===-1&&(a=!1,o=l+1),u===qe?r===-1?r=l:c!==1&&(c=1):r!==-1&&(c=-1)}if(o!==-1){const u=n===0&&s?1:n;r===-1||c===0||c===1&&r===o-1&&r===n+1?t.base=t.name=e.slice(u,o):(t.name=e.slice(u,r),t.base=e.slice(u,o),t.ext=e.slice(r,o))}return n>0?t.dir=e.slice(0,n-1):s&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};B.win32=ee.win32=ee,B.posix=ee.posix=B;var At=ue?ee.normalize:B.normalize,vr=ue?ee.isAbsolute:B.isAbsolute,O=ue?ee.join:B.join,ls=ue?ee.resolve:B.resolve,R1=ue?ee.relative:B.relative,Dt=ue?ee.dirname:B.dirname,hs=ue?ee.basename:B.basename,ku=ue?ee.extname:B.extname,Su=ue?ee.format:B.format,N1=ue?ee.parse:B.parse,Pu=ue?ee.toNamespacedPath:B.toNamespacedPath,us=ue?ee.sep:B.sep,br=ue?ee.delimiter:B.delimiter,F1=/^\w[\w\d+.-]*$/,T1=/^\//,j1=/^\/\//;function U1(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!F1.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!T1.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(j1.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function B1(e,t){return!e&&!t?"file":e}function z1(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==we&&(t=we+t):t=we;break}return t}var X="",we="/",W1=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,j=class _s{static isUri(t){return t instanceof _s?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}constructor(t,s,i,r,n,o=!1){typeof t=="object"?(this.scheme=t.scheme||X,this.authority=t.authority||X,this.path=t.path||X,this.query=t.query||X,this.fragment=t.fragment||X):(this.scheme=B1(t,o),this.authority=s||X,this.path=z1(this.scheme,i||X),this.query=r||X,this.fragment=n||X,U1(this,o))}get fsPath(){return fs(this,!1)}with(t){if(!t)return this;let{scheme:s,authority:i,path:r,query:n,fragment:o}=t;return s===void 0?s=this.scheme:s===null&&(s=X),i===void 0?i=this.authority:i===null&&(i=X),r===void 0?r=this.path:r===null&&(r=X),n===void 0?n=this.query:n===null&&(n=X),o===void 0?o=this.fragment:o===null&&(o=X),s===this.scheme&&i===this.authority&&r===this.path&&n===this.query&&o===this.fragment?this:new ct(s,i,r,n,o)}static parse(t,s=!1){const i=W1.exec(t);return i?new ct(i[2]||X,ds(i[4]||X),ds(i[5]||X),ds(i[7]||X),ds(i[9]||X),s):new ct(X,X,X,X,X)}static file(t){let s=X;if(N&&(t=t.replace(/\\/g,we)),t[0]===we&&t[1]===we){const i=t.indexOf(we,2);i===-1?(s=t.substring(2),t=we):(s=t.substring(2,i),t=t.substring(i)||we)}return new ct("file",s,t,X,X)}static from(t,s){return new ct(t.scheme,t.authority,t.path,t.query,t.fragment,s)}static joinPath(t,...s){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let i;return N&&t.scheme==="file"?i=_s.file(ee.join(fs(t,!0),...s)).path:i=B.join(t.path,...s),t.with({path:i})}toString(t=!1){return Zs(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof _s)return t;{const s=new ct(t);return s._formatted=t.external??null,s._fsPath=t._sep===yr?t.fsPath??null:null,s}}else return t}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},yr=N?1:void 0,ct=class extends j{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=fs(this,!1)),this._fsPath}toString(e=!1){return e?Zs(this,!0):(this._formatted||(this._formatted=Zs(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=yr),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}},wr={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function Cr(e,t,s){let i,r=-1;for(let n=0;n<e.length;n++){const o=e.charCodeAt(n);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||t&&o===47||s&&o===91||s&&o===93||s&&o===58)r!==-1&&(i+=encodeURIComponent(e.substring(r,n)),r=-1),i!==void 0&&(i+=e.charAt(n));else{i===void 0&&(i=e.substr(0,n));const a=wr[o];a!==void 0?(r!==-1&&(i+=encodeURIComponent(e.substring(r,n)),r=-1),i+=a):r===-1&&(r=n)}}return r!==-1&&(i+=encodeURIComponent(e.substring(r))),i!==void 0?i:e}function H1(e){let t;for(let s=0;s<e.length;s++){const i=e.charCodeAt(s);i===35||i===63?(t===void 0&&(t=e.substr(0,s)),t+=wr[i]):t!==void 0&&(t+=e[s])}return t!==void 0?t:e}function fs(e,t){let s;return e.authority&&e.path.length>1&&e.scheme==="file"?s=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?s=e.path.substr(1):s=e.path[1].toLowerCase()+e.path.substr(2):s=e.path,N&&(s=s.replace(/\//g,"\\")),s}function Zs(e,t){const s=t?H1:Cr;let i="",{scheme:r,authority:n,path:o,query:a,fragment:l}=e;if(r&&(i+=r,i+=":"),(n||r==="file")&&(i+=we,i+=we),n){let c=n.indexOf("@");if(c!==-1){const u=n.substr(0,c);n=n.substr(c+1),c=u.lastIndexOf(":"),c===-1?i+=s(u,!1,!1):(i+=s(u.substr(0,c),!1,!1),i+=":",i+=s(u.substr(c+1),!1,!0)),i+="@"}n=n.toLowerCase(),c=n.lastIndexOf(":"),c===-1?i+=s(n,!1,!0):(i+=s(n.substr(0,c),!1,!0),i+=n.substr(c))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){const c=o.charCodeAt(1);c>=65&&c<=90&&(o=`/${String.fromCharCode(c+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){const c=o.charCodeAt(0);c>=65&&c<=90&&(o=`${String.fromCharCode(c+32)}:${o.substr(2)}`)}i+=s(o,!0,!1)}return a&&(i+="?",i+=s(a,!1,!1)),l&&(i+="#",i+=t?l:Cr(l,!1,!1)),i}function xr(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+xr(e.substr(3)):e}}var Er=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ds(e){return e.match(Er)?e.replace(Er,t=>xr(t)):e}var V1=new class{transformIncoming(e){return e}transformOutgoing(e){return e}transformOutgoingURI(e){return e}transformOutgoingScheme(e){return e}},Au=class Rs{static{this.Undefined=new Rs(void 0)}constructor(t){this.element=t,this.next=Rs.Undefined,this.prev=Rs.Undefined}},q1=globalThis.performance.now.bind(globalThis.performance),G1=class Jo{static create(t){return new Jo(t)}constructor(t){this.c=t===!1?Date.now:q1,this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}},kr=!1,X1=!1,K;(function(e){e.None=()=>G.None;function t(y){if(X1){const{onDidAddListener:m}=y,E=ei.create();let x=0;y.onDidAddListener=()=>{++x===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),E.print()),m?.()}}}function s(y,m){return d(y,()=>{},0,void 0,!0,void 0,m)}e.defer=s;function i(y){return(m,E=null,x)=>{let S=!1,M;return M=y(H=>{if(!S)return M?M.dispose():S=!0,m.call(E,H)},null,x),S&&M.dispose(),M}}e.once=i;function r(y,m){return e.once(e.filter(y,m))}e.onceIf=r;function n(y,m,E){return h((x,S=null,M)=>y(H=>x.call(S,m(H)),null,M),E)}e.map=n;function o(y,m,E){return h((x,S=null,M)=>y(H=>{m(H),x.call(S,H)},null,M),E)}e.forEach=o;function a(y,m,E){return h((x,S=null,M)=>y(H=>m(H)&&x.call(S,H),null,M),E)}e.filter=a;function l(y){return y}e.signal=l;function c(...y){return(m,E=null,x)=>{const S=Ka(...y.map(M=>M(H=>m.call(E,H))));return f(S,x)}}e.any=c;function u(y,m,E,x){let S=E;return n(y,M=>(S=m(S,M),S),x)}e.reduce=u;function h(y,m){let E;const x={onWillAddFirstListener(){E=y(S.fire,S)},onDidRemoveLastListener(){E?.dispose()}};m||t(x);const S=new C(x);return m?.add(S),S.event}function f(y,m){return m instanceof Array?m.push(y):m&&m.add(y),y}function d(y,m,E=100,x=!1,S=!1,M,H){let ie,ne,st,ts=0,bt;const Hi={leakWarningThreshold:M,onWillAddFirstListener(){ie=y(pa=>{ts++,ne=m(ne,pa),x&&!st&&(ss.fire(ne),ne=void 0),bt=()=>{const ma=ne;ne=void 0,st=void 0,(!x||ts>1)&&ss.fire(ma),ts=0},typeof E=="number"?(clearTimeout(st),st=setTimeout(bt,E)):st===void 0&&(st=0,queueMicrotask(bt))})},onWillRemoveListener(){S&&ts>0&&bt?.()},onDidRemoveLastListener(){bt=void 0,ie.dispose()}};H||t(Hi);const ss=new C(Hi);return H?.add(ss),ss.event}e.debounce=d;function p(y,m=0,E){return e.debounce(y,(x,S)=>x?(x.push(S),x):[S],m,void 0,!0,void 0,E)}e.accumulate=p;function v(y,m=(x,S)=>x===S,E){let x=!0,S;return a(y,M=>{const H=x||!m(M,S);return x=!1,S=M,H},E)}e.latch=v;function g(y,m,E){return[e.filter(y,m,E),e.filter(y,x=>!m(x),E)]}e.split=g;function b(y,m=!1,E=[],x){let S=E.slice(),M=y(ne=>{S?S.push(ne):ie.fire(ne)});x&&x.add(M);const H=()=>{S?.forEach(ne=>ie.fire(ne)),S=null},ie=new C({onWillAddFirstListener(){M||(M=y(ne=>ie.fire(ne)),x&&x.add(M))},onDidAddFirstListener(){S&&(m?setTimeout(H):H())},onDidRemoveLastListener(){M&&M.dispose(),M=null}});return x&&x.add(ie),ie.event}e.buffer=b;function $(y,m){return(x,S,M)=>{const H=m(new tt);return y(function(ie){const ne=H.evaluate(ie);ne!==R&&x.call(S,ne)},void 0,M)}}e.chain=$;const R=Symbol("HaltChainable");class tt{constructor(){this.f=[]}map(m){return this.f.push(m),this}forEach(m){return this.f.push(E=>(m(E),E)),this}filter(m){return this.f.push(E=>m(E)?E:R),this}reduce(m,E){let x=E;return this.f.push(S=>(x=m(x,S),x)),this}latch(m=(E,x)=>E===x){let E=!0,x;return this.f.push(S=>{const M=E||!m(S,x);return E=!1,x=S,M?S:R}),this}evaluate(m){for(const E of this.f)if(m=E(m),m===R)break;return m}}function k(y,m,E=x=>x){const x=(...ie)=>H.fire(E(...ie)),S=()=>y.on(m,x),M=()=>y.removeListener(m,x),H=new C({onWillAddFirstListener:S,onDidRemoveLastListener:M});return H.event}e.fromNodeEventEmitter=k;function P(y,m,E=x=>x){const x=(...ie)=>H.fire(E(...ie)),S=()=>y.addEventListener(m,x),M=()=>y.removeEventListener(m,x),H=new C({onWillAddFirstListener:S,onDidRemoveLastListener:M});return H.event}e.fromDOMEventEmitter=P;function F(y,m){return new Promise(E=>i(y)(E,null,m))}e.toPromise=F;function q(y){const m=new C;return y.then(E=>{m.fire(E)},()=>{m.fire(void 0)}).finally(()=>{m.dispose()}),m.event}e.fromPromise=q;function D(y,m){return y(E=>m.fire(E))}e.forward=D;function Q(y,m,E){return m(E),y(x=>m(x))}e.runAndSubscribe=Q;class ve{constructor(m,E){this._observable=m,this.f=0,this.g=!1;const x={onWillAddFirstListener:()=>{m.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{m.removeObserver(this)}};E||t(x),this.emitter=new C(x),E&&E.add(this.emitter)}beginUpdate(m){this.f++}handlePossibleChange(m){}handleChange(m,E){this.g=!0}endUpdate(m){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function W(y,m){return new ve(y,m).emitter.event}e.fromObservable=W;function We(y){return(m,E,x)=>{let S=0,M=!1;const H={beginUpdate(){S++},endUpdate(){S--,S===0&&(y.reportChanges(),M&&(M=!1,m.call(E)))},handlePossibleChange(){},handleChange(){M=!0}};y.addObserver(H),y.reportChanges();const ie={dispose(){y.removeObserver(H)}};return x instanceof He?x.add(ie):Array.isArray(x)&&x.push(ie),ie}}e.fromObservableLight=We})(K||(K={}));var J1=class Ui{static{this.all=new Set}static{this.f=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${Ui.f++}`,Ui.all.add(this)}start(t){this.g=new G1,this.listenerCount=t}stop(){if(this.g){const t=this.g.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this.g=void 0}}},Sr=-1,Y1=class Yo{static{this.f=1}constructor(t,s,i=(Yo.f++).toString(16).padStart(3,"0")){this.j=t,this.threshold=s,this.name=i,this.h=0}dispose(){this.g?.clear()}check(t,s){const i=this.threshold;if(i<=0||s<i)return;this.g||(this.g=new Map);const r=this.g.get(t.value)||0;if(this.g.set(t.value,r+1),this.h-=1,this.h<=0){this.h=i*.5;const[n,o]=this.getMostFrequentStack(),a=`[${this.name}] potential listener LEAK detected, having ${s} listeners already. MOST frequent listener (${o}):`;console.warn(a),console.warn(n);const l=new Q1(a,n);this.j(l)}return()=>{const n=this.g.get(t.value)||0;this.g.set(t.value,n-1)}}getMostFrequentStack(){if(!this.g)return;let t,s=0;for(const[i,r]of this.g)(!t||s<r)&&(t=[i,r],s=r);return t}},ei=class Qo{static create(){const t=new Error;return new Qo(t.stack??"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},Q1=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},K1=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},Z1=0,ps=class{constructor(e){this.value=e,this.id=Z1++}},ec=2,tc=(e,t)=>{if(e instanceof ps)t(e);else for(let s=0;s<e.length;s++){const i=e[s];i&&t(i)}},C=class{constructor(e){this.z=0,this.f=e,this.g=Sr>0||this.f?.leakWarningThreshold?new Y1(e?.onListenerError??is,this.f?.leakWarningThreshold??Sr):void 0,this.j=this.f?._profName?new J1(this.f._profName):void 0,this.w=this.f?.deliveryQueue}dispose(){if(!this.m){if(this.m=!0,this.w?.current===this&&this.w.reset(),this.u){if(kr){const e=this.u;queueMicrotask(()=>{tc(e,t=>t.stack?.print())})}this.u=void 0,this.z=0}this.f?.onDidRemoveLastListener?.(),this.g?.dispose()}}get event(){return this.q??=(e,t,s)=>{if(this.g&&this.z>this.g.threshold**2){const a=`[${this.g.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.z} vs ${this.g.threshold})`;console.warn(a);const l=this.g.getMostFrequentStack()??["UNKNOWN stack",-1],c=new K1(`${a}. HINT: Stack shows most frequent listener (${l[1]}-times)`,l[0]);return(this.f?.onListenerError||is)(c),G.None}if(this.m)return G.None;t&&(e=e.bind(t));const i=new ps(e);let r,n;this.g&&this.z>=Math.ceil(this.g.threshold*.2)&&(i.stack=ei.create(),r=this.g.check(i.stack,this.z+1)),kr&&(i.stack=n??ei.create()),this.u?this.u instanceof ps?(this.w??=new sc,this.u=[this.u,i]):this.u.push(i):(this.f?.onWillAddFirstListener?.(this),this.u=i,this.f?.onDidAddFirstListener?.(this)),this.f?.onDidAddListener?.(this),this.z++;const o=be(()=>{r?.(),this.A(i)});return s instanceof He?s.add(o):Array.isArray(s)&&s.push(o),o},this.q}A(e){if(this.f?.onWillRemoveListener?.(this),!this.u)return;if(this.z===1){this.u=void 0,this.f?.onDidRemoveLastListener?.(this),this.z=0;return}const t=this.u,s=t.indexOf(e);if(s===-1)throw console.log("disposed?",this.m),console.log("size?",this.z),console.log("arr?",JSON.stringify(this.u)),new Error("Attempted to dispose unknown listener");this.z--,t[s]=void 0;const i=this.w.current===this;if(this.z*ec<=t.length){let r=0;for(let n=0;n<t.length;n++)t[n]?t[r++]=t[n]:i&&r<this.w.end&&(this.w.end--,r<this.w.i&&this.w.i--);t.length=r}}B(e,t){if(!e)return;const s=this.f?.onListenerError||is;if(!s){e.value(t);return}try{e.value(t)}catch(i){s(i)}}C(e){const t=e.current.u;for(;e.i<e.end;)this.B(t[e.i++],e.value);e.reset()}fire(e){if(this.w?.current&&(this.C(this.w),this.j?.stop()),this.j?.start(this.z),this.u)if(this.u instanceof ps)this.B(this.u,e);else{const t=this.w;t.enqueue(this,e,this.u.length),this.C(t)}this.j?.stop()}hasListeners(){return this.z>0}},sc=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,s){this.i=0,this.end=s,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}},ic=class{constructor(){this.g=!1,this.h=[],this.f=new C({onWillAddFirstListener:()=>this.j(),onDidRemoveLastListener:()=>this.k()})}get event(){return this.f.event}add(e){const t={event:e,listener:null};return this.h.push(t),this.g&&this.m(t),be(Ws(()=>{this.g&&this.o(t);const i=this.h.indexOf(t);this.h.splice(i,1)}))}j(){this.g=!0,this.h.forEach(e=>this.m(e))}k(){this.g=!1,this.h.forEach(e=>this.o(e))}m(e){e.listener=e.event(t=>this.f.fire(t))}o(e){e.listener?.dispose(),e.listener=null}dispose(){this.f.dispose();for(const e of this.h)e.listener?.dispose();this.h=[]}},rc=class{constructor(){this.f=!1,this.g=K.None,this.h=G.None,this.j=new C({onDidAddFirstListener:()=>{this.f=!0,this.h=this.g(this.j.fire,this.j)},onDidRemoveLastListener:()=>{this.f=!1,this.h.dispose()}}),this.event=this.j.event}set input(e){this.g=e,this.f&&(this.h.dispose(),this.h=e(this.j.fire,this.j))}dispose(){this.h.dispose(),this.j.dispose()}},Pr=Object.freeze(function(e,t){const s=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(s)}}}),Lt;(function(e){function t(s){return s===e.None||s===e.Cancelled||s instanceof ms?!0:!s||typeof s!="object"?!1:typeof s.isCancellationRequested=="boolean"&&typeof s.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:K.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Pr})})(Lt||(Lt={}));var ms=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?Pr:(this.b||(this.b=new C),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}},ti=class{constructor(e){this.f=void 0,this.g=void 0,this.g=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this.f||(this.f=new ms),this.f}cancel(){this.f?this.f instanceof ms&&this.f.cancel():this.f=Lt.Cancelled}dispose(e=!1){e&&this.cancel(),this.g?.dispose(),this.f?this.f instanceof ms&&this.f.dispose():this.f=Lt.None}};function nc(e){return e}var oc=class{constructor(e,t){this.a=void 0,this.b=void 0,typeof e=="function"?(this.c=e,this.d=nc):(this.c=t,this.d=e.getCacheKey)}get(e){const t=this.d(e);return this.b!==t&&(this.b=t,this.a=this.c(e)),this.a}};function ac(e){return!e||typeof e!="string"?!0:e.trim().length===0}var cc=/{(\d+)}/g;function lt(e,...t){return t.length===0?e:e.replace(cc,function(s,i){const r=parseInt(i,10);return isNaN(r)||r<0||r>=t.length?s:t[r]})}function si(e,t){return e<t?-1:e>t?1:0}function ii(e,t,s=0,i=e.length,r=0,n=t.length){for(;s<i&&r<n;s++,r++){const l=e.charCodeAt(s),c=t.charCodeAt(r);if(l<c)return-1;if(l>c)return 1}const o=i-s,a=n-r;return o<a?-1:o>a?1:0}function Ar(e,t){return $t(e,t,0,e.length,0,t.length)}function $t(e,t,s=0,i=e.length,r=0,n=t.length){for(;s<i&&r<n;s++,r++){let l=e.charCodeAt(s),c=t.charCodeAt(r);if(l===c)continue;if(l>=128||c>=128)return ii(e.toLowerCase(),t.toLowerCase(),s,i,r,n);Dr(l)&&(l-=32),Dr(c)&&(c-=32);const u=l-c;if(u!==0)return u}const o=i-s,a=n-r;return o<a?-1:o>a?1:0}function Dr(e){return e>=97&&e<=122}function Lr(e){return e>=65&&e<=90}function lc(e,t){return e.length===t.length&&$t(e,t)===0}function hc(e,t){const s=t.length;return t.length>e.length?!1:$t(e,t,0,s)===0}function uc(e){return 55296<=e&&e<=56319}function $r(e){return 56320<=e&&e<=57343}function fc(e,t){return(e-55296<<10)+(t-56320)+65536}var dc=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,pc=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,mc=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,gc=new RegExp("(?:"+[dc.source,pc.source,mc.source].join("|")+")","g");function vc(e){return e&&(e=e.replace(gc,"")),e}var bc=/\\\[.*?\\\]/g;function yc(e){return vc(e).replace(bc,"")}var Du="\uFEFF",Mr;(function(e){e[e.Other=0]="Other",e[e.Prepend=1]="Prepend",e[e.CR=2]="CR",e[e.LF=3]="LF",e[e.Control=4]="Control",e[e.Extend=5]="Extend",e[e.Regional_Indicator=6]="Regional_Indicator",e[e.SpacingMark=7]="SpacingMark",e[e.L=8]="L",e[e.V=9]="V",e[e.T=10]="T",e[e.LV=11]="LV",e[e.LVT=12]="LVT",e[e.ZWJ=13]="ZWJ",e[e.Extended_Pictographic=14]="Extended_Pictographic"})(Mr||(Mr={}));var Lu=class Yt{static{this.c=null}static getInstance(){return Yt.c||(Yt.c=new Yt),Yt.c}constructor(){this.d=wc()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const s=this.d,i=s.length/3;let r=1;for(;r<=i;)if(t<s[3*r])r=2*r;else if(t>s[3*r+1])r=2*r+1;else return s[3*r+2];return 0}};function wc(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var Ir;(function(e){e[e.zwj=8205]="zwj",e[e.emojiVariantSelector=65039]="emojiVariantSelector",e[e.enclosingKeyCap=8419]="enclosingKeyCap",e[e.space=32]="space"})(Ir||(Ir={}));var $u=class Qt{static{this.c=new it(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new oc({getCacheKey:JSON.stringify},t=>{function s(u){const h=new Map;for(let f=0;f<u.length;f+=2)h.set(u[f],u[f+1]);return h}function i(u,h){const f=new Map(u);for(const[d,p]of h)f.set(d,p);return f}function r(u,h){if(!u)return h;const f=new Map;for(const[d,p]of u)h.has(d)&&f.set(d,p);return f}const n=this.c.value;let o=t.filter(u=>!u.startsWith("_")&&u in n);o.length===0&&(o=["_default"]);let a;for(const u of o){const h=s(n[u]);a=r(a,h)}const l=s(n._common),c=i(l,a);return new Qt(c)})}static getInstance(t){return Qt.d.get(Array.from(t))}static{this.e=new it(()=>Object.keys(Qt.c.value).filter(t=>!t.startsWith("_")))}static getLocales(){return Qt.e.value}constructor(t){this.f=t}isAmbiguous(t){return this.f.has(t)}containsAmbiguousCharacter(t){for(let s=0;s<t.length;s++){const i=t.codePointAt(s);if(typeof i=="number"&&this.isAmbiguous(i))return!0}return!1}getPrimaryConfusable(t){return this.f.get(t)}getConfusableCodePoints(){return new Set(this.f.keys())}},Mu=class Kt{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(Kt.c())].flat())),this.d}static isInvisibleCharacter(t){return Kt.e().has(t)}static containsInvisibleCharacter(t){for(let s=0;s<t.length;s++){const i=t.codePointAt(s);if(typeof i=="number"&&(Kt.isInvisibleCharacter(i)||i===32))return!0}return!1}static get codePoints(){return Kt.e()}};function je(e){return e===47||e===92}function Or(e){return e.replace(/[\\/]/g,B.sep)}function Cc(e){return e.indexOf("/")===-1&&(e=Or(e)),/^[a-zA-Z]:(\/|$)/.test(e)&&(e="/"+e),e}function _r(e,t=B.sep){if(!e)return"";const s=e.length,i=e.charCodeAt(0);if(je(i)){if(je(e.charCodeAt(1))&&!je(e.charCodeAt(2))){let n=3;const o=n;for(;n<s&&!je(e.charCodeAt(n));n++);if(o!==n&&!je(e.charCodeAt(n+1))){for(n+=1;n<s;n++)if(je(e.charCodeAt(n)))return e.slice(0,n+1).replace(/[\\/]/g,t)}}return t}else if(Rr(i)&&e.charCodeAt(1)===58)return je(e.charCodeAt(2))?e.slice(0,2)+t:e.slice(0,2);let r=e.indexOf("://");if(r!==-1){for(r+=3;r<s;r++)if(je(e.charCodeAt(r)))return e.slice(0,r+1)}return""}function ri(e,t,s,i=us){if(e===t)return!0;if(!e||!t||t.length>e.length)return!1;if(s){if(!hc(e,t))return!1;if(t.length===e.length)return!0;let n=t.length;return t.charAt(t.length-1)===i&&n--,e.charAt(n)===i}return t.charAt(t.length-1)!==i&&(t+=i),e.indexOf(t)===0}function Rr(e){return e>=65&&e<=90||e>=97&&e<=122}function xc(e){const t=At(e);return N?e.length>3?!1:Ec(t)&&(e.length===2||t.charCodeAt(2)===92):t===B.sep}function Ec(e,t=N){return t?Rr(e.charCodeAt(0))&&e.charCodeAt(1)===58:!1}var kc="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",Sc="BDEFGHIJKMOQRSTUVWXYZbdefghijkmoqrstuvwxyz0123456789";function Pc(e,t,s=8){let i="";for(let n=0;n<s;n++){let o;n===0&&N&&!t&&(s===3||s===4)?o=Sc:o=kc,i+=o.charAt(Math.floor(Math.random()*o.length))}let r;return t?r=`${t}-${i}`:r=i,e?O(e,r):r}var te;(function(e){e.inMemory="inmemory",e.vscode="vscode",e.internal="private",e.walkThrough="walkThrough",e.walkThroughSnippet="walkThroughSnippet",e.http="http",e.https="https",e.file="file",e.mailto="mailto",e.untitled="untitled",e.data="data",e.command="command",e.vscodeRemote="vscode-remote",e.vscodeRemoteResource="vscode-remote-resource",e.vscodeManagedRemoteResource="vscode-managed-remote-resource",e.vscodeUserData="vscode-userdata",e.vscodeCustomEditor="vscode-custom-editor",e.vscodeNotebookCell="vscode-notebook-cell",e.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",e.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",e.vscodeNotebookCellOutput="vscode-notebook-cell-output",e.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",e.vscodeNotebookMetadata="vscode-notebook-metadata",e.vscodeInteractiveInput="vscode-interactive-input",e.vscodeSettings="vscode-settings",e.vscodeWorkspaceTrust="vscode-workspace-trust",e.vscodeTerminal="vscode-terminal",e.vscodeChatCodeBlock="vscode-chat-code-block",e.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",e.vscodeChatSesssion="vscode-chat-editor",e.webviewPanel="webview-panel",e.vscodeWebview="vscode-webview",e.extension="extension",e.vscodeFileResource="vscode-file",e.tmp="tmp",e.vsls="vsls",e.vscodeSourceControl="vscode-scm",e.commentsInput="comment",e.codeSetting="code-setting",e.outputChannel="output",e.accessibleView="accessible-view"})(te||(te={}));var Ac="tkn",Dc=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(e){this.d=e}setDelegate(e){this.e=e}setServerRootPath(e,t){this.f=B.join(t??"/",$c(e))}getServerRootPath(){return this.f}get g(){return B.join(this.f,te.vscodeRemoteResource)}set(e,t,s){this.a[e]=t,this.b[e]=s}setConnectionToken(e,t){this.c[e]=t}getPreferredWebSchema(){return this.d}rewrite(e){if(this.e)try{return this.e(e)}catch(o){return is(o),e}const t=e.authority;let s=this.a[t];s&&s.indexOf(":")!==-1&&s.indexOf("[")===-1&&(s=`[${s}]`);const i=this.b[t],r=this.c[t];let n=`path=${encodeURIComponent(e.path)}`;return typeof r=="string"&&(n+=`&${Ac}=${encodeURIComponent(r)}`),j.from({scheme:Ys?this.d:te.vscodeRemoteResource,authority:`${s}:${i}`,path:this.g,query:n})}},Lc=new Dc;function $c(e){return`${e.quality??"oss"}-${e.commit??"dev"}`}var Mc="vscode-app",Ic=class Ns{static{this.a=Mc}asBrowserUri(t){const s=this.b(t);return this.uriToBrowserUri(s)}uriToBrowserUri(t){return t.scheme===te.vscodeRemote?Lc.rewrite(t):t.scheme===te.file&&(b1||w1===`${te.vscodeFileResource}://${Ns.a}`)?t.with({scheme:te.vscodeFileResource,authority:t.authority||Ns.a,query:null,fragment:null}):t}asFileUri(t){const s=this.b(t);return this.uriToFileUri(s)}uriToFileUri(t){return t.scheme===te.vscodeFileResource?t.with({scheme:te.file,authority:t.authority!==Ns.a?t.authority:null,query:null,fragment:null}):t}b(t){if(j.isUri(t))return t;if(globalThis._VSCODE_FILE_ROOT){const s=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(s))return j.joinPath(j.parse(s,!0),t);const i=O(s,t);return j.file(i)}throw new Error("Cannot determine URI for module id!")}},Mt=new Ic,Iu=Object.freeze({"Cache-Control":"no-cache, no-store"}),Ou=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),Nr;(function(e){const t=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);e.CoopAndCoep=Object.freeze(t.get("3"));const s="vscode-coi";function i(n){let o;typeof n=="string"?o=new URL(n).searchParams:n instanceof URL?o=n.searchParams:j.isUri(n)&&(o=new URL(n.toString(!0)).searchParams);const a=o?.get(s);if(a)return t.get(a)}e.getHeadersFromQuery=i;function r(n,o,a){if(!globalThis.crossOriginIsolated)return;const l=o&&a?"3":a?"2":"1";n instanceof URLSearchParams?n.set(s,l):n[s]=l}e.addSearchParam=r})(Nr||(Nr={}));function Le(e){return fs(e,!0)}var ni=class{constructor(e){this.a=e}compare(e,t,s=!1){return e===t?0:si(this.getComparisonKey(e,s),this.getComparisonKey(t,s))}isEqual(e,t,s=!1){return e===t?!0:!e||!t?!1:this.getComparisonKey(e,s)===this.getComparisonKey(t,s)}getComparisonKey(e,t=!1){return e.with({path:this.a(e)?e.path.toLowerCase():void 0,fragment:t?null:void 0}).toString()}ignorePathCasing(e){return this.a(e)}isEqualOrParent(e,t,s=!1){if(e.scheme===t.scheme){if(e.scheme===te.file)return ri(Le(e),Le(t),this.a(e))&&e.query===t.query&&(s||e.fragment===t.fragment);if(Fr(e.authority,t.authority))return ri(e.path,t.path,this.a(e),"/")&&e.query===t.query&&(s||e.fragment===t.fragment)}return!1}joinPath(e,...t){return j.joinPath(e,...t)}basenameOrAuthority(e){return _c(e)||e.authority}basename(e){return B.basename(e.path)}extname(e){return B.extname(e.path)}dirname(e){if(e.path.length===0)return e;let t;return e.scheme===te.file?t=j.file(Dt(Le(e))).path:(t=B.dirname(e.path),e.authority&&t.length&&t.charCodeAt(0)!==47&&(console.error(`dirname("${e.toString})) resulted in a relative path`),t="/")),e.with({path:t})}normalizePath(e){if(!e.path.length)return e;let t;return e.scheme===te.file?t=j.file(At(Le(e))).path:t=B.normalize(e.path),e.with({path:t})}relativePath(e,t){if(e.scheme!==t.scheme||!Fr(e.authority,t.authority))return;if(e.scheme===te.file){const r=R1(Le(e),Le(t));return N?Or(r):r}let s=e.path||"/";const i=t.path||"/";if(this.a(e)){let r=0;for(const n=Math.min(s.length,i.length);r<n&&!(s.charCodeAt(r)!==i.charCodeAt(r)&&s.charAt(r).toLowerCase()!==i.charAt(r).toLowerCase());r++);s=i.substr(0,r)+s.substr(r)}return B.relative(s,i)}resolvePath(e,t){if(e.scheme===te.file){const s=j.file(ls(Le(e),t));return e.with({authority:s.authority,path:s.path})}return t=Cc(t),e.with({path:B.resolve(e.path,t)})}isAbsolutePath(e){return!!e.path&&e.path[0]==="/"}isEqualAuthority(e,t){return e===t||e!==void 0&&t!==void 0&&lc(e,t)}hasTrailingPathSeparator(e,t=us){if(e.scheme===te.file){const s=Le(e);return s.length>_r(s).length&&s[s.length-1]===t}else{const s=e.path;return s.length>1&&s.charCodeAt(s.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(e.fsPath)}}removeTrailingPathSeparator(e,t=us){return Tr(e,t)?e.with({path:e.path.substr(0,e.path.length-1)}):e}addTrailingPathSeparator(e,t=us){let s=!1;if(e.scheme===te.file){const i=Le(e);s=i!==void 0&&i.length===_r(i).length&&i[i.length-1]===t}else{t="/";const i=e.path;s=i.length===1&&i.charCodeAt(i.length-1)===47}return!s&&!Tr(e,t)?e.with({path:e.path+"/"}):e}},T=new ni(()=>!1),Oc=new ni(e=>e.scheme===te.file?!St:!0),_u=new ni(e=>!0),Ru=T.isEqual.bind(T),Nu=T.isEqualOrParent.bind(T),Fu=T.getComparisonKey.bind(T),Tu=T.basenameOrAuthority.bind(T),_c=T.basename.bind(T),ju=T.extname.bind(T),Uu=T.dirname.bind(T),Ce=T.joinPath.bind(T),Bu=T.normalizePath.bind(T),zu=T.relativePath.bind(T),Wu=T.resolvePath.bind(T),Hu=T.isAbsolutePath.bind(T),Fr=T.isEqualAuthority.bind(T),Tr=T.hasTrailingPathSeparator.bind(T),Vu=T.removeTrailingPathSeparator.bind(T),qu=T.addTrailingPathSeparator.bind(T),jr;(function(e){e.META_DATA_LABEL="label",e.META_DATA_DESCRIPTION="description",e.META_DATA_SIZE="size",e.META_DATA_MIME="mime";function t(s){const i=new Map;s.path.substring(s.path.indexOf(";")+1,s.path.lastIndexOf(";")).split(";").forEach(o=>{const[a,l]=o.split(":");a&&l&&i.set(a,l)});const n=s.path.substring(0,s.path.indexOf(";"));return n&&i.set(e.META_DATA_MIME,n),i}e.parseMetaData=t})(jr||(jr={}));var Gu=Symbol("MicrotaskDelay");function oi(e){const t=new ti,s=e(t.token);let i=!1;const r=new Promise((n,o)=>{const a=t.token.onCancellationRequested(()=>{i=!0,a.dispose(),o(new Re)});Promise.resolve(s).then(l=>{a.dispose(),t.dispose(),i?Qa(l)&&l.dispose():n(l)},l=>{a.dispose(),t.dispose(),o(l)})});return new class{cancel(){t.cancel(),t.dispose()}then(n,o){return r.then(n,o)}catch(n){return this.then(void 0,n)}finally(n){return r.finally(n)}}}var Rc=class{constructor(){this.a=!1,this.b=new Promise((e,t)=>{this.d=e})}isOpen(){return this.a}open(){this.a=!0,this.d(!0)}wait(){return this.b}},Nc=class extends Rc{constructor(e){super(),this.f=setTimeout(()=>this.open(),e)}open(){clearTimeout(this.f),super.open()}};function Ue(e,t){return t?new Promise((s,i)=>{const r=setTimeout(()=>{n.dispose(),s()},e),n=t.onCancellationRequested(()=>{clearTimeout(r),n.dispose(),i(new Re)})}):oi(s=>Ue(e,s))}var Fc=class{constructor(e){this.a=0,this.b=!1,this.f=e,this.g=[],this.d=0,this.h=new C}whenIdle(){return this.size>0?K.toPromise(this.onDrained):Promise.resolve()}get onDrained(){return this.h.event}get size(){return this.a}queue(e){if(this.b)throw new Error("Object has been disposed");return this.a++,new Promise((t,s)=>{this.g.push({factory:e,c:t,e:s}),this.j()})}j(){for(;this.g.length&&this.d<this.f;){const e=this.g.shift();this.d++;const t=e.factory();t.then(e.c,e.e),t.then(()=>this.k(),()=>this.k())}}k(){this.b||(this.d--,--this.a===0&&this.h.fire(),this.g.length>0&&this.j())}clear(){if(this.b)throw new Error("Object has been disposed");this.g.length=0,this.a=this.d}dispose(){this.b=!0,this.g.length=0,this.a=0,this.h.dispose()}},Ur=class extends Fc{constructor(){super(1)}},Tc=class{constructor(){this.a=new Map,this.b=new Set,this.d=void 0,this.f=0}async whenDrained(){if(this.g())return;const e=new Bc;return this.b.add(e),e.p}g(){for(const[,e]of this.a)if(e.size>0)return!1;return!0}queueSize(e,t=T){const s=t.getComparisonKey(e);return this.a.get(s)?.size??0}queueFor(e,t,s=T){const i=s.getComparisonKey(e);let r=this.a.get(i);if(!r){r=new Ur;const n=this.f++,o=K.once(r.onDrained)(()=>{r?.dispose(),this.a.delete(i),this.h(),this.d?.deleteAndDispose(n),this.d?.size===0&&(this.d.dispose(),this.d=void 0)});this.d||(this.d=new e1),this.d.set(n,o),this.a.set(i,r)}return r.queue(t)}h(){this.g()&&this.j()}j(){for(const e of this.b)e.complete();this.b.clear()}dispose(){for(const[,e]of this.a)e.dispose();this.a.clear(),this.j(),this.d?.dispose()}},jc=class{constructor(e,t){this.b=-1,this.a=e,this.d=t,this.f=this.g.bind(this)}dispose(){this.cancel(),this.a=null}cancel(){this.isScheduled()&&(clearTimeout(this.b),this.b=-1)}schedule(e=this.d){this.cancel(),this.b=setTimeout(this.f,e)}get delay(){return this.d}set delay(e){this.d=e}isScheduled(){return this.b!==-1}flush(){this.isScheduled()&&(this.cancel(),this.h())}g(){this.b=-1,this.a&&this.h()}h(){this.a?.()}},Br=class{constructor(e,t){t%1e3!==0&&console.warn(`ProcessTimeRunOnceScheduler resolution is 1s, ${t}ms is not a multiple of 1000ms.`),this.a=e,this.b=t,this.d=0,this.f=-1,this.g=this.h.bind(this)}dispose(){this.cancel(),this.a=null}cancel(){this.isScheduled()&&(clearInterval(this.f),this.f=-1)}schedule(e=this.b){e%1e3!==0&&console.warn(`ProcessTimeRunOnceScheduler resolution is 1s, ${e}ms is not a multiple of 1000ms.`),this.cancel(),this.d=Math.ceil(e/1e3),this.f=setInterval(this.g,1e3)}isScheduled(){return this.f!==-1}h(){this.d--,!(this.d>0)&&(clearInterval(this.f),this.f=-1,this.a?.())}},Uc,ai;(function(){typeof globalThis.requestIdleCallback!="function"||typeof globalThis.cancelIdleCallback!="function"?ai=(e,t,s)=>{E1(()=>{if(i)return;const r=Date.now()+15;t(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,r-Date.now())}}))});let i=!1;return{dispose(){i||(i=!0)}}}:ai=(e,t,s)=>{const i=e.requestIdleCallback(t,typeof s=="number"?{timeout:s}:void 0);let r=!1;return{dispose(){r||(r=!0,e.cancelIdleCallback(i))}}},Uc=(e,t)=>ai(globalThis,e,t)})();var zr;(function(e){e[e.Resolved=0]="Resolved",e[e.Rejected=1]="Rejected"})(zr||(zr={}));var Bc=class{get isRejected(){return this.d?.outcome===1}get isResolved(){return this.d?.outcome===0}get isSettled(){return!!this.d}get value(){return this.d?.outcome===0?this.d?.value:void 0}constructor(){this.p=new Promise((e,t)=>{this.a=e,this.b=t})}complete(e){return new Promise(t=>{this.a(e),this.d={outcome:0,value:e},t()})}error(e){return new Promise(t=>{this.b(e),this.d={outcome:1,value:e},t()})}cancel(){return this.error(new Re)}},ci;(function(e){async function t(i){let r;const n=await Promise.all(i.map(o=>o.then(a=>a,a=>{r||(r=a)})));if(typeof r<"u")throw r;return n}e.settled=t;function s(i){return new Promise(async(r,n)=>{try{await i(r,n)}catch(o){n(o)}})}e.withAsyncBody=s})(ci||(ci={}));var Wr;(function(e){e[e.Initial=0]="Initial",e[e.DoneOK=1]="DoneOK",e[e.DoneError=2]="DoneError"})(Wr||(Wr={}));var Xu=class me{static fromArray(t){return new me(s=>{s.emitMany(t)})}static fromPromise(t){return new me(async s=>{s.emitMany(await t)})}static fromPromisesResolveOrder(t){return new me(async s=>{await Promise.all(t.map(async i=>s.emitOne(await i)))})}static merge(t){return new me(async s=>{await Promise.all(t.map(async i=>{for await(const r of i)s.emitOne(r)}))})}static{this.EMPTY=me.fromArray([])}constructor(t,s){this.a=0,this.b=[],this.d=null,this.f=s,this.g=new C,queueMicrotask(async()=>{const i={emitOne:r=>this.h(r),emitMany:r=>this.j(r),reject:r=>this.l(r)};try{await Promise.resolve(t(i)),this.k()}catch(r){this.l(r)}finally{i.emitOne=void 0,i.emitMany=void 0,i.reject=void 0}})}[Symbol.asyncIterator](){let t=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(t<this.b.length)return{done:!1,value:this.b[t++]};if(this.a===1)return{done:!0,value:void 0};await K.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(t,s){return new me(async i=>{for await(const r of t)i.emitOne(s(r))})}map(t){return me.map(this,t)}static filter(t,s){return new me(async i=>{for await(const r of t)s(r)&&i.emitOne(r)})}filter(t){return me.filter(this,t)}static coalesce(t){return me.filter(t,s=>!!s)}coalesce(){return me.coalesce(this)}static async toPromise(t){const s=[];for await(const i of t)s.push(i);return s}toPromise(){return me.toPromise(this)}h(t){this.a===0&&(this.b.push(t),this.g.fire())}j(t){this.a===0&&(this.b=this.b.concat(t),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(t){this.a===0&&(this.a=2,this.d=t,this.g.fire())}};function Hr(e){return(t,s,i)=>{let r=null,n=null;if(typeof i.value=="function"?(r="value",n=i.value):typeof i.get=="function"&&(r="get",n=i.get),!n||typeof s=="symbol")throw new Error("not supported");i[r]=e(n,s)}}function U(e,t,s){let i=null,r=null;if(typeof s.value=="function"?(i="value",r=s.value,r.length!==0&&console.warn("Memoize should only be used in functions with zero parameters")):typeof s.get=="function"&&(i="get",r=s.get),!r)throw new Error("not supported");const n=`$memoize$${t}`;s[i]=function(...o){return this.hasOwnProperty(n)||Object.defineProperty(this,n,{configurable:!1,enumerable:!1,writable:!1,value:r.apply(this,o)}),this[n]}}function li(e,t,s){return Hr((i,r)=>{const n=`$debounce$${r}`,o=`$debounce$result$${r}`;return function(...a){this[o]||(this[o]=s?s():void 0),clearTimeout(this[n]),t&&(this[o]=t(this[o],...a),a=[this[o]]),this[n]=setTimeout(()=>{i.apply(this,a),this[o]=s?s():void 0},e)}})}function Vr(e,t,s){return Hr((i,r)=>{const n=`$throttle$timer$${r}`,o=`$throttle$result$${r}`,a=`$throttle$lastRun$${r}`,l=`$throttle$pending$${r}`;return function(...c){if(this[o]||(this[o]=s?s():void 0),(this[a]===null||this[a]===void 0)&&(this[a]=-Number.MAX_VALUE),t&&(this[o]=t(this[o],...c)),this[l])return;const u=this[a]+e;u<=Date.now()?(this[a]=Date.now(),i.apply(this,[this[o]]),this[o]=s?s():void 0):(this[l]=!0,this[n]=setTimeout(()=>{this[l]=!1,this[a]=Date.now(),i.apply(this,[this[o]]),this[o]=s?s():void 0},u-Date.now()))}})}function gs(e,t=0){if(!e||t>200)return e;if(typeof e=="object"){switch(e.$mid){case 1:return j.revive(e);case 2:return new RegExp(e.source,e.flags);case 17:return new Date(e.source)}if(e instanceof ye||e instanceof Uint8Array)return e;if(Array.isArray(e))for(let s=0;s<e.length;++s)e[s]=gs(e[s],t+1);else for(const s in e)Object.hasOwnProperty.call(e,s)&&(e[s]=gs(e[s],t+1))}return e}var qr;(function(e){e[e.Promise=100]="Promise",e[e.PromiseCancel=101]="PromiseCancel",e[e.EventListen=102]="EventListen",e[e.EventDispose=103]="EventDispose"})(qr||(qr={}));function ht(e){switch(e){case 100:return"req";case 101:return"cancel";case 102:return"subscribe";case 103:return"unsubscribe"}}var Gr;(function(e){e[e.Initialize=200]="Initialize",e[e.PromiseSuccess=201]="PromiseSuccess",e[e.PromiseError=202]="PromiseError",e[e.PromiseErrorObj=203]="PromiseErrorObj",e[e.EventFire=204]="EventFire"})(Gr||(Gr={}));function vs(e){switch(e){case 200:return"init";case 201:return"reply:";case 202:case 203:return"replyErr:";case 204:return"event:"}}var Ge;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Idle=1]="Idle"})(Ge||(Ge={}));function ut(e){let t=0;for(let s=0;;s+=7){const i=e.read(1);if(t|=(i.buffer[0]&127)<<s,!(i.buffer[0]&128))return t}}var zc=Be(0);function ft(e,t){if(t===0){e.write(zc);return}let s=0;for(let r=t;r!==0;r=r>>>7)s++;const i=ye.alloc(s);for(let r=0;t!==0;r++)i.buffer[r]=t&127,t=t>>>7,t>0&&(i.buffer[r]|=128);e.write(i)}var hi=class{constructor(e){this.b=e,this.a=0}read(e){const t=this.b.slice(this.a,this.a+e);return this.a+=t.byteLength,t}},Xr=class{constructor(){this.a=[]}get buffer(){return ye.concat(this.a)}write(e){this.a.push(e)}},oe;(function(e){e[e.Undefined=0]="Undefined",e[e.String=1]="String",e[e.Buffer=2]="Buffer",e[e.VSBuffer=3]="VSBuffer",e[e.Array=4]="Array",e[e.Object=5]="Object",e[e.Int=6]="Int"})(oe||(oe={}));function Be(e){const t=ye.alloc(1);return t.writeUInt8(e,0),t}var Xe={Undefined:Be(oe.Undefined),String:Be(oe.String),Buffer:Be(oe.Buffer),VSBuffer:Be(oe.VSBuffer),Array:Be(oe.Array),Object:Be(oe.Object),Uint:Be(oe.Int)},Wc=typeof Buffer<"u";function It(e,t){if(typeof t>"u")e.write(Xe.Undefined);else if(typeof t=="string"){const s=ye.fromString(t);e.write(Xe.String),ft(e,s.byteLength),e.write(s)}else if(Wc&&Buffer.isBuffer(t)){const s=ye.wrap(t);e.write(Xe.Buffer),ft(e,s.byteLength),e.write(s)}else if(t instanceof ye)e.write(Xe.VSBuffer),ft(e,t.byteLength),e.write(t);else if(Array.isArray(t)){e.write(Xe.Array),ft(e,t.length);for(const s of t)It(e,s)}else if(typeof t=="number"&&(t|0)===t)e.write(Xe.Uint),ft(e,t);else{const s=ye.fromString(JSON.stringify(t));e.write(Xe.Object),ft(e,s.byteLength),e.write(s)}}function dt(e){switch(e.read(1).readUInt8(0)){case oe.Undefined:return;case oe.String:return e.read(ut(e)).toString();case oe.Buffer:return e.read(ut(e)).buffer;case oe.VSBuffer:return e.read(ut(e));case oe.Array:{const s=ut(e),i=[];for(let r=0;r<s;r++)i.push(dt(e));return i}case oe.Object:return JSON.parse(e.read(ut(e)).toString());case oe.Int:return ut(e)}}var Jr=class{constructor(e,t,s=null,i=1e3){this.h=e,this.j=t,this.k=s,this.l=i,this.b=new Map,this.d=new Map,this.g=new Map,this.f=this.h.onMessage(r=>this.q(r)),this.m({type:200})}registerChannel(e,t){this.b.set(e,t),setTimeout(()=>this.w(e),0)}m(e){switch(e.type){case 200:{const t=this.o([e.type]);this.k?.logOutgoing(t,0,1,vs(e.type));return}case 201:case 202:case 204:case 203:{const t=this.o([e.type,e.id],e.data);this.k?.logOutgoing(t,e.id,1,vs(e.type),e.data);return}}}o(e,t=void 0){const s=new Xr;return It(s,e),It(s,t),this.p(s.buffer)}p(e){try{return this.h.send(e),e.byteLength}catch{return 0}}q(e){const t=new hi(e),s=dt(t),i=dt(t),r=s[0];switch(r){case 100:return this.k?.logIncoming(e.byteLength,s[1],1,`${ht(r)}: ${s[2]}.${s[3]}`,i),this.s({type:r,id:s[1],channelName:s[2],name:s[3],arg:i});case 102:return this.k?.logIncoming(e.byteLength,s[1],1,`${ht(r)}: ${s[2]}.${s[3]}`,i),this.t({type:r,id:s[1],channelName:s[2],name:s[3],arg:i});case 101:return this.k?.logIncoming(e.byteLength,s[1],1,`${ht(r)}`),this.u({type:r,id:s[1]});case 103:return this.k?.logIncoming(e.byteLength,s[1],1,`${ht(r)}`),this.u({type:r,id:s[1]})}}s(e){const t=this.b.get(e.channelName);if(!t){this.v(e);return}const s=new ti;let i;try{i=t.call(this.j,e.name,e.arg,s.token)}catch(o){i=Promise.reject(o)}const r=e.id;i.then(o=>{this.m({id:r,data:o,type:201})},o=>{o instanceof Error?this.m({id:r,data:{message:o.message,name:o.name,stack:o.stack?o.stack.split(`
`):void 0},type:202}):this.m({id:r,data:o,type:203})}).finally(()=>{n.dispose(),this.d.delete(e.id)});const n=be(()=>s.cancel());this.d.set(e.id,n)}t(e){const t=this.b.get(e.channelName);if(!t){this.v(e);return}const s=e.id,r=t.listen(this.j,e.name,e.arg)(n=>this.m({id:s,data:n,type:204}));this.d.set(e.id,r)}u(e){const t=this.d.get(e.id);t&&(t.dispose(),this.d.delete(e.id))}v(e){let t=this.g.get(e.channelName);t||(t=[],this.g.set(e.channelName,t));const s=setTimeout(()=>{console.error(`Unknown channel: ${e.channelName}`),e.type===100&&this.m({id:e.id,data:{name:"Unknown channel",message:`Channel name '${e.channelName}' timed out after ${this.l}ms`,stack:void 0},type:202})},this.l);t.push({request:e,timeoutTimer:s})}w(e){const t=this.g.get(e);if(t){for(const s of t)switch(clearTimeout(s.timeoutTimer),s.request.type){case 100:this.s(s.request);break;case 102:this.t(s.request);break}this.g.delete(e)}}dispose(){this.f&&(this.f.dispose(),this.f=null),Ne(this.d.values()),this.d.clear()}},Yr;(function(e){e[e.LocalSide=0]="LocalSide",e[e.OtherSide=1]="OtherSide"})(Yr||(Yr={}));var Qr=class{constructor(e,t=null){this.l=e,this.a=!1,this.b=Ge.Uninitialized,this.d=new Set,this.f=new Map,this.g=0,this.k=new C,this.onDidInitialize=this.k.event,this.h=this.l.onMessage(s=>this.s(s)),this.j=t}getChannel(e){const t=this;return{call(s,i,r){return t.a?Promise.reject(new Re):t.m(e,s,i,r)},listen(s,i){return t.a?K.None:t.o(e,s,i)}}}m(e,t,s,i=Lt.None){const r=this.g++,o={id:r,type:100,channelName:e,name:t,arg:s};if(i.isCancellationRequested)return Promise.reject(new Re);let a,l;return new Promise((u,h)=>{if(i.isCancellationRequested)return h(new Re);const f=()=>{const v=g=>{switch(g.type){case 201:this.f.delete(r),u(g.data);break;case 202:{this.f.delete(r);const b=new Error(g.data.message);b.stack=Array.isArray(g.data.stack)?g.data.stack.join(`
`):g.data.stack,b.name=g.data.name,h(b);break}case 203:this.f.delete(r),h(g.data);break}};this.f.set(r,v),this.p(o)};let d=null;this.b===Ge.Idle?f():(d=oi(v=>this.u()),d.then(()=>{d=null,f()}));const p=()=>{d?(d.cancel(),d=null):this.p({id:r,type:101}),h(new Re)};a=i.onCancellationRequested(p),l={dispose:Ws(()=>{p(),a.dispose()})},this.d.add(l)}).finally(()=>{a?.dispose(),this.d.delete(l)})}o(e,t,s){const i=this.g++,n={id:i,type:102,channelName:e,name:t,arg:s};let o=null;const a=new C({onWillAddFirstListener:()=>{const c=()=>{this.d.add(a),this.p(n)};this.b===Ge.Idle?c():(o=oi(u=>this.u()),o.then(()=>{o=null,c()}))},onDidRemoveLastListener:()=>{o?(o.cancel(),o=null):(this.d.delete(a),this.p({id:i,type:103}))}}),l=c=>a.fire(c.data);return this.f.set(i,l),a.event}p(e){switch(e.type){case 100:case 102:{const t=this.q([e.type,e.id,e.channelName,e.name],e.arg);this.j?.logOutgoing(t,e.id,0,`${ht(e.type)}: ${e.channelName}.${e.name}`,e.arg);return}case 101:case 103:{const t=this.q([e.type,e.id]);this.j?.logOutgoing(t,e.id,0,ht(e.type));return}}}q(e,t=void 0){const s=new Xr;return It(s,e),It(s,t),this.r(s.buffer)}r(e){try{return this.l.send(e),e.byteLength}catch{return 0}}s(e){const t=new hi(e),s=dt(t),i=dt(t),r=s[0];switch(r){case 200:return this.j?.logIncoming(e.byteLength,0,0,vs(r)),this.t({type:s[0]});case 201:case 202:case 204:case 203:return this.j?.logIncoming(e.byteLength,s[1],0,vs(r),i),this.t({type:s[0],id:s[1],data:i})}}t(e){if(e.type===200){this.b=Ge.Idle,this.k.fire();return}this.f.get(e.id)?.(e)}get onDidInitializePromise(){return K.toPromise(this.onDidInitialize)}u(){return this.b===Ge.Idle?Promise.resolve():this.onDidInitializePromise}dispose(){this.a=!0,this.h&&(this.h.dispose(),this.h=null),Ne(this.d.values()),this.d.clear()}};__decorate([U],Qr.prototype,"onDidInitializePromise",null);var Hc=class{get connections(){const e=[];return this.f.forEach(t=>e.push(t)),e}constructor(e,t,s){this.a=new Map,this.f=new Set,this.g=new C,this.onDidAddConnection=this.g.event,this.h=new C,this.onDidRemoveConnection=this.h.event,this.j=new He,this.j.add(e(({protocol:i,onDidClientDisconnect:r})=>{const n=K.once(i.onMessage);this.j.add(n(o=>{const a=new hi(o),l=dt(a),c=new Jr(i,l,t,s),u=new Qr(i,t);this.a.forEach((f,d)=>c.registerChannel(d,f));const h={channelServer:c,channelClient:u,ctx:l};this.f.add(h),this.g.fire(h),this.j.add(r(()=>{c.dispose(),u.dispose(),this.f.delete(h),this.h.fire(h)}))}))}))}getChannel(e,t){const s=this;return{call(i,r,n){let o;if(Vs(t)){const l=Oa(s.connections.filter(t));o=l?Promise.resolve(l):K.toPromise(K.filter(s.onDidAddConnection,t))}else o=t.routeCall(s,i,r);const a=o.then(l=>l.channelClient.getChannel(e));return Kr(a).call(i,r,n)},listen(i,r){if(Vs(t))return s.k(e,t,i,r);const n=t.routeEvent(s,i,r).then(o=>o.channelClient.getChannel(e));return Kr(n).listen(i,r)}}}k(e,t,s,i){const r=this;let n;const o=new C({onWillAddFirstListener:()=>{n=new He;const a=new ic,l=new Map,c=h=>{const d=h.channelClient.getChannel(e).listen(s,i),p=a.add(d);l.set(h,p)},u=h=>{const f=l.get(h);f&&(f.dispose(),l.delete(h))};r.connections.filter(t).forEach(c),K.filter(r.onDidAddConnection,t)(c,void 0,n),r.onDidRemoveConnection(u,void 0,n),a.event(o.fire,o,n),n.add(a)},onDidRemoveLastListener:()=>{n?.dispose(),n=void 0}});return r.j.add(o),o.event}registerChannel(e,t){this.a.set(e,t);for(const s of this.f)s.channelServer.registerChannel(e,t)}dispose(){this.j.dispose();for(const e of this.f)e.channelClient.dispose(),e.channelServer.dispose();this.f.clear(),this.a.clear(),this.g.dispose(),this.h.dispose()}};function Kr(e){return{call(t,s,i){return e.then(r=>r.call(t,s,i))},listen(t,s){const i=new rc;return e.then(r=>i.input=r.listen(t,s)),i.event}}}var bs;(function(e){function t(n,o,a){const l=n,c=a&&a.disableMarshalling,u=new Map;for(const h in l)i(h)&&u.set(h,K.buffer(l[h],!0,void 0,o));return new class{listen(h,f,d){const p=u.get(f);if(p)return p;const v=l[f];if(typeof v=="function"){if(r(f))return v.call(l,d);if(i(f))return u.set(f,K.buffer(l[f],!0,void 0,o)),u.get(f)}throw new rt(`Event not found: ${f}`)}call(h,f,d){const p=l[f];if(typeof p=="function"){if(!c&&Array.isArray(d))for(let g=0;g<d.length;g++)d[g]=gs(d[g]);let v=p.apply(l,d);return v instanceof Promise||(v=Promise.resolve(v)),v}throw new rt(`Method not found: ${f}`)}}}e.fromService=t;function s(n,o){const a=o&&o.disableMarshalling;return new Proxy({},{get(l,c){if(typeof c=="string")return o?.properties?.has(c)?o.properties.get(c):r(c)?function(u){return n.listen(c,u)}:i(c)?n.listen(c):async function(...u){let h;o&&!qa(o.context)?h=[o.context,...u]:h=u;const f=await n.call(c,h);return a?f:gs(f)};throw new rt(`Property not found: ${String(c)}`)}})}e.toService=s;function i(n){return n[0]==="o"&&n[1]==="n"&&Lr(n.charCodeAt(2))}function r(n){return/^onDynamic/.test(n)&&Lr(n.charCodeAt(9))}})(bs||(bs={}));import"child_process";function ys(e,t){if(e===t)return!0;if(e==null||t===null||t===void 0||typeof e!=typeof t||typeof e!="object"||Array.isArray(e)!==Array.isArray(t))return!1;let s,i;if(Array.isArray(e)){if(e.length!==t.length)return!1;for(s=0;s<e.length;s++)if(!ys(e[s],t[s]))return!1}else{const r=[];for(i in e)r.push(i);r.sort();const n=[];for(i in t)n.push(i);if(n.sort(),!ys(r,n))return!1;for(s=0;s<r.length;s++)if(!ys(e[r[s]],t[r[s]]))return!1}return!0}function Zr(e,t){const s=t.toLowerCase(),i=Object.keys(e).find(r=>r.toLowerCase()===s);return i?e[i]:e[t]}import{promises as en}from"fs";var tn;(function(e){e[e.stdout=0]="stdout",e[e.stderr=1]="stderr"})(tn||(tn={}));var sn;(function(e){e[e.Success=0]="Success",e[e.Unknown=1]="Unknown",e[e.AccessDenied=2]="AccessDenied",e[e.ProcessNotFound=3]="ProcessNotFound"})(sn||(sn={}));import*as V from"fs";import{tmpdir as Vc}from"os";import{promisify as Ot}from"util";var qc=new Zi(1e4);function rn(e){return Xc(e,"NFC",qc)}var ef=new Zi(1e4),Gc=/[^\u0000-\u0080]/;function Xc(e,t,s){if(!e)return e;const i=s.get(e);if(i)return i;let r;return Gc.test(e)?r=e.normalize(t):r=e,s.set(e,r),r}var _t;(function(e){e[e.UNLINK=0]="UNLINK",e[e.MOVE=1]="MOVE"})(_t||(_t={}));async function nn(e,t=_t.UNLINK,s){if(xc(e))throw new Error("rimraf - will refuse to recursively delete root");return t===_t.UNLINK?ui(e):Jc(e,s)}async function Jc(e,t=Pc(Vc())){try{try{await V.promises.rename(e,t)}catch(s){return s.code==="ENOENT"?void 0:ui(e)}ui(t).catch(s=>{})}catch(s){if(s.code!=="ENOENT")throw s}}async function ui(e){return V.promises.rm(e,{recursive:!0,force:!0,maxRetries:3})}async function ws(e,t){return Qc(await(t?Yc(e):V.promises.readdir(e)))}async function Yc(e){try{return await V.promises.readdir(e,{withFileTypes:!0})}catch(i){console.warn("[node.js fs] readdir with filetypes failed with error: ",i)}const t=[],s=await ws(e);for(const i of s){let r=!1,n=!1,o=!1;try{const a=await V.promises.lstat(O(e,i));r=a.isFile(),n=a.isDirectory(),o=a.isSymbolicLink()}catch(a){console.warn("[node.js fs] unexpected error from lstat after readdir: ",a)}t.push({name:i,isFile:()=>r,isDirectory:()=>n,isSymbolicLink:()=>o})}return t}function Qc(e){return e.map(t=>typeof t=="string"?Fe?rn(t):t:(t.name=Fe?rn(t.name):t.name,t))}async function Kc(e){const t=await ws(e),s=[];for(const i of t)await $e.existsDirectory(O(e,i))&&s.push(i);return s}var $e;(function(e){async function t(r){let n;try{if(n=await V.promises.lstat(r),!n.isSymbolicLink())return{stat:n}}catch{}try{return{stat:await V.promises.stat(r),symbolicLink:n?.isSymbolicLink()?{dangling:!1}:void 0}}catch(o){if(o.code==="ENOENT"&&n)return{stat:n,symbolicLink:{dangling:!0}};if(N&&o.code==="EACCES")try{return{stat:await V.promises.stat(await V.promises.readlink(r)),symbolicLink:{dangling:!1}}}catch(a){if(a.code==="ENOENT"&&n)return{stat:n,symbolicLink:{dangling:!0}};throw a}throw o}}e.stat=t;async function s(r){try{const{stat:n,symbolicLink:o}=await e.stat(r);return n.isFile()&&o?.dangling!==!0}catch{}return!1}e.existsFile=s;async function i(r){try{const{stat:n,symbolicLink:o}=await e.stat(r);return n.isDirectory()&&o?.dangling!==!0}catch{}return!1}e.existsDirectory=i})($e||($e={}));var Zc=new Tc;function el(e,t,s){return Zc.queueFor(j.file(e),()=>{const i=il(s);return new Promise((r,n)=>sl(e,t,i,o=>o?n(o):r()))},Oc)}var on=!0;function tl(e){on=e}function sl(e,t,s,i){if(!on)return V.writeFile(e,t,{mode:s.mode,flag:s.flag},i);V.open(e,s.flag,s.mode,(r,n)=>{if(r)return i(r);V.writeFile(n,t,o=>{if(o)return V.close(n,()=>i(o));V.fdatasync(n,a=>(a&&(console.warn("[node.js fs] fdatasync is now disabled for this session because it failed: ",a),tl(!1)),V.close(n,l=>i(l))))})})}function il(e){return e?{mode:typeof e.mode=="number"?e.mode:438,flag:typeof e.flag=="string"?e.flag:"w"}:{mode:438,flag:"w"}}async function rl(e,t,s=6e4){if(e!==t)try{N&&typeof s=="number"?await an(e,t,Date.now(),s):await V.promises.rename(e,t)}catch(i){if(e.toLowerCase()!==t.toLowerCase()&&i.code==="EXDEV"||e.endsWith("."))await cn(e,t,{preserveSymlinks:!1}),await nn(e,_t.MOVE);else throw i}}async function an(e,t,s,i,r=0){try{return await V.promises.rename(e,t)}catch(n){if(n.code!=="EACCES"&&n.code!=="EPERM"&&n.code!=="EBUSY")throw n;if(Date.now()-s>=i)throw console.error(`[node.js fs] rename failed after ${r} retries with error: ${n}`),n;if(r===0){let o=!1;try{const{stat:a}=await $e.stat(t);a.isFile()||(o=!0)}catch{}if(o)throw n}return await Ue(Math.min(100,r*10)),an(e,t,s,i,r+1)}}async function cn(e,t,s){return hn(e,t,{root:{source:e,target:t},options:s,handledSourcePaths:new Set})}var ln=511;async function hn(e,t,s){if(s.handledSourcePaths.has(e))return;s.handledSourcePaths.add(e);const{stat:i,symbolicLink:r}=await $e.stat(e);if(r){if(s.options.preserveSymlinks)try{return await al(e,t,s)}catch{}if(r.dangling)return}return i.isDirectory()?nl(e,t,i.mode&ln,s):ol(e,t,i.mode&ln)}async function nl(e,t,s,i){await V.promises.mkdir(t,{recursive:!0,mode:s});const r=await ws(e);for(const n of r)await hn(O(e,n),O(t,n),i)}async function ol(e,t,s){await V.promises.copyFile(e,t),await V.promises.chmod(t,s)}async function al(e,t,s){let i=await V.promises.readlink(e);ri(i,s.root.source,!St)&&(i=O(s.root.target,i.substr(s.root.source.length+1))),await V.promises.symlink(i,t)}var fi=new class{get read(){return(e,t,s,i,r)=>new Promise((n,o)=>{V.read(e,t,s,i,r,(a,l,c)=>a?o(a):n({bytesRead:l,buffer:c}))})}get write(){return(e,t,s,i,r)=>new Promise((n,o)=>{V.write(e,t,s,i,r,(a,l,c)=>a?o(a):n({bytesWritten:l,buffer:c}))})}get fdatasync(){return Ot(V.fdatasync)}get open(){return Ot(V.open)}get close(){return Ot(V.close)}get realpath(){return Ot(V.realpath)}get ftruncate(){return Ot(V.ftruncate)}async exists(e){try{return await V.promises.access(e),!0}catch{return!1}}get readdir(){return ws}get readDirsInDir(){return Kc}get writeFile(){return el}get rm(){return nn}get rename(){return rl}get copy(){return cn}};function cl(e=Pe){return e.comspec||"cmd.exe"}async function ll(e){if(await fi.exists(e)){let t;try{t=await en.stat(e)}catch(s){s.message.startsWith("EACCES")&&(t=await en.lstat(e))}return t?!t.isDirectory():!1}return!1}async function hl(e,t,s,i=Pe,r=ll){if(vr(e))return await r(e)?e:void 0;if(t===void 0&&(t=Pt()),Dt(e)!=="."){const l=O(t,e);return await r(l)?l:void 0}const o=Zr(i,"PATH");if(s===void 0&&nt(o)&&(s=o.split(br)),s===void 0||s.length===0){const l=O(t,e);return await r(l)?l:void 0}for(const l of s){let c;if(vr(l)?c=O(l,e):c=O(t,l,e),N){const h=(Zr(i,"PATHEXT")||".COM;.EXE;.BAT;.CMD").split(";").map(async f=>{const d=c+f;return await r(d)?d:void 0});for(const f of h){const d=await f;if(d)return d}}if(await r(c))return c}const a=O(t,e);return await r(a)?a:void 0}var ul=class extends Jr{constructor(e){super({send:t=>{try{process.send?.(t.buffer.toString("base64"))}catch{}},onMessage:K.fromNodeEventEmitter(process,"message",t=>ye.wrap(Buffer.from(t,"base64")))},e),process.once("disconnect",()=>this.dispose())}};function un(e){return!!e.parentPort}var fl=class{constructor(e){this.a=e,this.onMessage=K.fromNodeEventEmitter(this.a,"message",t=>t.data?ye.wrap(t.data):ye.alloc(0)),e.start()}send(e){this.a.postMessage(e.buffer)}disconnect(){this.a.close()}},dl=class Ko extends Hc{static b(t){Ga(un(process),"Electron Utility Process");const s=new C;return process.parentPort.on("message",i=>{if(t?.handledClientConnection(i))return;const r=i.ports.at(0);r&&s.fire(r)}),K.map(s.event,i=>({protocol:new fl(i),onDidClientDisconnect:K.fromNodeEventEmitter(i,"close")}))}constructor(t){super(Ko.b(t))}},pl=ka(Sa(),1),tf={o:w(1842,null),e:w(1843,null),t:w(1844,null)},ml={tunnel:{type:"subcommand",description:"Make the current machine accessible from vscode.dev or other machines through a secure tunnel",options:{"cli-data-dir":{type:"string",args:"dir",description:w(1845,null)},"disable-telemetry":{type:"boolean"},"telemetry-level":{type:"string"},user:{type:"subcommand",options:{login:{type:"subcommand",options:{provider:{type:"string"},"access-token":{type:"string"}}}}}}},"serve-web":{type:"subcommand",description:"Run a server that displays the editor UI in browsers.",options:{"cli-data-dir":{type:"string",args:"dir",description:w(1846,null)},"disable-telemetry":{type:"boolean"},"telemetry-level":{type:"string"}}},diff:{type:"boolean",cat:"o",alias:"d",args:["file","file"],description:w(1847,null)},merge:{type:"boolean",cat:"o",alias:"m",args:["path1","path2","base","result"],description:w(1848,null)},add:{type:"boolean",cat:"o",alias:"a",args:"folder",description:w(1849,null)},remove:{type:"boolean",cat:"o",args:"folder",description:w(1850,null)},goto:{type:"boolean",cat:"o",alias:"g",args:"file:line[:character]",description:w(1851,null)},"new-window":{type:"boolean",cat:"o",alias:"n",description:w(1852,null)},"reuse-window":{type:"boolean",cat:"o",alias:"r",description:w(1853,null)},wait:{type:"boolean",cat:"o",alias:"w",description:w(1854,null)},waitMarkerFilePath:{type:"string"},locale:{type:"string",cat:"o",args:"locale",description:w(1855,null)},"user-data-dir":{type:"string",cat:"o",args:"dir",description:w(1856,null)},profile:{type:"string",cat:"o",args:"profileName",description:w(1857,null)},help:{type:"boolean",cat:"o",alias:"h",description:w(1858,null)},"extensions-dir":{type:"string",deprecates:["extensionHomePath"],cat:"e",args:"dir",description:w(1859,null)},"extensions-download-dir":{type:"string"},"builtin-extensions-dir":{type:"string"},"list-extensions":{type:"boolean",cat:"e",description:w(1860,null)},"show-versions":{type:"boolean",cat:"e",description:w(1861,null)},category:{type:"string",allowEmptyValue:!0,cat:"e",description:w(1862,null),args:"category"},"install-extension":{type:"string[]",cat:"e",args:"ext-id | path",description:w(1863,null)},"pre-release":{type:"boolean",cat:"e",description:w(1864,null)},"uninstall-extension":{type:"string[]",cat:"e",args:"ext-id",description:w(1865,null)},"update-extensions":{type:"boolean",cat:"e",description:w(1866,null)},"enable-proposed-api":{type:"string[]",allowEmptyValue:!0,cat:"e",args:"ext-id",description:w(1867,null)},"add-mcp":{type:"string[]",cat:"o",args:"json",description:w(1868,null)},version:{type:"boolean",cat:"t",alias:"v",description:w(1869,null)},verbose:{type:"boolean",cat:"t",global:!0,description:w(1870,null)},log:{type:"string[]",cat:"t",args:"level",global:!0,description:w(1871,null)},status:{type:"boolean",alias:"s",cat:"t",description:w(1872,null)},"prof-startup":{type:"boolean",cat:"t",description:w(1873,null)},"prof-append-timers":{type:"string"},"prof-duration-markers":{type:"string[]"},"prof-duration-markers-file":{type:"string"},"no-cached-data":{type:"boolean"},"prof-startup-prefix":{type:"string"},"prof-v8-extensions":{type:"boolean"},"disable-extensions":{type:"boolean",deprecates:["disableExtensions"],cat:"t",description:w(1874,null)},"disable-extension":{type:"string[]",cat:"t",args:"ext-id",description:w(1875,null)},sync:{type:"string",cat:"t",description:w(1876,null),args:["on | off"]},"inspect-extensions":{type:"string",allowEmptyValue:!0,deprecates:["debugPluginHost"],args:"port",cat:"t",description:w(1877,null)},"inspect-brk-extensions":{type:"string",allowEmptyValue:!0,deprecates:["debugBrkPluginHost"],args:"port",cat:"t",description:w(1878,null)},"disable-lcd-text":{type:"boolean",cat:"t",description:w(1879,null)},"disable-gpu":{type:"boolean",cat:"t",description:w(1880,null)},"disable-chromium-sandbox":{type:"boolean",cat:"t",description:w(1881,null)},sandbox:{type:"boolean"},"locate-shell-integration-path":{type:"string",cat:"t",args:["shell"],description:w(1882,null)},telemetry:{type:"boolean",cat:"t",description:w(1883,null)},remote:{type:"string",allowEmptyValue:!0},"folder-uri":{type:"string[]",cat:"o",args:"uri"},"file-uri":{type:"string[]",cat:"o",args:"uri"},"locate-extension":{type:"string[]"},extensionDevelopmentPath:{type:"string[]"},extensionDevelopmentKind:{type:"string[]"},extensionTestsPath:{type:"string"},extensionEnvironment:{type:"string"},debugId:{type:"string"},debugRenderer:{type:"boolean"},"inspect-ptyhost":{type:"string",allowEmptyValue:!0},"inspect-brk-ptyhost":{type:"string",allowEmptyValue:!0},"inspect-search":{type:"string",deprecates:["debugSearch"],allowEmptyValue:!0},"inspect-brk-search":{type:"string",deprecates:["debugBrkSearch"],allowEmptyValue:!0},"inspect-sharedprocess":{type:"string",allowEmptyValue:!0},"inspect-brk-sharedprocess":{type:"string",allowEmptyValue:!0},"export-default-configuration":{type:"string"},"install-source":{type:"string"},"enable-smoke-test-driver":{type:"boolean"},logExtensionHostCommunication:{type:"boolean"},"skip-release-notes":{type:"boolean"},"skip-welcome":{type:"boolean"},"disable-telemetry":{type:"boolean"},"disable-updates":{type:"boolean"},"use-inmemory-secretstorage":{type:"boolean",deprecates:["disable-keytar"]},"password-store":{type:"string"},"disable-workspace-trust":{type:"boolean"},"disable-crash-reporter":{type:"boolean"},"crash-reporter-directory":{type:"string"},"crash-reporter-id":{type:"string"},"skip-add-to-recently-opened":{type:"boolean"},"open-url":{type:"boolean"},"file-write":{type:"boolean"},"file-chmod":{type:"boolean"},"install-builtin-extension":{type:"string[]"},force:{type:"boolean"},"do-not-sync":{type:"boolean"},"do-not-include-pack-dependencies":{type:"boolean"},trace:{type:"boolean"},"trace-memory-infra":{type:"boolean"},"trace-category-filter":{type:"string"},"trace-options":{type:"string"},"preserve-env":{type:"boolean"},"force-user-env":{type:"boolean"},"force-disable-user-env":{type:"boolean"},"open-devtools":{type:"boolean"},"disable-gpu-sandbox":{type:"boolean"},logsPath:{type:"string"},"__enable-file-policy":{type:"boolean"},editSessionId:{type:"string"},continueOn:{type:"string"},"enable-coi":{type:"boolean"},"unresponsive-sample-interval":{type:"string"},"unresponsive-sample-period":{type:"string"},"no-proxy-server":{type:"boolean"},"no-sandbox":{type:"boolean",alias:"sandbox"},"proxy-server":{type:"string"},"proxy-bypass-list":{type:"string"},"proxy-pac-url":{type:"string"},"js-flags":{type:"string"},inspect:{type:"string",allowEmptyValue:!0},"inspect-brk":{type:"string",allowEmptyValue:!0},nolazy:{type:"boolean"},"force-device-scale-factor":{type:"string"},"force-renderer-accessibility":{type:"boolean"},"ignore-certificate-errors":{type:"boolean"},"allow-insecure-localhost":{type:"boolean"},"log-net-log":{type:"string"},vmodule:{type:"string"},_urls:{type:"string[]"},"disable-dev-shm-usage":{type:"boolean"},"profile-temp":{type:"boolean"},"ozone-platform":{type:"string"},"enable-tracing":{type:"string"},"trace-startup-format":{type:"string"},"trace-startup-file":{type:"string"},"trace-startup-duration":{type:"string"},"xdg-portal-required-version":{type:"string"},_:{type:"string[]"}},gl={onUnknownOption:()=>{},onMultipleValues:()=>{},onEmptyValue:()=>{},onDeprecatedOption:()=>{}};function fn(e,t,s=gl){const i=e.find(f=>f.length>0&&f[0]!=="-"),r={},n=["_"],o=[],a={};let l;for(const f in t){const d=t[f];d.type==="subcommand"?f===i&&(l=d):(d.alias&&(r[f]=d.alias),d.type==="string"||d.type==="string[]"?(n.push(f),d.deprecates&&n.push(...d.deprecates)):d.type==="boolean"&&(o.push(f),d.deprecates&&o.push(...d.deprecates)),d.global&&(a[f]=d))}if(l&&i){const f=a;for(const g in l.options)f[g]=l.options[g];const d=e.filter(g=>g!==i),p=s.getSubcommandReporter?s.getSubcommandReporter(i):void 0,v=fn(d,f,p);return{[i]:v,_:[]}}const c=(0,pl.default)(e,{string:n,boolean:o,alias:r}),u={},h=c;u._=c._.map(f=>String(f)).filter(f=>f.length>0),delete h._;for(const f in t){const d=t[f];if(d.type==="subcommand")continue;d.alias&&delete h[d.alias];let p=h[f];if(d.deprecates)for(const v of d.deprecates)h.hasOwnProperty(v)&&(p||(p=h[v],p&&s.onDeprecatedOption(v,d.deprecationMessage||w(1884,null,f))),delete h[v]);if(typeof p<"u"){if(d.type==="string[]"){if(Array.isArray(p)||(p=[p]),!d.allowEmptyValue){const v=p.filter(g=>g.length>0);v.length!==p.length&&(s.onEmptyValue(f),p=v.length>0?v:void 0)}}else d.type==="string"&&(Array.isArray(p)?(p=p.pop(),s.onMultipleValues(f,p)):!p&&!d.allowEmptyValue&&(s.onEmptyValue(f),p=void 0));u[f]=p,d.deprecationMessage&&s.onDeprecatedOption(f,d.deprecationMessage)}delete h[f]}for(const f in h)s.onUnknownOption(f);return u}import{homedir as vl,tmpdir as bl}from"os";var yl=60,wl=yl*60,di=wl*24,rf=di*7,nf=di*30,of=di*365;function Cl(e){return e.getFullYear()+"-"+String(e.getMonth()+1).padStart(2,"0")+"-"+String(e.getDate()).padStart(2,"0")+"T"+String(e.getHours()).padStart(2,"0")+":"+String(e.getMinutes()).padStart(2,"0")+":"+String(e.getSeconds()).padStart(2,"0")+"."+(e.getMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"}var dn=/^([^.]+\..+)[:=](.+)$/,z=class{get appRoot(){return Dt(Mt.asFileUri("").fsPath)}get userHome(){return j.file(this.b.homeDir)}get userDataPath(){return this.b.userDataDir}get appSettingsHome(){return j.file(O(this.userDataPath,"User"))}get tmpDir(){return j.file(this.b.tmpDir)}get cacheHome(){return j.file(this.userDataPath)}get stateResource(){return Ce(this.appSettingsHome,"globalStorage","storage.json")}get userRoamingDataHome(){return this.appSettingsHome.with({scheme:te.vscodeUserData})}get userDataSyncHome(){return Ce(this.appSettingsHome,"sync")}get logsHome(){if(!this.args.logsPath){const e=Cl(new Date).replace(/-|:|\.\d+Z$/g,"");this.args.logsPath=O(this.userDataPath,"logs",e)}return j.file(this.args.logsPath)}get sync(){return this.args.sync}get machineSettingsResource(){return Ce(j.file(O(this.userDataPath,"Machine")),"settings.json")}get workspaceStorageHome(){return Ce(this.appSettingsHome,"workspaceStorage")}get localHistoryHome(){return Ce(this.appSettingsHome,"History")}get keyboardLayoutResource(){return Ce(this.userRoamingDataHome,"keyboardLayout.json")}get argvResource(){const e=Pe.VSCODE_PORTABLE;return e?j.file(O(e,"argv.json")):Ce(this.userHome,this.c.dataFolderName,"argv.json")}get isExtensionDevelopment(){return!!this.args.extensionDevelopmentPath}get untitledWorkspacesHome(){return j.file(O(this.userDataPath,"Workspaces"))}get builtinExtensionsPath(){const e=this.args["builtin-extensions-dir"];return e?ls(e):At(O(Mt.asFileUri("").fsPath,"..","extensions"))}get extensionsDownloadLocation(){const e=this.args["extensions-download-dir"];return e?j.file(ls(e)):j.file(O(this.userDataPath,"CachedExtensionVSIXs"))}get extensionsPath(){const e=this.args["extensions-dir"];if(e)return ls(e);const t=Pe.VSCODE_EXTENSIONS;if(t)return t;const s=Pe.VSCODE_PORTABLE;return s?O(s,"extensions"):Ce(this.userHome,this.c.dataFolderName,"extensions").fsPath}get extensionDevelopmentLocationURI(){const e=this.args.extensionDevelopmentPath;if(Array.isArray(e))return e.map(t=>/^[^:/?#]+?:\/\//.test(t)?j.parse(t):j.file(At(t)))}get extensionDevelopmentKind(){return this.args.extensionDevelopmentKind?.map(e=>e==="ui"||e==="workspace"||e==="web"?e:"workspace")}get extensionTestsLocationURI(){const e=this.args.extensionTestsPath;if(e)return/^[^:/?#]+?:\/\//.test(e)?j.parse(e):j.file(At(e))}get disableExtensions(){if(this.args["disable-extensions"])return!0;const e=this.args["disable-extension"];if(e){if(typeof e=="string")return[e];if(Array.isArray(e)&&e.length>0)return e}return!1}get debugExtensionHost(){return xl(this.args,this.isBuilt)}get debugRenderer(){return!!this.args.debugRenderer}get isBuilt(){return!Pe.VSCODE_DEV}get verbose(){return!!this.args.verbose}get logLevel(){return this.args.log?.find(e=>!dn.test(e))}get extensionLogLevel(){const e=[];for(const t of this.args.log||[]){const s=dn.exec(t);s&&s[1]&&s[2]&&e.push([s[1],s[2]])}return e.length?e:void 0}get serviceMachineIdResource(){return Ce(j.file(this.userDataPath),"machineid")}get crashReporterId(){return this.args["crash-reporter-id"]}get crashReporterDirectory(){return this.args["crash-reporter-directory"]}get disableTelemetry(){return!!this.args["disable-telemetry"]}get disableWorkspaceTrust(){return!!this.args["disable-workspace-trust"]}get useInMemorySecretStorage(){return!!this.args["use-inmemory-secretstorage"]}get policyFile(){if(this.args["__enable-file-policy"]){const e=Pe.VSCODE_PORTABLE;return e?j.file(O(e,"policy.json")):Ce(this.userHome,this.c.dataFolderName,"policy.json")}}get editSessionId(){return this.args.editSessionId}get continueOn(){return this.args.continueOn}set continueOn(e){this.args.continueOn=e}get args(){return this.a}constructor(e,t,s){this.a=e,this.b=t,this.c=s}};__decorate([U],z.prototype,"appRoot",null),__decorate([U],z.prototype,"userHome",null),__decorate([U],z.prototype,"userDataPath",null),__decorate([U],z.prototype,"appSettingsHome",null),__decorate([U],z.prototype,"tmpDir",null),__decorate([U],z.prototype,"cacheHome",null),__decorate([U],z.prototype,"stateResource",null),__decorate([U],z.prototype,"userRoamingDataHome",null),__decorate([U],z.prototype,"userDataSyncHome",null),__decorate([U],z.prototype,"sync",null),__decorate([U],z.prototype,"machineSettingsResource",null),__decorate([U],z.prototype,"workspaceStorageHome",null),__decorate([U],z.prototype,"localHistoryHome",null),__decorate([U],z.prototype,"keyboardLayoutResource",null),__decorate([U],z.prototype,"argvResource",null),__decorate([U],z.prototype,"isExtensionDevelopment",null),__decorate([U],z.prototype,"untitledWorkspacesHome",null),__decorate([U],z.prototype,"builtinExtensionsPath",null),__decorate([U],z.prototype,"extensionsPath",null),__decorate([U],z.prototype,"extensionDevelopmentLocationURI",null),__decorate([U],z.prototype,"extensionDevelopmentKind",null),__decorate([U],z.prototype,"extensionTestsLocationURI",null),__decorate([U],z.prototype,"debugExtensionHost",null),__decorate([U],z.prototype,"logLevel",null),__decorate([U],z.prototype,"extensionLogLevel",null),__decorate([U],z.prototype,"serviceMachineIdResource",null),__decorate([U],z.prototype,"disableTelemetry",null),__decorate([U],z.prototype,"disableWorkspaceTrust",null),__decorate([U],z.prototype,"useInMemorySecretStorage",null),__decorate([U],z.prototype,"policyFile",null);function xl(e,t){return El(e["inspect-extensions"],e["inspect-brk-extensions"],5870,t,e.debugId,e.extensionEnvironment)}function El(e,t,s,i,r,n){const a=Number(t||e)||(i?null:s),l=a?!!t:!1;let c;if(n)try{c=JSON.parse(n)}catch{}return{port:a,break:l,debugId:r,env:c}}import*as pn from"os";import*as ze from"path";var kl=process.env.VSCODE_CWD||process.cwd();function Sl(e,t){const s=Pl(e,t),i=[s];return ze.isAbsolute(s)||i.unshift(kl),ze.resolve(...i)}function Pl(e,t){process.env.VSCODE_DEV&&(t="code-oss-dev");const s=process.env.VSCODE_PORTABLE;if(s)return ze.join(s,"user-data");let i=process.env.VSCODE_APPDATA;if(i)return ze.join(i,t);const r=e["user-data-dir"];if(r)return r;switch(process.platform){case"win32":if(i=process.env.APPDATA,!i){const n=process.env.USERPROFILE;if(typeof n!="string")throw new Error("Windows: Unexpected undefined %USERPROFILE% environment variable");i=ze.join(n,"AppData","Roaming")}break;case"darwin":i=ze.join(pn.homedir(),"Library","Application Support");break;case"linux":i=process.env.XDG_CONFIG_HOME||ze.join(pn.homedir(),".config");break;default:throw new Error("Platform not supported")}return ze.join(i,t)}var Al=class extends z{constructor(e,t){super(e,{homeDir:vl(),tmpDir:bl(),userDataDir:Sl(e,t.nameShort)},t)}};function pi(e,t){return t&&(e.stack||e.stacktrace)?w(110,null,gn(e),mn(e.stack)||mn(e.stacktrace)):gn(e)}function mn(e){return Array.isArray(e)?e.join(`
`):e}function gn(e){return e.code==="ERR_UNC_HOST_NOT_ALLOWED"?`${e.message}. Please update the 'security.allowedUNCHosts' setting if you want to allow this host.`:typeof e.code=="string"&&typeof e.errno=="number"&&typeof e.syscall=="string"?w(111,null,e.message):e.message||w(112,null)}function vn(e=null,t=!1){if(!e)return w(113,null);if(Array.isArray(e)){const s=Ia(e),i=vn(s[0],t);return s.length>1?w(114,null,i,s.length):i}if(nt(e))return e;if(e.detail){const s=e.detail;if(s.error)return pi(s.error,t);if(s.exception)return pi(s.exception,t)}return e.stack?pi(e,t):e.message?e.message:w(115,null)}function Dl(e){return mi(e,0)}function mi(e,t){switch(typeof e){case"object":return e===null?Me(349,t):Array.isArray(e)?$l(e,t):Ml(e,t);case"string":return bn(e,t);case"boolean":return Ll(e,t);case"number":return Me(e,t);case"undefined":return Me(937,t);default:return Me(617,t)}}function Me(e,t){return(t<<5)-t+e|0}function Ll(e,t){return Me(e?433:863,t)}function bn(e,t){t=Me(149417,t);for(let s=0,i=e.length;s<i;s++)t=Me(e.charCodeAt(s),t);return t}function $l(e,t){return t=Me(104579,t),e.reduce((s,i)=>mi(i,s),t)}function Ml(e,t){return t=Me(181387,t),Object.keys(e).sort().reduce((s,i)=>(s=bn(i,s),mi(e[i],s)),t)}var yn;(function(e){e[e.BLOCK_SIZE=64]="BLOCK_SIZE",e[e.UNICODE_REPLACEMENT=65533]="UNICODE_REPLACEMENT"})(yn||(yn={}));function gi(e,t,s=32){const i=s-t,r=~((1<<i)-1);return(e<<t|(r&e)>>>i)>>>0}function Rt(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(s=>s.toString(16).padStart(2,"0")).join(""):(e>>>0).toString(16).padStart(t/4,"0")}var af=class Zo{static{this.g=new DataView(new ArrayBuffer(320))}constructor(){this.h=1732584193,this.l=4023233417,this.m=2562383102,this.n=271733878,this.o=3285377520,this.p=new Uint8Array(67),this.q=new DataView(this.p.buffer),this.r=0,this.t=0,this.u=0,this.v=!1}update(t){const s=t.length;if(s===0)return;const i=this.p;let r=this.r,n=this.u,o,a;for(n!==0?(o=n,a=-1,n=0):(o=t.charCodeAt(0),a=0);;){let l=o;if(uc(o))if(a+1<s){const c=t.charCodeAt(a+1);$r(c)?(a++,l=fc(o,c)):l=65533}else{n=o;break}else $r(o)&&(l=65533);if(r=this.w(i,r,l),a++,a<s)o=t.charCodeAt(a);else break}this.r=r,this.u=n}w(t,s,i){return i<128?t[s++]=i:i<2048?(t[s++]=192|(i&1984)>>>6,t[s++]=128|(i&63)>>>0):i<65536?(t[s++]=224|(i&61440)>>>12,t[s++]=128|(i&4032)>>>6,t[s++]=128|(i&63)>>>0):(t[s++]=240|(i&1835008)>>>18,t[s++]=128|(i&258048)>>>12,t[s++]=128|(i&4032)>>>6,t[s++]=128|(i&63)>>>0),s>=64&&(this.y(),s-=64,this.t+=64,t[0]=t[64],t[1]=t[65],t[2]=t[66]),s}digest(){return this.v||(this.v=!0,this.u&&(this.u=0,this.r=this.w(this.p,this.r,65533)),this.t+=this.r,this.x()),Rt(this.h)+Rt(this.l)+Rt(this.m)+Rt(this.n)+Rt(this.o)}x(){this.p[this.r++]=128,this.p.subarray(this.r).fill(0),this.r>56&&(this.y(),this.p.fill(0));const t=8*this.t;this.q.setUint32(56,Math.floor(t/4294967296),!1),this.q.setUint32(60,t%4294967296,!1),this.y()}y(){const t=Zo.g,s=this.q;for(let h=0;h<64;h+=4)t.setUint32(h,s.getUint32(h,!1),!1);for(let h=64;h<320;h+=4)t.setUint32(h,gi(t.getUint32(h-12,!1)^t.getUint32(h-32,!1)^t.getUint32(h-56,!1)^t.getUint32(h-64,!1),1),!1);let i=this.h,r=this.l,n=this.m,o=this.n,a=this.o,l,c,u;for(let h=0;h<80;h++)h<20?(l=r&n|~r&o,c=1518500249):h<40?(l=r^n^o,c=1859775393):h<60?(l=r&n|r&o|n&o,c=2400959708):(l=r^n^o,c=3395469782),u=gi(i,5)+l+a+c+t.getUint32(h*4,!1)&4294967295,a=o,o=n,n=gi(r,30),r=i,i=u;this.h=this.h+i&4294967295,this.l=this.l+r&4294967295,this.m=this.m+n&4294967295,this.n=this.n+o&4294967295,this.o=this.o+a&4294967295}},wn;(function(e){e[e.LParen=0]="LParen",e[e.RParen=1]="RParen",e[e.Neg=2]="Neg",e[e.Eq=3]="Eq",e[e.NotEq=4]="NotEq",e[e.Lt=5]="Lt",e[e.LtEq=6]="LtEq",e[e.Gt=7]="Gt",e[e.GtEq=8]="GtEq",e[e.RegexOp=9]="RegexOp",e[e.RegexStr=10]="RegexStr",e[e.True=11]="True",e[e.False=12]="False",e[e.In=13]="In",e[e.Not=14]="Not",e[e.And=15]="And",e[e.Or=16]="Or",e[e.Str=17]="Str",e[e.QuotedStr=18]="QuotedStr",e[e.Error=19]="Error",e[e.EOF=20]="EOF"})(wn||(wn={}));function vi(...e){switch(e.length){case 1:return w(1822,null,e[0]);case 2:return w(1823,null,e[0],e[1]);case 3:return w(1824,null,e[0],e[1],e[2]);default:return}}var Il=w(1825,null),Ol=w(1826,null),Nt=class Bi{constructor(){this.c="",this.d=0,this.e=0,this.f=[],this.g=[],this.m=/[a-zA-Z0-9_<>\-\./\\:\*\?\+\[\]\^,#@;"%\$\p{L}-]+/uy}static getLexeme(t){switch(t.type){case 0:return"(";case 1:return")";case 2:return"!";case 3:return t.isTripleEq?"===":"==";case 4:return t.isTripleEq?"!==":"!=";case 5:return"<";case 6:return"<=";case 7:return">=";case 8:return">=";case 9:return"=~";case 10:return t.lexeme;case 11:return"true";case 12:return"false";case 13:return"in";case 14:return"not";case 15:return"&&";case 16:return"||";case 17:return t.lexeme;case 18:return t.lexeme;case 19:return t.lexeme;case 20:return"EOF";default:throw La(`unhandled token type: ${JSON.stringify(t)}; have you forgotten to add a case?`)}}static{this.a=new Set(["i","g","s","m","y","u"].map(t=>t.charCodeAt(0)))}static{this.b=new Map([["not",14],["in",13],["false",12],["true",11]])}get errors(){return this.g}reset(t){return this.c=t,this.d=0,this.e=0,this.f=[],this.g=[],this}scan(){for(;!this.r();)switch(this.d=this.e,this.i()){case 40:this.k(0);break;case 41:this.k(1);break;case 33:if(this.h(61)){const s=this.h(61);this.f.push({type:4,offset:this.d,isTripleEq:s})}else this.k(2);break;case 39:this.o();break;case 47:this.q();break;case 61:if(this.h(61)){const s=this.h(61);this.f.push({type:3,offset:this.d,isTripleEq:s})}else this.h(126)?this.k(9):this.l(vi("==","=~"));break;case 60:this.k(this.h(61)?6:5);break;case 62:this.k(this.h(61)?8:7);break;case 38:this.h(38)?this.k(15):this.l(vi("&&"));break;case 124:this.h(124)?this.k(16):this.l(vi("||"));break;case 32:case 13:case 9:case 10:case 160:break;default:this.n()}return this.d=this.e,this.k(20),Array.from(this.f)}h(t){return this.r()||this.c.charCodeAt(this.e)!==t?!1:(this.e++,!0)}i(){return this.c.charCodeAt(this.e++)}j(){return this.r()?0:this.c.charCodeAt(this.e)}k(t){this.f.push({type:t,offset:this.d})}l(t){const s=this.d,i=this.c.substring(this.d,this.e),r={type:19,offset:this.d,lexeme:i};this.g.push({offset:s,lexeme:i,additionalInfo:t}),this.f.push(r)}n(){this.m.lastIndex=this.d;const t=this.m.exec(this.c);if(t){this.e=this.d+t[0].length;const s=this.c.substring(this.d,this.e),i=Bi.b.get(s);i?this.k(i):this.f.push({type:17,lexeme:s,offset:this.d})}}o(){for(;this.j()!==39&&!this.r();)this.i();if(this.r()){this.l(Il);return}this.i(),this.f.push({type:18,lexeme:this.c.substring(this.d+1,this.e-1),offset:this.d+1})}q(){let t=this.e,s=!1,i=!1;for(;;){if(t>=this.c.length){this.e=t,this.l(Ol);return}const n=this.c.charCodeAt(t);if(s)s=!1;else if(n===47&&!i){t++;break}else n===91?i=!0:n===92?s=!0:n===93&&(i=!1);t++}for(;t<this.c.length&&Bi.a.has(this.c.charCodeAt(t));)t++;this.e=t;const r=this.c.substring(this.d,this.e);this.f.push({type:10,lexeme:r,offset:this.d})}r(){return this.e>=this.c.length}},Ie;(function(e){e.serviceIds=new Map,e.DI_TARGET="$di$target",e.DI_DEPENDENCIES="$di$dependencies";function t(s){return s[e.DI_DEPENDENCIES]||[]}e.getServiceDependencies=t})(Ie||(Ie={}));var cf=Oe("instantiationService");function _l(e,t,s){t[Ie.DI_TARGET]===t?t[Ie.DI_DEPENDENCIES].push({id:e,index:s}):(t[Ie.DI_DEPENDENCIES]=[{id:e,index:s}],t[Ie.DI_TARGET]=t)}function Oe(e){if(Ie.serviceIds.has(e))return Ie.serviceIds.get(e);const t=function(s,i,r){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");_l(t,s,r)};return t.toString=()=>e,Ie.serviceIds.set(e,t),t}var se=new Map;se.set("false",!1),se.set("true",!0),se.set("isMac",Fe),se.set("isLinux",St),se.set("isWindows",N),se.set("isWeb",Ys),se.set("isMacNative",Fe&&!Ys),se.set("isEdge",P1),se.set("isFirefox",k1),se.set("isChrome",dr),se.set("isSafari",S1);var Rl=Object.prototype.hasOwnProperty,Cn;(function(e){e[e.False=0]="False",e[e.True=1]="True",e[e.Defined=2]="Defined",e[e.Not=3]="Not",e[e.Equals=4]="Equals",e[e.NotEquals=5]="NotEquals",e[e.And=6]="And",e[e.Regex=7]="Regex",e[e.NotRegex=8]="NotRegex",e[e.Or=9]="Or",e[e.In=10]="In",e[e.NotIn=11]="NotIn",e[e.Greater=12]="Greater",e[e.GreaterEquals=13]="GreaterEquals",e[e.Smaller=14]="Smaller",e[e.SmallerEquals=15]="SmallerEquals"})(Cn||(Cn={}));var Nl={regexParsingWithErrorRecovery:!0},Fl=w(1802,null),Tl=w(1803,null),jl=w(1804,null),xn=w(1805,null),Ul=w(1806,null),Bl=w(1807,null),zl=w(1808,null),Wl=w(1809,null),Hl=class Zt{static{this.c=new Error}get lexingErrors(){return this.d.errors}get parsingErrors(){return this.h}constructor(t=Nl){this.k=t,this.d=new Nt,this.f=[],this.g=0,this.h=[],this.v=/g|y/g}parse(t){if(t===""){this.h.push({message:Fl,offset:0,lexeme:"",additionalInfo:Tl});return}this.f=this.d.reset(t).scan(),this.g=0,this.h=[];try{const s=this.l();if(!this.E()){const i=this.D(),r=i.type===17?Bl:void 0;throw this.h.push({message:Ul,offset:i.offset,lexeme:Nt.getLexeme(i),additionalInfo:r}),Zt.c}return s}catch(s){if(s!==Zt.c)throw s;return}}l(){return this.m()}m(){const t=[this.o()];for(;this.y(16);){const s=this.o();t.push(s)}return t.length===1?t[0]:ae.or(...t)}o(){const t=[this.s()];for(;this.y(15);){const s=this.s();t.push(s)}return t.length===1?t[0]:ae.and(...t)}s(){if(this.y(2)){const t=this.D();switch(t.type){case 11:return this.z(),ce.INSTANCE;case 12:return this.z(),fe.INSTANCE;case 0:{this.z();const s=this.l();return this.A(1,xn),s?.negate()}case 17:return this.z(),jt.create(t.lexeme);default:throw this.B("KEY | true | false | '(' expression ')'",t)}}return this.t()}t(){const t=this.D();switch(t.type){case 11:return this.z(),ae.true();case 12:return this.z(),ae.false();case 0:{this.z();const s=this.l();return this.A(1,xn),s}case 17:{const s=t.lexeme;if(this.z(),this.y(9)){const r=this.D();if(!this.k.regexParsingWithErrorRecovery){if(this.z(),r.type!==10)throw this.B("REGEX",r);const n=r.lexeme,o=n.lastIndexOf("/"),a=o===n.length-1?void 0:this.w(n.substring(o+1));let l;try{l=new RegExp(n.substring(1,o),a)}catch{throw this.B("REGEX",r)}return Ei.create(s,l)}switch(r.type){case 10:case 19:{const n=[r.lexeme];this.z();let o=this.D(),a=0;for(let f=0;f<r.lexeme.length;f++)r.lexeme.charCodeAt(f)===40?a++:r.lexeme.charCodeAt(f)===41&&a--;for(;!this.E()&&o.type!==15&&o.type!==16;){switch(o.type){case 0:a++;break;case 1:a--;break;case 10:case 18:for(let f=0;f<o.lexeme.length;f++)o.lexeme.charCodeAt(f)===40?a++:r.lexeme.charCodeAt(f)===41&&a--}if(a<0)break;n.push(Nt.getLexeme(o)),this.z(),o=this.D()}const l=n.join(""),c=l.lastIndexOf("/"),u=c===l.length-1?void 0:this.w(l.substring(c+1));let h;try{h=new RegExp(l.substring(1,c),u)}catch{throw this.B("REGEX",r)}return ae.regex(s,h)}case 18:{const n=r.lexeme;this.z();let o=null;if(!ac(n)){const a=n.indexOf("/"),l=n.lastIndexOf("/");if(a!==l&&a>=0){const c=n.slice(a+1,l),u=n[l+1]==="i"?"i":"";try{o=new RegExp(c,u)}catch{throw this.B("REGEX",r)}}}if(o===null)throw this.B("REGEX",r);return Ei.create(s,o)}default:throw this.B("REGEX",this.D())}}if(this.y(14)){this.A(13,jl);const r=this.u();return ae.notIn(s,r)}switch(this.D().type){case 3:{this.z();const r=this.u();if(this.x().type===18)return ae.equals(s,r);switch(r){case"true":return ae.has(s);case"false":return ae.not(s);default:return ae.equals(s,r)}}case 4:{this.z();const r=this.u();if(this.x().type===18)return ae.notEquals(s,r);switch(r){case"true":return ae.not(s);case"false":return ae.has(s);default:return ae.notEquals(s,r)}}case 5:return this.z(),Ci.create(s,this.u());case 6:return this.z(),xi.create(s,this.u());case 7:return this.z(),xs.create(s,this.u());case 8:return this.z(),wi.create(s,this.u());case 13:return this.z(),ae.in(s,this.u());default:return ae.has(s)}}case 20:throw this.h.push({message:zl,offset:t.offset,lexeme:"",additionalInfo:Wl}),Zt.c;default:throw this.B(`true | false | KEY 
	| KEY '=~' REGEX 
	| KEY ('==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not' 'in') value`,this.D())}}u(){const t=this.D();switch(t.type){case 17:case 18:return this.z(),t.lexeme;case 11:return this.z(),"true";case 12:return this.z(),"false";case 13:return this.z(),"in";default:return""}}w(t){return t.replaceAll(this.v,"")}x(){return this.f[this.g-1]}y(t){return this.C(t)?(this.z(),!0):!1}z(){return this.E()||this.g++,this.x()}A(t,s){if(this.C(t))return this.z();throw this.B(s,this.D())}B(t,s,i){const r=w(1810,null,t,Nt.getLexeme(s)),n=s.offset,o=Nt.getLexeme(s);return this.h.push({message:r,offset:n,lexeme:o,additionalInfo:i}),Zt.c}C(t){return this.D().type===t}D(){return this.f[this.g]}E(){return this.D().type===20}},ae=class{static false(){return ce.INSTANCE}static true(){return fe.INSTANCE}static has(e){return Tt.create(e)}static equals(e,t){return bi.create(e,t)}static notEquals(e,t){return yi.create(e,t)}static regex(e,t){return Ei.create(e,t)}static in(e,t){return En.create(e,t)}static notIn(e,t){return kn.create(e,t)}static not(e){return jt.create(e)}static and(...e){return Pn.create(e,null,!0)}static or(...e){return ki.create(e,null,!0)}static greater(e,t){return xs.create(e,t)}static greaterEquals(e,t){return wi.create(e,t)}static smaller(e,t){return Ci.create(e,t)}static smallerEquals(e,t){return xi.create(e,t)}static{this.c=new Hl({regexParsingWithErrorRecovery:!1})}static deserialize(e){return e==null?void 0:this.c.parse(e)}};function Ft(e,t){return e.cmp(t)}var ce=class ea{static{this.INSTANCE=new ea}constructor(){this.type=0}cmp(t){return this.type-t.type}equals(t){return t.type===this.type}substituteConstants(){return this}evaluate(t){return!1}serialize(){return"false"}keys(){return[]}map(t){return this}negate(){return fe.INSTANCE}},fe=class ta{static{this.INSTANCE=new ta}constructor(){this.type=1}cmp(t){return this.type-t.type}equals(t){return t.type===this.type}substituteConstants(){return this}evaluate(t){return!0}serialize(){return"true"}keys(){return[]}map(t){return this}negate(){return ce.INSTANCE}},Tt=class sa{static create(t,s=null){const i=se.get(t);return typeof i=="boolean"?i?fe.INSTANCE:ce.INSTANCE:new sa(t,s)}constructor(t,s){this.key=t,this.c=s,this.type=2}cmp(t){return t.type!==this.type?this.type-t.type:Dn(this.key,t.key)}equals(t){return t.type===this.type?this.key===t.key:!1}substituteConstants(){const t=se.get(this.key);return typeof t=="boolean"?t?fe.INSTANCE:ce.INSTANCE:this}evaluate(t){return!!t.getValue(this.key)}serialize(){return this.key}keys(){return[this.key]}map(t){return t.mapDefined(this.key)}negate(){return this.c||(this.c=jt.create(this.key,this)),this.c}},bi=class ia{static create(t,s,i=null){if(typeof s=="boolean")return s?Tt.create(t,i):jt.create(t,i);const r=se.get(t);return typeof r=="boolean"?s===(r?"true":"false")?fe.INSTANCE:ce.INSTANCE:new ia(t,s,i)}constructor(t,s,i){this.c=t,this.d=s,this.f=i,this.type=4}cmp(t){return t.type!==this.type?this.type-t.type:Je(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){const t=se.get(this.c);if(typeof t=="boolean"){const s=t?"true":"false";return this.d===s?fe.INSTANCE:ce.INSTANCE}return this}evaluate(t){return t.getValue(this.c)==this.d}serialize(){return`${this.c} == '${this.d}'`}keys(){return[this.c]}map(t){return t.mapEquals(this.c,this.d)}negate(){return this.f||(this.f=yi.create(this.c,this.d,this)),this.f}},En=class ra{static create(t,s){return new ra(t,s)}constructor(t,s){this.d=t,this.f=s,this.type=10,this.c=null}cmp(t){return t.type!==this.type?this.type-t.type:Je(this.d,this.f,t.d,t.f)}equals(t){return t.type===this.type?this.d===t.d&&this.f===t.f:!1}substituteConstants(){return this}evaluate(t){const s=t.getValue(this.f),i=t.getValue(this.d);return Array.isArray(s)?s.includes(i):typeof i=="string"&&typeof s=="object"&&s!==null?Rl.call(s,i):!1}serialize(){return`${this.d} in '${this.f}'`}keys(){return[this.d,this.f]}map(t){return t.mapIn(this.d,this.f)}negate(){return this.c||(this.c=kn.create(this.d,this.f)),this.c}},kn=class na{static create(t,s){return new na(t,s)}constructor(t,s){this.d=t,this.f=s,this.type=11,this.c=En.create(t,s)}cmp(t){return t.type!==this.type?this.type-t.type:this.c.cmp(t.c)}equals(t){return t.type===this.type?this.c.equals(t.c):!1}substituteConstants(){return this}evaluate(t){return!this.c.evaluate(t)}serialize(){return`${this.d} not in '${this.f}'`}keys(){return this.c.keys()}map(t){return t.mapNotIn(this.d,this.f)}negate(){return this.c}},yi=class oa{static create(t,s,i=null){if(typeof s=="boolean")return s?jt.create(t,i):Tt.create(t,i);const r=se.get(t);return typeof r=="boolean"?s===(r?"true":"false")?ce.INSTANCE:fe.INSTANCE:new oa(t,s,i)}constructor(t,s,i){this.c=t,this.d=s,this.f=i,this.type=5}cmp(t){return t.type!==this.type?this.type-t.type:Je(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){const t=se.get(this.c);if(typeof t=="boolean"){const s=t?"true":"false";return this.d===s?ce.INSTANCE:fe.INSTANCE}return this}evaluate(t){return t.getValue(this.c)!=this.d}serialize(){return`${this.c} != '${this.d}'`}keys(){return[this.c]}map(t){return t.mapNotEquals(this.c,this.d)}negate(){return this.f||(this.f=bi.create(this.c,this.d,this)),this.f}},jt=class aa{static create(t,s=null){const i=se.get(t);return typeof i=="boolean"?i?ce.INSTANCE:fe.INSTANCE:new aa(t,s)}constructor(t,s){this.c=t,this.d=s,this.type=3}cmp(t){return t.type!==this.type?this.type-t.type:Dn(this.c,t.c)}equals(t){return t.type===this.type?this.c===t.c:!1}substituteConstants(){const t=se.get(this.c);return typeof t=="boolean"?t?ce.INSTANCE:fe.INSTANCE:this}evaluate(t){return!t.getValue(this.c)}serialize(){return`!${this.c}`}keys(){return[this.c]}map(t){return t.mapNot(this.c)}negate(){return this.d||(this.d=Tt.create(this.c,this)),this.d}};function Cs(e,t){if(typeof e=="string"){const s=parseFloat(e);isNaN(s)||(e=s)}return typeof e=="string"||typeof e=="number"?t(e):ce.INSTANCE}var xs=class ca{static create(t,s,i=null){return Cs(s,r=>new ca(t,r,i))}constructor(t,s,i){this.c=t,this.d=s,this.f=i,this.type=12}cmp(t){return t.type!==this.type?this.type-t.type:Je(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))>this.d}serialize(){return`${this.c} > ${this.d}`}keys(){return[this.c]}map(t){return t.mapGreater(this.c,this.d)}negate(){return this.f||(this.f=xi.create(this.c,this.d,this)),this.f}},wi=class la{static create(t,s,i=null){return Cs(s,r=>new la(t,r,i))}constructor(t,s,i){this.c=t,this.d=s,this.f=i,this.type=13}cmp(t){return t.type!==this.type?this.type-t.type:Je(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))>=this.d}serialize(){return`${this.c} >= ${this.d}`}keys(){return[this.c]}map(t){return t.mapGreaterEquals(this.c,this.d)}negate(){return this.f||(this.f=Ci.create(this.c,this.d,this)),this.f}},Ci=class ha{static create(t,s,i=null){return Cs(s,r=>new ha(t,r,i))}constructor(t,s,i){this.c=t,this.d=s,this.f=i,this.type=14}cmp(t){return t.type!==this.type?this.type-t.type:Je(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))<this.d}serialize(){return`${this.c} < ${this.d}`}keys(){return[this.c]}map(t){return t.mapSmaller(this.c,this.d)}negate(){return this.f||(this.f=wi.create(this.c,this.d,this)),this.f}},xi=class ua{static create(t,s,i=null){return Cs(s,r=>new ua(t,r,i))}constructor(t,s,i){this.c=t,this.d=s,this.f=i,this.type=15}cmp(t){return t.type!==this.type?this.type-t.type:Je(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))<=this.d}serialize(){return`${this.c} <= ${this.d}`}keys(){return[this.c]}map(t){return t.mapSmallerEquals(this.c,this.d)}negate(){return this.f||(this.f=xs.create(this.c,this.d,this)),this.f}},Ei=class fa{static create(t,s){return new fa(t,s)}constructor(t,s){this.d=t,this.f=s,this.type=7,this.c=null}cmp(t){if(t.type!==this.type)return this.type-t.type;if(this.d<t.d)return-1;if(this.d>t.d)return 1;const s=this.f?this.f.source:"",i=t.f?t.f.source:"";return s<i?-1:s>i?1:0}equals(t){if(t.type===this.type){const s=this.f?this.f.source:"",i=t.f?t.f.source:"";return this.d===t.d&&s===i}return!1}substituteConstants(){return this}evaluate(t){const s=t.getValue(this.d);return this.f?this.f.test(s):!1}serialize(){const t=this.f?`/${this.f.source}/${this.f.flags}`:"/invalid/";return`${this.d} =~ ${t}`}keys(){return[this.d]}map(t){return t.mapRegex(this.d,this.f)}negate(){return this.c||(this.c=Vl.create(this)),this.c}},Vl=class zi{static create(t){return new zi(t)}constructor(t){this.c=t,this.type=8}cmp(t){return t.type!==this.type?this.type-t.type:this.c.cmp(t.c)}equals(t){return t.type===this.type?this.c.equals(t.c):!1}substituteConstants(){return this}evaluate(t){return!this.c.evaluate(t)}serialize(){return`!(${this.c.serialize()})`}keys(){return this.c.keys()}map(t){return new zi(this.c.map(t))}negate(){return this.c}};function Sn(e){let t=null;for(let s=0,i=e.length;s<i;s++){const r=e[s].substituteConstants();if(e[s]!==r&&t===null){t=[];for(let n=0;n<s;n++)t[n]=e[n]}t!==null&&(t[s]=r)}return t===null?e:t}var Pn=class vt{static create(t,s,i){return vt.d(t,s,i)}constructor(t,s){this.expr=t,this.c=s,this.type=6}cmp(t){if(t.type!==this.type)return this.type-t.type;if(this.expr.length<t.expr.length)return-1;if(this.expr.length>t.expr.length)return 1;for(let s=0,i=this.expr.length;s<i;s++){const r=Ft(this.expr[s],t.expr[s]);if(r!==0)return r}return 0}equals(t){if(t.type===this.type){if(this.expr.length!==t.expr.length)return!1;for(let s=0,i=this.expr.length;s<i;s++)if(!this.expr[s].equals(t.expr[s]))return!1;return!0}return!1}substituteConstants(){const t=Sn(this.expr);return t===this.expr?this:vt.create(t,this.c,!1)}evaluate(t){for(let s=0,i=this.expr.length;s<i;s++)if(!this.expr[s].evaluate(t))return!1;return!0}static d(t,s,i){const r=[];let n=!1;for(const o of t)if(o){if(o.type===1){n=!0;continue}if(o.type===0)return ce.INSTANCE;if(o.type===6){r.push(...o.expr);continue}r.push(o)}if(r.length===0&&n)return fe.INSTANCE;if(r.length!==0){if(r.length===1)return r[0];r.sort(Ft);for(let o=1;o<r.length;o++)r[o-1].equals(r[o])&&(r.splice(o,1),o--);if(r.length===1)return r[0];for(;r.length>1;){const o=r[r.length-1];if(o.type!==9)break;r.pop();const a=r.pop(),l=r.length===0,c=ki.create(o.expr.map(u=>vt.create([u,a],null,i)),null,l);c&&(r.push(c),r.sort(Ft))}if(r.length===1)return r[0];if(i){for(let o=0;o<r.length;o++)for(let a=o+1;a<r.length;a++)if(r[o].negate().equals(r[a]))return ce.INSTANCE;if(r.length===1)return r[0]}return new vt(r,s)}}serialize(){return this.expr.map(t=>t.serialize()).join(" && ")}keys(){const t=[];for(const s of this.expr)t.push(...s.keys());return t}map(t){return new vt(this.expr.map(s=>s.map(t)),null)}negate(){if(!this.c){const t=[];for(const s of this.expr)t.push(s.negate());this.c=ki.create(t,this,!0)}return this.c}},ki=class et{static create(t,s,i){return et.d(t,s,i)}constructor(t,s){this.expr=t,this.c=s,this.type=9}cmp(t){if(t.type!==this.type)return this.type-t.type;if(this.expr.length<t.expr.length)return-1;if(this.expr.length>t.expr.length)return 1;for(let s=0,i=this.expr.length;s<i;s++){const r=Ft(this.expr[s],t.expr[s]);if(r!==0)return r}return 0}equals(t){if(t.type===this.type){if(this.expr.length!==t.expr.length)return!1;for(let s=0,i=this.expr.length;s<i;s++)if(!this.expr[s].equals(t.expr[s]))return!1;return!0}return!1}substituteConstants(){const t=Sn(this.expr);return t===this.expr?this:et.create(t,this.c,!1)}evaluate(t){for(let s=0,i=this.expr.length;s<i;s++)if(this.expr[s].evaluate(t))return!0;return!1}static d(t,s,i){let r=[],n=!1;if(t){for(let o=0,a=t.length;o<a;o++){const l=t[o];if(l){if(l.type===0){n=!0;continue}if(l.type===1)return fe.INSTANCE;if(l.type===9){r=r.concat(l.expr);continue}r.push(l)}}if(r.length===0&&n)return ce.INSTANCE;r.sort(Ft)}if(r.length!==0){if(r.length===1)return r[0];for(let o=1;o<r.length;o++)r[o-1].equals(r[o])&&(r.splice(o,1),o--);if(r.length===1)return r[0];if(i){for(let o=0;o<r.length;o++)for(let a=o+1;a<r.length;a++)if(r[o].negate().equals(r[a]))return fe.INSTANCE;if(r.length===1)return r[0]}return new et(r,s)}}serialize(){return this.expr.map(t=>t.serialize()).join(" || ")}keys(){const t=[];for(const s of this.expr)t.push(...s.keys());return t}map(t){return new et(this.expr.map(s=>s.map(t)),null)}negate(){if(!this.c){const t=[];for(const s of this.expr)t.push(s.negate());for(;t.length>1;){const s=t.shift(),i=t.shift(),r=[];for(const n of Ln(s))for(const o of Ln(i))r.push(Pn.create([n,o],null,!1));t.unshift(et.create(r,null,!1))}this.c=et.create(t,this,!0)}return this.c}},An=class Fs extends Tt{static{this.d=[]}static all(){return Fs.d.values()}constructor(t,s,i){super(t,null),this.f=s,typeof i=="object"?Fs.d.push({...i,key:t}):i!==!0&&Fs.d.push({key:t,description:i,type:s!=null?typeof s:void 0})}bindTo(t){return t.createKey(this.key,this.f)}getValue(t){return t.getContextKeyValue(this.key)}toNegated(){return this.negate()}isEqualTo(t){return bi.create(this.key,t)}notEqualsTo(t){return yi.create(this.key,t)}greater(t){return xs.create(this.key,t)}},lf=Oe("contextKeyService");function Dn(e,t){return e<t?-1:e>t?1:0}function Je(e,t,s,i){return e<s?-1:e>s?1:t<i?-1:t>i?1:0}function Ln(e){return e.type===9?e.expr:[e]}var Ye=Oe("logService"),hf=Oe("loggerService");function $n(e){return tr(e)}var A;(function(e){e[e.Off=0]="Off",e[e.Trace=1]="Trace",e[e.Debug=2]="Debug",e[e.Info=3]="Info",e[e.Warning=4]="Warning",e[e.Error=5]="Error"})(A||(A={}));var Mn=A.Info;function ql(e,t){return e!==A.Off&&e<=t}function pt(e,t=!1){let s="";for(let i=0;i<e.length;i++){let r=e[i];if(r instanceof Error&&(r=vn(r,t)),typeof r=="object")try{r=JSON.stringify(r)}catch{}s+=(i>0?" ":"")+r}return s}var In=class extends G{constructor(){super(...arguments),this.b=Mn,this.c=this.B(new C),this.onDidChangeLogLevel=this.c.event}setLevel(e){this.b!==e&&(this.b=e,this.c.fire(this.b))}getLevel(){return this.b}f(e){return ql(this.b,e)}g(e){return this.q.isDisposed?!1:this.f(e)}},Gl=class extends In{constructor(e){super(),this.h=e}f(e){return this.h||super.f(e)}trace(e,...t){this.g(A.Trace)&&this.m(A.Trace,pt([e,...t],!0))}debug(e,...t){this.g(A.Debug)&&this.m(A.Debug,pt([e,...t]))}info(e,...t){this.g(A.Info)&&this.m(A.Info,pt([e,...t]))}warn(e,...t){this.g(A.Warning)&&this.m(A.Warning,pt([e,...t]))}error(e,...t){if(this.g(A.Error))if(e instanceof Error){const s=Array.prototype.slice.call(arguments);s[0]=e.stack,this.m(A.Error,pt(s))}else this.m(A.Error,pt([e,...t]))}flush(){}},Xl=class extends In{constructor(e){super(),this.h=e,e.length&&this.setLevel(e[0].getLevel())}setLevel(e){for(const t of this.h)t.setLevel(e);super.setLevel(e)}trace(e,...t){for(const s of this.h)s.trace(e,...t)}debug(e,...t){for(const s of this.h)s.debug(e,...t)}info(e,...t){for(const s of this.h)s.info(e,...t)}warn(e,...t){for(const s of this.h)s.warn(e,...t)}error(e,...t){for(const s of this.h)s.error(e,...t)}flush(){for(const e of this.h)e.flush()}dispose(){for(const e of this.h)e.dispose();super.dispose()}},Jl=class extends G{constructor(e,t,s){if(super(),this.j=e,this.m=t,this.b=new zs,this.f=this.B(new C),this.onDidChangeLoggers=this.f.event,this.g=this.B(new C),this.onDidChangeLogLevel=this.g.event,this.h=this.B(new C),this.onDidChangeVisibility=this.h.event,s)for(const i of s)this.b.set(i.resource,{logger:void 0,info:i})}n(e){return nt(e)?[...this.b.values()].find(t=>t.info.id===e):this.b.get(e)}getLogger(e){return this.n(e)?.logger}createLogger(e,t){const s=this.s(e),i=nt(e)?e:t?.id??Dl(s.toString()).toString(16);let r=this.b.get(s)?.logger;const n=t?.logLevel==="always"?A.Trace:t?.logLevel;r||(r=this.t(s,n??this.getLogLevel(s)??this.j,{...t,id:i}));const o={logger:r,info:{resource:s,id:i,logLevel:n,name:t?.name,hidden:t?.hidden,group:t?.group,extensionId:t?.extensionId,when:t?.when}};return this.registerLogger(o.info),this.b.set(s,o),r}s(e){return nt(e)?Ce(this.m,`${e}.log`):e}setLogLevel(e,t){if(j.isUri(e)){const s=e,i=t,r=this.b.get(s);r&&i!==r.info.logLevel&&(r.info.logLevel=i===this.j?void 0:i,r.logger?.setLevel(i),this.b.set(r.info.resource,r),this.g.fire([s,i]))}else{this.j=e;for(const[s,i]of this.b.entries())this.b.get(s)?.info.logLevel===void 0&&i.logger?.setLevel(this.j);this.g.fire(this.j)}}setVisibility(e,t){const s=this.n(e);s&&t!==!s.info.hidden&&(s.info.hidden=!t,this.b.set(s.info.resource,s),this.h.fire([s.info.resource,t]))}getLogLevel(e){let t;return e&&(t=this.b.get(e)?.info.logLevel),t??this.j}registerLogger(e){const t=this.b.get(e.resource);t?t.info.hidden!==e.hidden&&this.setVisibility(e.resource,!e.hidden):(this.b.set(e.resource,{info:e,logger:void 0}),this.f.fire({added:[e],removed:[]}))}deregisterLogger(e){const t=this.s(e),s=this.b.get(t);s&&(s.logger&&s.logger.dispose(),this.b.delete(t),this.f.fire({added:[],removed:[s.info]}))}*getRegisteredLoggers(){for(const e of this.b.values())yield e.info}getRegisteredLogger(e){return this.b.get(e)?.info}dispose(){this.b.forEach(e=>e.logger?.dispose()),this.b.clear(),super.dispose()}};function Yl(e){if(e.verbose)return A.Trace;if(typeof e.logLevel=="string"){const t=Kl(e.logLevel.toLowerCase());if(t!==void 0)return t}return Mn}function Ql(e){switch(e){case A.Trace:return"trace";case A.Debug:return"debug";case A.Info:return"info";case A.Warning:return"warn";case A.Error:return"error";case A.Off:return"off"}}function Kl(e){switch(e){case"trace":return A.Trace;case"debug":return A.Debug;case"info":return A.Info;case"warn":return A.Warning;case"error":return A.Error;case"critical":return A.Error;case"off":return A.Off}}var uf=new An("logLevel",Ql(A.Info)),Zl=class{constructor(e,t){this.a=e,this.b=t}listen(e,t){const s=this.b(e);switch(t){case"onDidChangeLoggers":return K.map(this.a.onDidChangeLoggers,i=>({added:[...i.added].map(r=>this.c(r,s)),removed:[...i.removed].map(r=>this.c(r,s))}));case"onDidChangeVisibility":return K.map(this.a.onDidChangeVisibility,i=>[s.transformOutgoingURI(i[0]),i[1]]);case"onDidChangeLogLevel":return K.map(this.a.onDidChangeLogLevel,i=>$n(i)?i:[s.transformOutgoingURI(i[0]),i[1]])}throw new Error(`Event not found: ${t}`)}async call(e,t,s){const i=this.b(e);switch(t){case"setLogLevel":return $n(s[0])?this.a.setLogLevel(s[0]):this.a.setLogLevel(j.revive(i.transformIncoming(s[0][0])),s[0][1]);case"getRegisteredLoggers":return Promise.resolve([...this.a.getRegisteredLoggers()].map(r=>this.c(r,i)))}throw new Error(`Call not found: ${t}`)}c(e,t){return{...e,resource:t.transformOutgoingURI(e.resource)}}},eh=class extends G{constructor(e,t=[]){super(),this.a=new Xl([e,...t]),this.B(e.onDidChangeLogLevel(s=>this.setLevel(s)))}get onDidChangeLogLevel(){return this.a.onDidChangeLogLevel}setLevel(e){this.a.setLevel(e)}getLevel(){return this.a.getLevel()}trace(e,...t){this.a.trace(e,...t)}debug(e,...t){this.a.debug(e,...t)}info(e,...t){this.a.info(e,...t)}warn(e,...t){this.a.warn(e,...t)}error(e,...t){this.a.error(e,...t)}flush(){this.a.flush()}},th=function(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID.bind(crypto);const e=new Uint8Array(16),t=[];for(let s=0;s<256;s++)t.push(s.toString(16).padStart(2,"0"));return function(){crypto.getRandomValues(e),e[6]=e[6]&15|64,e[8]=e[8]&63|128;let i=0,r="";return r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r+="-",r+=t[e[i++]],r+=t[e[i++]],r+="-",r+=t[e[i++]],r+=t[e[i++]],r+="-",r+=t[e[i++]],r+=t[e[i++]],r+="-",r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r}}(),sh=class{constructor(){this.b="",this.c=0}reset(e){return this.b=e,this.c=0,this}next(){return this.c+=1,this}hasNext(){return this.c<this.b.length-1}cmp(e){const t=e.charCodeAt(0),s=this.b.charCodeAt(this.c);return t-s}value(){return this.b[this.c]}},ih=class{constructor(e=!0){this.e=e}reset(e){return this.b=e,this.c=0,this.d=0,this.next()}hasNext(){return this.d<this.b.length}next(){this.c=this.d;let e=!0;for(;this.d<this.b.length;this.d++)if(this.b.charCodeAt(this.d)===46)if(e)this.c++;else break;else e=!1;return this}cmp(e){return this.e?ii(e,this.b,0,e.length,this.c,this.d):$t(e,this.b,0,e.length,this.c,this.d)}value(){return this.b.substring(this.c,this.d)}},On=class{constructor(e=!0,t=!0){this.f=e,this.g=t}reset(e){this.d=0,this.e=0,this.b=e,this.c=e.length;for(let t=e.length-1;t>=0;t--,this.c--){const s=this.b.charCodeAt(t);if(!(s===47||this.f&&s===92))break}return this.next()}hasNext(){return this.e<this.c}next(){this.d=this.e;let e=!0;for(;this.e<this.c;this.e++){const t=this.b.charCodeAt(this.e);if(t===47||this.f&&t===92)if(e)this.d++;else break;else e=!1}return this}cmp(e){return this.g?ii(e,this.b,0,e.length,this.d,this.e):$t(e,this.b,0,e.length,this.d,this.e)}value(){return this.b.substring(this.d,this.e)}},_n;(function(e){e[e.Scheme=1]="Scheme",e[e.Authority=2]="Authority",e[e.Path=3]="Path",e[e.Query=4]="Query",e[e.Fragment=5]="Fragment"})(_n||(_n={}));var rh=class{constructor(e,t){this.f=e,this.g=t,this.d=[],this.e=0}reset(e){return this.c=e,this.d=[],this.c.scheme&&this.d.push(1),this.c.authority&&this.d.push(2),this.c.path&&(this.b=new On(!1,!this.f(e)),this.b.reset(e.path),this.b.value()&&this.d.push(3)),this.g(e)||(this.c.query&&this.d.push(4),this.c.fragment&&this.d.push(5)),this.e=0,this}next(){return this.d[this.e]===3&&this.b.hasNext()?this.b.next():this.e+=1,this}hasNext(){return this.d[this.e]===3&&this.b.hasNext()||this.e<this.d.length-1}cmp(e){if(this.d[this.e]===1)return Ar(e,this.c.scheme);if(this.d[this.e]===2)return Ar(e,this.c.authority);if(this.d[this.e]===3)return this.b.cmp(e);if(this.d[this.e]===4)return si(e,this.c.query);if(this.d[this.e]===5)return si(e,this.c.fragment);throw new Error}value(){if(this.d[this.e]===1)return this.c.scheme;if(this.d[this.e]===2)return this.c.authority;if(this.d[this.e]===3)return this.b.value();if(this.d[this.e]===4)return this.c.query;if(this.d[this.e]===5)return this.c.fragment;throw new Error}},Qe=class Wi{static{this.Val=Symbol("undefined_placeholder")}static wrap(t){return t===void 0?Wi.Val:t}static unwrap(t){return t===Wi.Val?void 0:t}},Es=class{constructor(){this.height=1,this.value=void 0,this.key=void 0,this.left=void 0,this.mid=void 0,this.right=void 0}isEmpty(){return!this.left&&!this.mid&&!this.right&&this.value===void 0}rotateLeft(){const e=this.right;return this.right=e.left,e.left=this,this.updateHeight(),e.updateHeight(),e}rotateRight(){const e=this.left;return this.left=e.right,e.right=this,this.updateHeight(),e.updateHeight(),e}updateHeight(){this.height=1+Math.max(this.heightLeft,this.heightRight)}balanceFactor(){return this.heightRight-this.heightLeft}get heightLeft(){return this.left?.height??0}get heightRight(){return this.right?.height??0}},Rn;(function(e){e[e.Left=-1]="Left",e[e.Mid=0]="Mid",e[e.Right=1]="Right"})(Rn||(Rn={}));var Si=class es{static forUris(t=()=>!1,s=()=>!1){return new es(new rh(t,s))}static forPaths(t=!1){return new es(new On(void 0,!t))}static forStrings(){return new es(new sh)}static forConfigKeys(){return new es(new ih)}constructor(t){this.b=t}clear(){this.c=void 0}fill(t,s){if(s){const i=s.slice(0);Gi(i);for(const r of i)this.set(r,t)}else{const i=t.slice(0);Gi(i);for(const r of i)this.set(r[0],r[1])}}set(t,s){const i=this.b.reset(t);let r;this.c||(this.c=new Es,this.c.segment=i.value());const n=[];for(r=this.c;;){const a=i.cmp(r.segment);if(a>0)r.left||(r.left=new Es,r.left.segment=i.value()),n.push([-1,r]),r=r.left;else if(a<0)r.right||(r.right=new Es,r.right.segment=i.value()),n.push([1,r]),r=r.right;else if(i.hasNext())i.next(),r.mid||(r.mid=new Es,r.mid.segment=i.value()),n.push([0,r]),r=r.mid;else break}const o=Qe.unwrap(r.value);r.value=Qe.wrap(s),r.key=t;for(let a=n.length-1;a>=0;a--){const l=n[a][1];l.updateHeight();const c=l.balanceFactor();if(c<-1||c>1){const u=n[a][0],h=n[a+1][0];if(u===1&&h===1)n[a][1]=l.rotateLeft();else if(u===-1&&h===-1)n[a][1]=l.rotateRight();else if(u===1&&h===-1)l.right=n[a+1][1]=n[a+1][1].rotateRight(),n[a][1]=l.rotateLeft();else if(u===-1&&h===1)l.left=n[a+1][1]=n[a+1][1].rotateLeft(),n[a][1]=l.rotateRight();else throw new Error;if(a>0)switch(n[a-1][0]){case-1:n[a-1][1].left=n[a][1];break;case 1:n[a-1][1].right=n[a][1];break;case 0:n[a-1][1].mid=n[a][1];break}else this.c=n[0][1]}}return o}get(t){return Qe.unwrap(this.d(t)?.value)}d(t){const s=this.b.reset(t);let i=this.c;for(;i;){const r=s.cmp(i.segment);if(r>0)i=i.left;else if(r<0)i=i.right;else if(s.hasNext())s.next(),i=i.mid;else break}return i}has(t){const s=this.d(t);return!(s?.value===void 0&&s?.mid===void 0)}delete(t){return this.e(t,!1)}deleteSuperstr(t){return this.e(t,!0)}e(t,s){const i=this.b.reset(t),r=[];let n=this.c;for(;n;){const o=i.cmp(n.segment);if(o>0)r.push([-1,n]),n=n.left;else if(o<0)r.push([1,n]),n=n.right;else if(i.hasNext())i.next(),r.push([0,n]),n=n.mid;else break}if(n){if(s?(n.left=void 0,n.mid=void 0,n.right=void 0,n.height=1):(n.key=void 0,n.value=void 0),!n.mid&&!n.value)if(n.left&&n.right){const o=[[1,n]],a=this.f(n.right,o);if(a.key){n.key=a.key,n.value=a.value,n.segment=a.segment;const l=a.right;if(o.length>1){const[u,h]=o[o.length-1];switch(u){case-1:h.left=l;break;case 0:er(!1);case 1:er(!1)}}else n.right=l;const c=this.g(o);if(r.length>0){const[u,h]=r[r.length-1];switch(u){case-1:h.left=c;break;case 0:h.mid=c;break;case 1:h.right=c;break}}else this.c=c}}else{const o=n.left??n.right;if(r.length>0){const[a,l]=r[r.length-1];switch(a){case-1:l.left=o;break;case 0:l.mid=o;break;case 1:l.right=o;break}}else this.c=o}this.c=this.g(r)??this.c}}f(t,s){for(;t.left;)s.push([-1,t]),t=t.left;return t}g(t){for(let s=t.length-1;s>=0;s--){const i=t[s][1];i.updateHeight();const r=i.balanceFactor();if(r>1?(i.right.balanceFactor()>=0||(i.right=i.right.rotateRight()),t[s][1]=i.rotateLeft()):r<-1&&(i.left.balanceFactor()<=0||(i.left=i.left.rotateLeft()),t[s][1]=i.rotateRight()),s>0)switch(t[s-1][0]){case-1:t[s-1][1].left=t[s][1];break;case 1:t[s-1][1].right=t[s][1];break;case 0:t[s-1][1].mid=t[s][1];break}else return t[0][1]}}findSubstr(t){const s=this.b.reset(t);let i=this.c,r;for(;i;){const n=s.cmp(i.segment);if(n>0)i=i.left;else if(n<0)i=i.right;else if(s.hasNext())s.next(),r=Qe.unwrap(i.value)||r,i=i.mid;else break}return i&&Qe.unwrap(i.value)||r}findSuperstr(t){return this.h(t,!1)}h(t,s){const i=this.b.reset(t);let r=this.c;for(;r;){const n=i.cmp(r.segment);if(n>0)r=r.left;else if(n<0)r=r.right;else if(i.hasNext())i.next(),r=r.mid;else return r.mid?this.j(r.mid):s?Qe.unwrap(r.value):void 0}}hasElementOrSubtree(t){return this.h(t,!0)!==void 0}forEach(t){for(const[s,i]of this)t(i,s)}*[Symbol.iterator](){yield*this.j(this.c)}j(t){const s=[];return this.l(t,s),s[Symbol.iterator]()}l(t,s){t&&(t.left&&this.l(t.left,s),t.value!==void 0&&s.push([t.key,Qe.unwrap(t.value)]),t.mid&&this.l(t.mid,s),t.right&&this.l(t.right,s))}_isBalanced(){const t=s=>{if(!s)return!0;const i=s.balanceFactor();return i<-1||i>1?!1:t(s.left)&&t(s.right)};return t(this.c)}},ff=Oe("fileService"),Nn;(function(e){e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=64]="SymbolicLink"})(Nn||(Nn={}));var Fn;(function(e){e[e.Readonly=1]="Readonly",e[e.Locked=2]="Locked"})(Fn||(Fn={}));var Tn;(function(e){e[e.UPDATED=2]="UPDATED",e[e.ADDED=4]="ADDED",e[e.DELETED=8]="DELETED"})(Tn||(Tn={}));var jn;(function(e){e[e.None=0]="None",e[e.FileReadWrite=2]="FileReadWrite",e[e.FileOpenReadWriteClose=4]="FileOpenReadWriteClose",e[e.FileReadStream=16]="FileReadStream",e[e.FileFolderCopy=8]="FileFolderCopy",e[e.PathCaseSensitive=1024]="PathCaseSensitive",e[e.Readonly=2048]="Readonly",e[e.Trash=4096]="Trash",e[e.FileWriteUnlock=8192]="FileWriteUnlock",e[e.FileAtomicRead=16384]="FileAtomicRead",e[e.FileAtomicWrite=32768]="FileAtomicWrite",e[e.FileAtomicDelete=65536]="FileAtomicDelete",e[e.FileClone=131072]="FileClone"})(jn||(jn={}));var Un;(function(e){e.FileExists="EntryExists",e.FileNotFound="EntryNotFound",e.FileNotADirectory="EntryNotADirectory",e.FileIsADirectory="EntryIsADirectory",e.FileExceedsStorageQuota="EntryExceedsStorageQuota",e.FileTooLarge="EntryTooLarge",e.FileWriteLocked="EntryWriteLocked",e.NoPermissions="NoPermissions",e.Unavailable="Unavailable",e.Unknown="Unknown"})(Un||(Un={}));var Bn;(function(e){e[e.CREATE=0]="CREATE",e[e.DELETE=1]="DELETE",e[e.MOVE=2]="MOVE",e[e.COPY=3]="COPY",e[e.WRITE=4]="WRITE"})(Bn||(Bn={}));var zn;(function(e){e[e.UPDATED=0]="UPDATED",e[e.ADDED=1]="ADDED",e[e.DELETED=2]="DELETED"})(zn||(zn={}));var df=class Ts{static{this.a=null}constructor(t,s){this.c=s,this.b=void 0,this.d=new it(()=>{const i=Si.forUris(()=>this.c);return i.fill(this.rawAdded.map(r=>[r,!0])),i}),this.f=new it(()=>{const i=Si.forUris(()=>this.c);return i.fill(this.rawUpdated.map(r=>[r,!0])),i}),this.g=new it(()=>{const i=Si.forUris(()=>this.c);return i.fill(this.rawDeleted.map(r=>[r,!0])),i}),this.rawAdded=[],this.rawUpdated=[],this.rawDeleted=[];for(const i of t){switch(i.type){case 1:this.rawAdded.push(i.resource);break;case 0:this.rawUpdated.push(i.resource);break;case 2:this.rawDeleted.push(i.resource);break}this.b!==Ts.a&&(typeof i.cId=="number"?this.b===void 0?this.b=i.cId:this.b!==i.cId&&(this.b=Ts.a):this.b!==void 0&&(this.b=Ts.a))}}contains(t,...s){return this.h(t,{includeChildren:!1},...s)}affects(t,...s){return this.h(t,{includeChildren:!0},...s)}h(t,s,...i){if(!t)return!1;const r=i.length>0;return!!((!r||i.includes(1))&&(this.d.value.get(t)||s.includeChildren&&this.d.value.findSuperstr(t))||(!r||i.includes(0))&&(this.f.value.get(t)||s.includeChildren&&this.f.value.findSuperstr(t))||(!r||i.includes(2))&&(this.g.value.findSubstr(t)||s.includeChildren&&this.g.value.findSuperstr(t)))}gotAdded(){return this.rawAdded.length>0}gotDeleted(){return this.rawDeleted.length>0}gotUpdated(){return this.rawUpdated.length>0}correlates(t){return this.b===t}hasCorrelation(){return typeof this.b=="number"}},Wn;(function(e){e[e.FILE_IS_DIRECTORY=0]="FILE_IS_DIRECTORY",e[e.FILE_NOT_FOUND=1]="FILE_NOT_FOUND",e[e.FILE_NOT_MODIFIED_SINCE=2]="FILE_NOT_MODIFIED_SINCE",e[e.FILE_MODIFIED_SINCE=3]="FILE_MODIFIED_SINCE",e[e.FILE_MOVE_CONFLICT=4]="FILE_MOVE_CONFLICT",e[e.FILE_WRITE_LOCKED=5]="FILE_WRITE_LOCKED",e[e.FILE_PERMISSION_DENIED=6]="FILE_PERMISSION_DENIED",e[e.FILE_TOO_LARGE=7]="FILE_TOO_LARGE",e[e.FILE_INVALID_PATH=8]="FILE_INVALID_PATH",e[e.FILE_NOT_DIRECTORY=9]="FILE_NOT_DIRECTORY",e[e.FILE_OTHER_ERROR=10]="FILE_OTHER_ERROR"})(Wn||(Wn={}));var Hn;(function(e){e[e.FILE=0]="FILE",e[e.FOLDER=1]="FOLDER",e[e.ROOT_FOLDER=2]="ROOT_FOLDER"})(Hn||(Hn={}));var nh=class le{static{this.KB=1024}static{this.MB=le.KB*le.KB}static{this.GB=le.MB*le.KB}static{this.TB=le.GB*le.KB}static formatSize(t){return tr(t)||(t=0),t<le.KB?w(2025,null,t.toFixed(0)):t<le.MB?w(2026,null,(t/le.KB).toFixed(2)):t<le.GB?w(2027,null,(t/le.MB).toFixed(2)):t<le.TB?w(2028,null,(t/le.GB).toFixed(2)):w(2029,null,(t/le.TB).toFixed(2))}},_e;(function(e){e[e.Trace=0]="Trace",e[e.Debug=1]="Debug",e[e.Info=2]="Info",e[e.Warning=3]="Warning",e[e.Error=4]="Error",e[e.Critical=5]="Critical",e[e.Off=6]="Off"})(_e||(_e={}));async function oh(e,t,s,i,r){try{const n=await import("@vscode/spdlog");n.setFlushOn(_e.Trace);const o=await n.createAsyncRotatingLogger(e,t,s,i);return r?o.clearFormatters():o.setPattern("%Y-%m-%d %H:%M:%S.%e [%l] %v"),o}catch(n){console.error(n)}return null}function Vn(e,t,s){switch(t){case A.Trace:e.trace(s);break;case A.Debug:e.debug(s);break;case A.Info:e.info(s);break;case A.Warning:e.warn(s);break;case A.Error:e.error(s);break;case A.Off:break;default:throw new Error(`Invalid log level ${t}`)}}function qn(e,t){switch(t){case A.Trace:e.setLevel(_e.Trace);break;case A.Debug:e.setLevel(_e.Debug);break;case A.Info:e.setLevel(_e.Info);break;case A.Warning:e.setLevel(_e.Warning);break;case A.Error:e.setLevel(_e.Error);break;case A.Off:e.setLevel(_e.Off);break;default:throw new Error(`Invalid log level ${t}`)}}var ah=class extends Gl{constructor(e,t,s,i,r){super(),this.n=[],this.setLevel(r),this.r=this.t(e,t,s,i),this.B(this.onDidChangeLogLevel(n=>{this.s&&qn(this.s,n)}))}async t(e,t,s,i){const r=s?6:1,n=30/r*nh.MB,o=await oh(e,t,n,r,i);if(o){this.s=o,qn(this.s,this.getLevel());for(const{level:a,message:l}of this.n)Vn(this.s,a,l);this.n=[]}}m(e,t){this.s?Vn(this.s,e,t):this.getLevel()<=e&&this.n.push({level:e,message:t})}flush(){this.s?this.w():this.r.then(()=>this.w())}dispose(){this.s?this.y():this.r.then(()=>this.y()),super.dispose()}w(){this.s&&this.s.flush()}y(){this.s&&(this.s.drop(),this.s=void 0)}},ch=class extends Jl{t(e,t,s){return new ah(th(),e.fsPath,!s?.donotRotate,!!s?.donotUseFormatters,t)}},de,Pi=globalThis.vscode;if(typeof Pi<"u"&&typeof Pi.context<"u"){const e=Pi.context.configuration();if(e)de=e.product;else throw new Error("Sandbox: unable to resolve product configuration from preload script.")}else if(globalThis._VSCODE_PRODUCT_JSON&&globalThis._VSCODE_PACKAGE_JSON){if(de=globalThis._VSCODE_PRODUCT_JSON,Pe.VSCODE_DEV&&Object.assign(de,{nameShort:`${de.nameShort} Dev`,nameLong:`${de.nameLong} Dev`,dataFolderName:`${de.dataFolderName}-dev`,serverDataFolderName:de.serverDataFolderName?`${de.serverDataFolderName}-dev`:void 0}),!de.version){const e=globalThis._VSCODE_PACKAGE_JSON;Object.assign(de,{version:e.version})}}else de={nameShort:"Code",nameLong:"Visual Studio Code",applicationName:"code",win32x64AppId:"{{EA457B21-F73E-494C-ACAB-524FDE069978}",win32arm64AppId:"{{A5270FC5-65AD-483E-AC30-2C276B63D0AC}",win32x64UserAppId:"{{771FD6B0-FA20-440A-A002-3B3BAC16DC50}",win32arm64UserAppId:"{{D9E514E7-1A56-452D-9337-2990C0DC4310}",win32NameVersion:"Microsoft Visual Studio Code",win32DirName:"Microsoft VS Code",win32SetupExeBasename:"VSCodeSetup",win32AppUserModelId:"Microsoft.VisualStudioCode",win32ShellNameShort:"Code",win32MutexName:"vscode",win32RegValueName:"VSCode",darwinCredits:"resources/darwin/Credits.rtf",darwinBundleIdentifier:"com.microsoft.VSCode",darwinProfileUUID:"EBAE60D6-C8A2-4419-92FF-24F8AD5077AB",darwinProfilePayloadUUID:"C6B5723A-6539-4F31-8A4E-3CC96E51F48C",darwinExecutable:"VSCode",linuxIconName:"vscode",licenseFileName:"LICENSE.rtf",licenseName:"Multiple, see https://code.visualstudio.com/license",serverGreeting:[],serverLicense:["*","* Visual Studio Code Server","*","* By using the software, you agree to","* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and","* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).","*"],serverLicensePrompt:"Do you accept the terms in the License Agreement (Y/n)?",serverApplicationName:"code-server",urlProtocol:"vscode",dataFolderName:".vscode",serverDataFolderName:".vscode-server",downloadUrl:"https://code.visualstudio.com",updateUrl:"https://update.code.visualstudio.com",webUrl:"https://vscode.dev",webEndpointUrl:"https://main.vscode-cdn.net",webEndpointUrlTemplate:"https://{{uuid}}.vscode-cdn.net/{{quality}}/{{commit}}",nlsCoreBaseUrl:"https://www.vscode-unpkg.net/nls/",webviewContentExternalBaseUrlTemplate:"https://{{uuid}}.vscode-cdn.net/{{quality}}/{{commit}}/out/vs/workbench/contrib/webview/browser/pre/",quality:"stable",extensionsGallery:{nlsBaseUrl:"https://www.vscode-unpkg.net/_lp/",serviceUrl:"https://marketplace.visualstudio.com/_apis/public/gallery",itemUrl:"https://marketplace.visualstudio.com/items",publisherUrl:"https://marketplace.visualstudio.com/publishers",resourceUrlTemplate:"https://{publisher}.vscode-unpkg.net/{publisher}/{name}/{version}/{path}",extensionUrlTemplate:"https://www.vscode-unpkg.net/_gallery/{publisher}/{name}/latest",controlUrl:"https://main.vscode-cdn.net/extensions/marketplace.json",accessSKUs:["copilot_enterprise_seat","copilot_enterprise_seat_quota","copilot_enterprise_seat_multi_quota","copilot_enterprise_seat_assignment","copilot_enterprise_seat_assignment_quota","copilot_enterprise_seat_assignment_multi_quota","copilot_enterprise_trial_seat","copilot_enterprise_trial_seat_quota","copilot_for_business_seat","copilot_for_business_seat_quota","copilot_for_business_seat_multi_quota","copilot_for_business_seat_assignment","copilot_for_business_seat_assignment_quota","copilot_for_business_seat_assignment_multi_quota","copilot_for_business_trial_seat","copilot_for_business_trial_seat_quota"]},extensionProperties:{"github.copilot-chat":{hasPrereleaseVersion:!1,excludeVersionRange:"<=0.16.1"},"github.copilot":{hasPrereleaseVersion:!0}},defaultAccount:{authenticationProvider:{id:"github",enterpriseProviderId:"github-enterprise",enterpriseProviderConfig:"github-enterprise.uri",scopes:["user:email"]},chatEntitlementUrl:"https://api.github.com/copilot_internal/user",tokenEntitlementUrl:"https://api.github.com/copilot_internal/v2/token"},profileTemplatesUrl:"https://main.vscode-cdn.net/core/profile-templates.json",extensionPublisherOrgs:["microsoft"],trustedExtensionPublishers:["microsoft","github"],extensionRecommendations:{"ms-dotnettools.csdevkit":{onFileOpen:[{pathGlob:"{**/*.cs,**/global.json,**/*.csproj,**/*.cshtml,**/*.sln}",important:!0},{languages:["csharp"],important:!0},{pathGlob:"{**/project.json,**/appsettings.json}"}]},"ms-python.python":{onFileOpen:[{pathGlob:"{**/*.py}",important:!0},{languages:["python"],important:!0},{pathGlob:"{**/*.ipynb}"}]},"ms-toolsai.jupyter":{onFileOpen:[{pathGlob:"{**/*.py}",contentPattern:"^#\\s*%%$",important:!0,whenInstalled:["ms-python.python"]},{pathGlob:"{**/*.ipynb}"}]},"ms-toolsai.datawrangler":{onFileOpen:[{pathGlob:"{**/*.ipynb}",contentPattern:"import\\s*pandas|from\\s*pandas",whenInstalled:["ms-toolsai.jupyter"]}]},"golang.Go":{onFileOpen:[{pathGlob:"**/*.go",important:!0},{languages:["go"],important:!0}]},"vscjava.vscode-java-pack":{onFileOpen:[{pathGlob:"{**/*.java}",important:!0,whenNotInstalled:["ASF.apache-netbeans-java","Oracle.oracle-java"]},{languages:["java"],important:!0,whenNotInstalled:["ASF.apache-netbeans-java","Oracle.oracle-java"]}]},"ms-vscode.PowerShell":{onFileOpen:[{pathGlob:"{**/*.ps1,**/*.psd1,**/*.psm1}",important:!0},{languages:["powershell"],important:!0},{pathGlob:"{**/*.ps.config,**/*.ps1.config}"}]},"ms-toolsai.prompty":{onFileOpen:[{pathGlob:"{**/*.prompty}",important:!1}]},"typespec.typespec-vscode":{onFileOpen:[{pathGlob:"{**/*.tsp,**/tspconfig.yaml}",important:!0}]},"ms-vscode.cpptools-extension-pack":{onFileOpen:[{pathGlob:"{**/*.c,**/*.cpp,**/*.cc,**/.cxx,**/*.hh,**/*.hpp,**/*.hxx,**/*.h}",important:!0,whenNotInstalled:["llvm-vs-code-extensions.vscode-clangd"]},{languages:["c","cpp"],important:!0,whenNotInstalled:["llvm-vs-code-extensions.vscode-clangd"]}]},"ms-azuretools.vscode-docker":{onFileOpen:[{pathGlob:"{**/dockerfile,**/Dockerfile,**/docker-compose.yml,**/docker-compose.*.yml}",important:!0,whenNotInstalled:["ms-azuretools.vscode-containers"]},{languages:["dockerfile"],important:!0,whenNotInstalled:["ms-azuretools.vscode-containers"]},{pathGlob:"{**/*.cs,**/project.json,**/global.json,**/*.csproj,**/*.cshtml,**/*.sln,**/appsettings.json,**/*.py,**/*.ipynb,**/*.js,**/*.ts,**/package.json}",whenNotInstalled:["ms-azuretools.vscode-containers"]}]},"vue.volar":{onFileOpen:[{pathGlob:"{**/*.vue}",important:!0},{languages:["vue"],important:!0}]},"ms-vscode.makefile-tools":{onFileOpen:[{pathGlob:"{**/makefile,**/Makefile}",important:!0},{languages:["makefile"],important:!0}]},"ms-vscode.cmake-tools":{onFileOpen:[{pathGlob:"{**/CMakeLists.txt}",important:!0}]},"ms-azure-devops.azure-pipelines":{onFileOpen:[{pathGlob:"{**/azure-pipelines.yaml}",important:!0}]},"msazurermtools.azurerm-vscode-tools":{onFileOpen:[{pathGlob:"{**/azuredeploy.json}",important:!0}]},"ms-vscode-remote.remote-containers":{onFileOpen:[{pathGlob:"{**/devcontainer.json}",important:!0}]},"ms-azuretools.vscode-bicep":{onFileOpen:[{pathGlob:"{**/*.bicep}",important:!0,whenNotInstalled:["ms-azuretools.rad-vscode-bicep"]}]},"svelte.svelte-vscode":{onFileOpen:[{pathGlob:"{**/*.svelte}",important:!0}]},"ms-vscode.vscode-github-issue-notebooks":{onFileOpen:[{pathGlob:"{**/*.github-issues}",important:!0}]},"ms-playwright.playwright":{onFileOpen:[{pathGlob:"{**/*playwright*.config.ts,**/*playwright*.config.js,**/*playwright*.config.mjs}",important:!0}]},"vscjava.vscode-gradle":{onFileOpen:[{pathGlob:"{**/gradlew,**/gradlew.bat,**/build.gradle,**/build.gradle.kts,**/settings.gradle,**/settings.gradle.kts}",important:!0}]},"REditorSupport.r":{onFileOpen:[{pathGlob:"{**/*.r}",important:!0},{languages:["r"],important:!0}]},"firefox-devtools.vscode-firefox-debug":{onFileOpen:[{pathGlob:"{**/*.ts,**/*.tsx,**/*.js,**/*.jsx,**/*.es6,**/.babelrc}"}]},"ms-edgedevtools.vscode-edge-devtools":{onFileOpen:[{pathGlob:"{**/*.ts,**/*.tsx,**/*.js,**/*.css,**/*.html}"}]},"Ionide.Ionide-fsharp":{onFileOpen:[{pathGlob:"{**/*.fsx,**/*.fsi,**/*.fs,**/*.ml,**/*.mli}"}]},"dbaeumer.vscode-eslint":{onFileOpen:[{pathGlob:"{**/*.js,**/*.jsx,**/*.es6,**/.eslintrc.*,**/.eslintrc,**/.babelrc,**/jsconfig.json}"}]},"bmewburn.vscode-intelephense-client":{onFileOpen:[{pathGlob:"{**/*.php,**/php.ini}"}]},"xdebug.php-debug":{onFileOpen:[{pathGlob:"{**/*.php,**/php.ini}"}]},"rust-lang.rust-analyzer":{onFileOpen:[{pathGlob:"{**/*.rs,**/*.rslib}"}]},"DavidAnson.vscode-markdownlint":{onFileOpen:[{pathGlob:"{**/*.md}"}]},"EditorConfig.EditorConfig":{onFileOpen:[{pathGlob:"{**/.editorconfig}"}]},"HookyQR.beautify":{onFileOpen:[{pathGlob:"{**/.jsbeautifyrc}"}]},"donjayamanne.githistory":{onFileOpen:[{pathGlob:"{**/.gitignore,**/.git}"}]},"eamodio.gitlens":{onFileOpen:[{pathGlob:"{**/.gitignore,**/.git}"}]},"Shopify.ruby-lsp":{onFileOpen:[{pathGlob:"{**/*.rb,**/*.erb,**/*.reek,**/.fasterer.yml,**/ruby-lint.yml,**/.rubocop.yml}"}]},"swiftlang.swift-vscode":{onFileOpen:[{pathGlob:"{**/*.swift,**/*.swiftinterface}",important:!0}]},"DotJoshJohnson.xml":{onFileOpen:[{pathGlob:"{**/*.xml}"}]},"stylelint.vscode-stylelint":{onFileOpen:[{pathGlob:"{**/.stylelintrc,**/stylelint.config.js}"}]},"ms-mssql.mssql":{onFileOpen:[{pathGlob:"{**/*.sql}"}]},"mtxr.sqltools":{onFileOpen:[{pathGlob:"{**/*.sql}"}]},"usqlextpublisher.usql-vscode-ext":{onFileOpen:[{pathGlob:"{**/*.usql}"}]},"ms-vscode.sublime-keybindings":{onFileOpen:[{pathGlob:"{**/.sublime-project,**/.sublime-workspace}"}]},"k--kato.intellij-idea-keybindings":{onFileOpen:[{pathGlob:"{**/.idea}"}]},"christian-kohler.npm-intellisense":{onFileOpen:[{pathGlob:"{**/package.json}"}]},"cake-build.cake-vscode":{onFileOpen:[{pathGlob:"{**/build.cake}"}]},"Angular.ng-template":{onFileOpen:[{pathGlob:"{**/.angular-cli.json,**/angular.json,**/*.ng.html,**/*.ng,**/*.ngml}"}]},"vscjava.vscode-maven":{onFileOpen:[{pathGlob:"**/pom.xml"}]},"ms-azuretools.vscode-azureterraform":{onFileOpen:[{pathGlob:"**/*.tf"}]},"HashiCorp.terraform":{onFileOpen:[{pathGlob:"**/*.tf"}]},"vsciot-vscode.vscode-arduino":{onFileOpen:[{pathGlob:"**/*.ino"}]},"ms-kubernetes-tools.vscode-kubernetes-tools":{onFileOpen:[{pathGlob:"{**/Chart.yaml}"}]},"Oracle.oracledevtools":{onFileOpen:[{pathGlob:"{**/*.sql}"}]},"betterthantomorrow.calva":{onFileOpen:[{pathGlob:"{**/*.clj,**/*.cljs}"}]},"vmware.vscode-boot-dev-pack":{onFileOpen:[{pathGlob:"{**/application.properties}"}]},"GitHub.copilot":{onFileOpen:[{pathGlob:"{**/*.ts,**/*.tsx,**/*.js,**/*.jsx,**/*.py,**/*.go,**/*.rb,**/*.html,**/*.css,**/*.php,**/*.cpp,**/*.vue,**/*.c,**/*.sql,**/*.java,**/*.cs,**/*.rs,**/*.dart,**/*.ps,**/*.ps1,**/*.tex}"}],onSettingsEditorOpen:{descriptionOverride:"GitHub Copilot is an AI pair programmer tool that helps you write code faster and smarter."}},"GitHub.vscode-github-actions":{onFileOpen:[{pathGlob:"{**/.github/workflows/*.yml}",important:!0}]},"circleci.circleci":{onFileOpen:[{pathGlob:"{**/.circleci/config.yml}"}]},"mechatroner.rainbow-csv":{onFileOpen:[{pathGlob:"**/*.csv",important:!0}]},"tomoki1207.pdf":{onFileOpen:[{pathGlob:"**/*.pdf",important:!0}]},"Redis.redis-for-vscode":{onFileOpen:[{pathGlob:"{**/redis.*,**/redis-server.*,**/redis_*,**/redisinsight.*}",important:!0}]},"SonarSource.sonarlint-vscode":{onFileOpen:[{pathGlob:"{**/sonar-project.properties,**/sonarcloud.properties,**/sonarlint.*}",important:!0}]}},keymapExtensionTips:["vscodevim.vim","ms-vscode.sublime-keybindings","ms-vscode.atom-keybindings","ms-vscode.brackets-keybindings","ms-vscode.vs-keybindings","ms-vscode.notepadplusplus-keybindings","k--kato.intellij-idea-keybindings","lfs.vscode-emacs-friendly","alphabotsec.vscode-eclipse-keybindings","alefragnani.delphi-keybindings"],languageExtensionTips:["ms-python.python","ms-vscode.cpptools-extension-pack","ms-dotnettools.csdevkit","ms-toolsai.jupyter","vscjava.vscode-java-pack","ecmel.vscode-html-css","vue.volar","bmewburn.vscode-intelephense-client","dsznajder.es7-react-js-snippets","golang.go","ms-vscode.powershell","dart-code.dart-code","rust-lang.rust-analyzer","Shopify.ruby-lsp","GitHub.copilot"],configBasedExtensionTips:{git:{configPath:".git/config",configName:"Git",recommendations:{"github.vscode-pull-request-github":{name:"GitHub Pull Request",contentPattern:"^\\s*url\\s*=\\s*https:\\/\\/github\\.com.*$"},"eamodio.gitlens":{name:"GitLens"}}},devContainer:{configPath:".devcontainer/devcontainer.json",configName:"Dev Container",recommendations:{"ms-vscode-remote.remote-containers":{name:"Dev Containers",important:!0}}},maven:{configPath:"pom.xml",configName:"Maven",recommendations:{"vscjava.vscode-java-pack":{name:"Java",important:!0,isExtensionPack:!0,whenNotInstalled:["ASF.apache-netbeans-java","Oracle.oracle-java"]},"vmware.vscode-boot-dev-pack":{name:"Spring Boot Extension Pack",isExtensionPack:!0}}},gradle:{configPath:"build.gradle",configName:"Gradle",recommendations:{"vscjava.vscode-java-pack":{name:"Java",important:!0,isExtensionPack:!0,whenNotInstalled:["ASF.apache-netbeans-java","Oracle.oracle-java"]}}},"github-pull-request":{configPath:".vscode/.github-pull-request.rec",configName:"GitHub",configScheme:"vscode-vfs",recommendations:{"github.vscode-pull-request-github":{name:"GitHub Pull Request",important:!0}}},"pyproject-formatter":{configPath:"pyproject.toml",configName:"Python Formatter",recommendations:{"ms-python.black-formatter":{name:"Black Formatter",contentPattern:'(^\\s*\\[\\[?\\s*"?tool"?\\s*\\.\\s*"?black"?\\s*[\\].])|("black\\s*["[(<=>!~;@])'},"ms-python.autopep8":{name:"Autopep8",contentPattern:'(^\\s*\\[\\[?\\s*"?tool"?\\s*\\.\\s*"?autopep8"?\\s*[\\].])|("autopep8\\s*["[(<=>!~;@])'}}},"pep8-formatter":{configPath:".pep8",configName:"Python Formatter",recommendations:{"ms-python.autopep8":{name:"Autopep8"}}},"python-setup-cgf-formatter":{configPath:"setup.cfg",configName:"Python Formatter",recommendations:{"ms-python.autopep8":{name:"Autopep8",contentPattern:"^\\[pep8\\]"}}},"tox-ini-formatter":{configPath:"tox.ini",configName:"Python Formatter",recommendations:{"ms-python.autopep8":{name:"Autopep8",contentPattern:"^\\[pep8\\]"}}},"pyproject-linter":{configPath:"pyproject.toml",configName:"Python Linter",recommendations:{"ms-python.pylint":{name:"Pylint",contentPattern:'(^\\s*\\[\\[?\\s*"?tool"?\\s*\\.\\s*"?pylint"?\\s*[\\].])|("pylint\\s*["[(<=>!~;@])'},"charliermarsh.ruff":{name:"Ruff",contentPattern:'(^\\s*\\[\\[?\\s*"?tool"?\\s*\\.\\s*"?ruff"?\\s*[\\].])|("ruff\\s*["[(<=>!~;@])'},"ms-python.mypy-type-checker":{name:"Mypy Type Checker",contentPattern:'(^\\s*\\[\\[?\\s*"?tool"?\\s*\\.\\s*"?mypy"?\\s*[\\].])|("mypy\\s*["[(<=>!~;@])'},"ms-python.flake8":{name:"Flake8",contentPattern:'(^\\s*\\[\\[?\\s*"?tool"?\\s*\\.\\s*"?flake8"?\\s*[\\].])|("flake8\\s*["[(<=>!~;@])'}}},".pylintrc-linter":{configPath:".pylintrc",configName:"Python Linter",recommendations:{"ms-python.pylint":{name:"Pylint"}}},"pylintrc-linter":{configPath:"pylintrc",configName:"Python Linter",recommendations:{"ms-python.pylint":{name:"Pylint"}}},"mypy-ini-linter":{configPath:".mypy.ini",configName:"Python Linter",recommendations:{"ms-python.mypy-type-checker":{name:"Mypy Type Checker"}}},"tox-ini-linter":{configPath:"tox.ini",configName:"Python Linter",recommendations:{"ms-python.flake8":{name:"Flake8",contentPattern:"^\\[flake8\\]"}}},".flake8-linter":{configPath:".flake8",configName:"Python Linter",recommendations:{"ms-python.flake8":{name:"Flake8"}}},"python-setup-cgf-linter":{configPath:"setup.cfg",configName:"Python Linter",recommendations:{"ms-python.flake8":{name:"Flake8",contentPattern:"^\\[flake8\\]"}}}},exeBasedExtensionTips:{az:{friendlyName:"Azure CLI",windowsPath:"%ProgramFiles(x86)%\\Microsoft SDKs\\Azure\\CLI2\\wbin\\az.cmd",recommendations:{"ms-vscode.vscode-node-azure-pack":{name:"Azure Tools"},"ms-azuretools.vscode-azure-github-copilot":{name:"GitHub Copilot for Azure"}}},azd:{friendlyName:"Azure Dev CLI",windowsPath:"%USERPROFILE%\\AppData\\Local\\Programs\\Azure Dev CLI\\azd.exe",recommendations:{"ms-vscode.vscode-node-azure-pack":{name:"Azure Tools"},"ms-azuretools.vscode-azure-github-copilot":{name:"GitHub Copilot for Azure"}}},"azd-user":{friendlyName:"Azure Dev CLI",windowsPath:"%ProgramFiles%\\Azure Dev CLI\\azd.exe",recommendations:{"ms-vscode.vscode-node-azure-pack":{name:"Azure Tools"},"ms-azuretools.vscode-azure-github-copilot":{name:"GitHub Copilot for Azure"}}},heroku:{friendlyName:"Heroku CLI",windowsPath:"%ProgramFiles%\\Heroku\\bin\\heroku.cmd",recommendations:{"ms-azuretools.vscode-azureappservice":{name:"Azure App Service"},"pkosta2005.heroku-command":{name:"heroku-cli"}}},mongo:{friendlyName:"Mongo",windowsPath:"%ProgramFiles%\\MongoDB\\Server\\3.6\\bin\\mongod.exe",recommendations:{"ms-azuretools.vscode-cosmosdb":{name:"Azure Databases"}}},serverless:{friendlyName:"Serverless framework",windowsPath:"%APPDATA%\\npm\\serverless.cmd",recommendations:{"ms-azuretools.vscode-azurefunctions":{name:"Azure Functions"}}},func:{friendlyName:"Azure Function SDK",windowsPath:"%APPDATA%\\npm\\func.cmd",recommendations:{"ms-azuretools.vscode-azurefunctions":{name:"Azure Functions"}}},mysql:{friendlyName:"MySQL",windowsPath:"%ProgramFiles%\\MySQL\\MySQL Server 8.0\\bin\\mysqld.exe",recommendations:{"mtxr.sqltools":{name:"SQLTools"}}},postgres:{friendlyName:"PostgreSQL",windowsPath:"%ProgramFiles%\\PostgreSQL\\11\\bin\\psql.exe",recommendations:{"ms-ossdata.vscode-postgresql":{name:"PostgreSQL"},"mtxr.sqltools":{name:"SQLTools"}}},sqlcmd:{friendlyName:"SQL CLI",recommendations:{"ms-mssql.mssql":{name:"SQL Server (mssql)"}}},now:{friendlyName:"Now CLI",windowsPath:"%APPDATA%\\npm\\now.cmd",recommendations:{"ms-azuretools.vscode-azureappservice":{name:"Azure App Service"},"ms-azuretools.vscode-docker":{name:"Docker"}}},docker:{friendlyName:"Docker",windowsPath:"%ProgramFiles%\\Docker\\Docker\\Resources\\bin\\docker.exe",recommendations:{"ms-azuretools.vscode-docker":{name:"Docker",important:!0},"ms-vscode-remote.remote-containers":{name:"Dev Containers",important:!0},"ms-kubernetes-tools.vscode-kubernetes-tools":{name:"Kubernetes"}}},kubectl:{friendlyName:"Kubernetes",windowsPath:"%ProgramFiles%\\Docker\\Docker\\Resources\\bin\\kubectl.exe",recommendations:{"ms-azuretools.vscode-docker":{name:"Docker"},"ms-kubernetes-tools.vscode-kubernetes-tools":{name:"Kubernetes"},"ms-vscode-remote.remote-containers":{name:"Dev Containers"}}},ng:{friendlyName:"Angular CLI",windowsPath:"%APPDATA%\\npmexit\\ng.cmd",recommendations:{"johnpapa.Angular2":{name:"Angular Snippets"}}},"create-react-app":{friendlyName:"Create React App",windowsPath:"%APPDATA%\\npm\\create-react-app.cmd",recommendations:{"msjsdiag.vscode-react-native":{name:"React Native Tools"}}},"react-native":{friendlyName:"React Native",windowsPath:"%APPDATA%\\npm\\react-native-cli",recommendations:{"msjsdiag.vscode-react-native":{name:"React Native Tools"}}},p4:{friendlyName:"Perforce",recommendations:{"slevesque.perforce":{name:"Perforce for VS Code"}}},hg:{friendlyName:"Mercurial",recommendations:{"mrcrowl.hg":{name:"Hg"}}},git:{friendlyName:"Git",windowsPath:"%ProgramFiles%\\Git\\git-bash.exe",recommendations:{"eamodio.gitlens":{name:"GitLens"}}},svn:{friendlyName:"Subversion",windowsPath:"%ProgramFiles%\\TortoiseSVN\\bin\\TortoiseProc.exe",recommendations:{"johnstoncode.svn-scm":{name:"SVN"}}},subl:{friendlyName:"Sublime",windowsPath:"%ProgramFiles%\\Sublime Text3\\sublime_text.exe",recommendations:{"ms-vscode.sublime-keybindings":{name:"Sublime Text Keymap and Settings Importer"}}},atom:{friendlyName:"Atom",windowsPath:"%USERPROFILE%\\AppData\\Local\\atom\\bin\\atom.cmd",recommendations:{"ms-vscode.atom-keybindings":{name:"Atom Keymap"}}},brackets:{friendlyName:"Brackets",windowsPath:"%ProgramFiles(x86)%\\Brackets\\Brackets.exe",recommendations:{"ms-vscode.brackets-keybindings":{name:"Brackets Keymap"}}},notepadplusplus:{friendlyName:"Notepad++",windowsPath:"%ProgramFiles%\\Notepad++\\Notepad++.exe",recommendations:{"ms-vscode.notepadplusplus-keybindings":{name:"Notepad++ keymap"}}},vi:{friendlyName:"VIM",windowsPath:"%ProgramFiles(x86)%\\Vim\\vim80\\gvim.exe",recommendations:{"vscodevim.vim":{name:"Vim"}}},mvn:{friendlyName:"Maven",recommendations:{"vscjava.vscode-java-pack":{name:"Java",important:!0,isExtensionPack:!0,whenNotInstalled:["ASF.apache-netbeans-java","Oracle.oracle-java"]}}},gradle:{friendlyName:"Gradle",recommendations:{"vscjava.vscode-java-pack":{name:"Java",important:!0,isExtensionPack:!0,whenNotInstalled:["ASF.apache-netbeans-java","Oracle.oracle-java"]}}},ollama:{friendlyName:"Ollama",windowsPath:"%USERPROFILE%\\AppData\\Local\\Programs\\Ollama\\ollama.exe",recommendations:{"ms-windows-ai-studio.windows-ai-studio":{name:"AI Toolkit for Visual Studio Code"}}},"Microsoft Edge":{friendlyName:"Microsoft Edge",windowsPath:"%USERPROFILE%\\AppData\\Local\\Microsoft\\Edge\\Application\\msedge.exe",recommendations:{"ms-edgedevtools.vscode-edge-devtools":{name:"Microsoft Edge Developer Tools"}}},"Microsoft Edge Dev":{friendlyName:"Microsoft Edge Dev",windowsPath:"%USERPROFILE%\\AppData\\Local\\Microsoft\\Edge Dev\\Application\\msedge.exe",recommendations:{"ms-edgedevtools.vscode-edge-devtools":{name:"Microsoft Edge Developer Tools"}}},"Microsoft Edge Beta":{friendlyName:"Microsoft Edge Beta",windowsPath:"%USERPROFILE%\\AppData\\Local\\Microsoft\\Edge Beta\\Application\\msedge.exe",recommendations:{"ms-edgedevtools.vscode-edge-devtools":{name:"Microsoft Edge Developer Tools"}}},"Microsoft Edge Canary":{friendlyName:"Microsoft Edge Canary",windowsPath:"%USERPROFILE%\\AppData\\Local\\Microsoft\\Edge SxS\\Application\\msedge.exe",recommendations:{"ms-edgedevtools.vscode-edge-devtools":{name:"Microsoft Edge Developer Tools"}}},"Mozilla Firefox (x86)":{friendlyName:"Mozilla Firefox",windowsPath:"%ProgramFiles(x86)%\\Mozilla Firefox\\firefox.exe",recommendations:{"firefox-devtools.vscode-firefox-debug":{name:"Debugger for Firefox"}}},"Mozilla Firefox Developer Edition (x86)":{friendlyName:"Mozilla Firefox Developer Edition",windowsPath:"%ProgramFiles(x86)%\\Firefox Developer Edition\\firefox.exe",recommendations:{"firefox-devtools.vscode-firefox-debug":{name:"Debugger for Firefox"}}},"Mozilla Firefox":{friendlyName:"Mozilla Firefox",windowsPath:"%ProgramFiles%\\Mozilla Firefox\\firefox.exe",recommendations:{"firefox-devtools.vscode-firefox-debug":{name:"Debugger for Firefox"}}},"Mozilla Firefox Developer Edition":{friendlyName:"Mozilla Firefox Developer Edition",windowsPath:"%ProgramFiles%\\Firefox Developer Edition\\firefox.exe",recommendations:{"firefox-devtools.vscode-firefox-debug":{name:"Debugger for Firefox"}}},cordova:{friendlyName:"Cordova",windowsPath:"%APPDATA%\\npm\\cordova",recommendations:{"msjsdiag.cordova-tools":{name:"Cordova Tools"}}},gcloud:{friendlyName:"Google GCloud CLI",windowsPath:"%ProgramFiles(x86)%\\Google\\Cloud SDK\\google-cloud-sdk\\bin\\gcloud.cmd",recommendations:{"GoogleCloudTools.cloudcode":{name:"Cloud Code"}}},skaffold:{friendlyName:"Skaffold Code to Cluster",recommendations:{"ms-azuretools.vscode-docker":{name:"Docker"},"ms-kubernetes-tools.vscode-kubernetes-tools":{name:"Kubernetes"}}},minikube:{friendlyName:"MiniKube Local Kubernetes Cluster",recommendations:{"ms-azuretools.vscode-docker":{name:"Docker"},"ms-kubernetes-tools.vscode-kubernetes-tools":{name:"Kubernetes"},"ms-vscode-remote.remote-containers":{name:"Dev Containers"}}},podman:{friendlyName:"Podman",recommendations:{"ms-vscode-remote.remote-containers":{name:"Dev Containers"}}},wsl:{friendlyName:"Windows Subsystem for Linux (WSL)",windowsPath:"%WINDIR%\\system32\\lxss\\LxssManager.dll",recommendations:{"ms-vscode-remote.remote-wsl":{name:"WSL"}}}},webExtensionTips:["tyriar.luna-paint","codespaces-contrib.codeswing","ms-vscode.vscode-github-issue-notebooks","esbenp.prettier-vscode","hediet.vscode-drawio"],virtualWorkspaceExtensionTips:{"vscode-vfs":{friendlyName:"Remote Repositories",extensionId:"ms-vscode.remote-repositories",startEntry:{helpLink:"https://aka.ms/vscode-remote/remote-repositories",startConnectLabel:"Remote Repository",startCommand:"remoteHub.continueOn.openRepository",priority:5}}},remoteExtensionTips:{wsl:{friendlyName:"WSL",extensionId:"ms-vscode-remote.remote-wsl",supportedPlatforms:["Windows","Web"],startEntry:{helpLink:"https://aka.ms/vscode-remote/wsl",startConnectLabel:"WSL",startCommand:"remote-wsl.connect",priority:3}},"ssh-remote":{friendlyName:"Remote - SSH",extensionId:"ms-vscode-remote.remote-ssh",supportedPlatforms:["Windows","Linux","Mac"],startEntry:{helpLink:"https://aka.ms/vscode-remote/ssh",startConnectLabel:"SSH ",startCommand:"opensshremotes.openEmptyWindowInCurrentWindow",priority:1}},"dev-container":{friendlyName:"Dev Containers",extensionId:"ms-vscode-remote.remote-containers",supportedPlatforms:["Windows","Linux","Mac"],startEntry:{helpLink:"https://aka.ms/vscode-remote/containers",startConnectLabel:"Dev Container",startCommand:"remote-containers.reopenInContainer",priority:2}},"attached-container":{friendlyName:"Dev Containers",extensionId:"ms-vscode-remote.remote-containers"},codespaces:{friendlyName:"GitHub Codespaces",extensionId:"github.codespaces",startEntry:{helpLink:"https://aka.ms/vscode-remote-codespaces",startConnectLabel:"GitHub Codespace ",startCommand:"github.codespaces.connect",priority:4}},tunnel:{friendlyName:"Remote - Tunnels",extensionId:"ms-vscode.remote-server",startEntry:{helpLink:"https://aka.ms/remote-tunnels-doc",startConnectLabel:"Tunnel",startCommand:"remote-tunnels.connectCurrentWindowToTunnel",priority:0}}},commandPaletteSuggestedCommandIds:["workbench.action.files.openFile","workbench.action.files.openFileFolder","workbench.action.files.openFolder","workbench.action.remote.showMenu","editor.action.formatDocument","editor.action.commentLine","workbench.action.tasks.runTask","workbench.action.openSettings2","workbench.action.selectTheme","workbench.action.openWalkthrough","workbench.action.openIssueReporter"],extensionKeywords:{md:["Markdown"],js:["JavaScript"],jsx:["JavaScript"],es6:["JavaScript"],html:["Html"],ts:["TypeScript"],tsx:["TypeScript"],css:["CSS"],scss:["SASS"],txt:["Text"],php:["PHP"],php3:["PHP"],php4:["PHP"],ph3:["PHP"],ph4:["PHP"],xml:["XML"],py:["Python"],pyc:["Python"],pyd:["Python"],pyo:["Python"],pyw:["Python"],pyz:["Python"],java:["Java"],class:["Java"],jar:["Java"],c:["c","objective c","objective-c"],m:["objective c","objective-c"],mm:["objective c","objective-c"],cpp:["cpp","c plus plus","c","c++"],cc:["cpp","c plus plus","c","c++"],cxx:["cpp","c plus plus","c++"],hh:["cpp","c plus plus","c++"],hpp:["cpp","c++"],h:["cpp","c plus plus","c++","c","objective c","objective-c"],sql:["sql"],sh:["bash"],bash:["bash"],zsh:["bash","zshell"],cs:["c#","csharp"],csproj:["c#","csharp"],sln:["c#","csharp"],go:["go"],sty:["latex"],tex:["latex"],ps:["powershell"],ps1:["powershell"],rs:["rust"],rslib:["rust"],hs:["haskell"],lhs:["haskell"],scm:["scheme"],ss:["scheme"],clj:["clojure"],cljs:["clojure"],cljc:["clojure"],edn:["clojure"],erl:["erlang"],hrl:["erlang"],scala:["scala"],sc:["scala"],pl:["perl"],pm:["perl"],t:["perl"],pod:["perl"],groovy:["groovy"],swift:["swift"],rb:["ruby"],rbw:["ruby"],jl:["julia"],f:["fortran"],for:["fortran"],f90:["fortran"],f95:["fortran"],coffee:["CoffeeScript"],litcoffee:["CoffeeScript"],yaml:["yaml"],yml:["yaml"],dart:["dart"],json:["json"]},extensionAllowedBadgeProviders:["api.travis-ci.com","app.fossa.io","badge.buildkite.com","badge.fury.io","badgen.net","badges.frapsoft.com","badges.gitter.im","cdn.travis-ci.com","ci.appveyor.com","circleci.com","cla.opensource.microsoft.com","codacy.com","codeclimate.com","codecov.io","coveralls.io","david-dm.org","deepscan.io","dev.azure.com","docs.rs","flat.badgen.net","gitlab.com","godoc.org","goreportcard.com","img.shields.io","isitmaintained.com","marketplace.visualstudio.com","nodesecurity.io","opencollective.com","snyk.io","travis-ci.com","travis-ci.org","visualstudio.com","vsmarketplacebadge.apphb.com"],extensionAllowedBadgeProvidersRegex:["^https:\\/\\/github\\.com\\/[^/]+\\/[^/]+\\/(actions\\/)?workflows\\/.*badge\\.svg"],crashReporter:{productName:"VSCode",companyName:"Microsoft"},appCenter:{"win32-x64":"appcenter://code?aid=a4e3233c-699c-46ec-b4f4-9c2a77254662","win32-arm64":"appcenter://code?aid=3712d786-7cc8-4f11-8b08-cc12eab6d4f7","linux-x64":"appcenter://code?aid=fba07a4d-84bd-4fc8-a125-9640fc8ce171",darwin:"appcenter://code?aid=860d6632-f65b-490b-85a8-3e72944f7774","darwin-arm64":"appcenter://code?aid=be71415d-3893-4ae5-b453-e537b9668a10","darwin-universal":"appcenter://code?aid=de75e3cc-e22f-4f42-a03f-1409c21d8af8"},enableTelemetry:!0,aiConfig:{ariaKey:"5bbf946d11a54f6783919c455abaddaf-fd62977b-c92d-4714-a45d-649d06980372-7168"},msftInternalDomains:["redmond.corp.microsoft.com","northamerica.corp.microsoft.com","fareast.corp.microsoft.com","ntdev.corp.microsoft.com","wingroup.corp.microsoft.com","southpacific.corp.microsoft.com","wingroup.windeploy.ntdev.microsoft.com","ddnet.microsoft.com","europe.corp.microsoft.com"],documentationUrl:"https://go.microsoft.com/fwlink/?LinkID=533484#vscode",serverDocumentationUrl:"https://aka.ms/vscode-server-doc",releaseNotesUrl:"https://go.microsoft.com/fwlink/?LinkID=533483#vscode",keyboardShortcutsUrlMac:"https://go.microsoft.com/fwlink/?linkid=832143",keyboardShortcutsUrlLinux:"https://go.microsoft.com/fwlink/?linkid=832144",keyboardShortcutsUrlWin:"https://go.microsoft.com/fwlink/?linkid=832145",introductoryVideosUrl:"https://go.microsoft.com/fwlink/?linkid=832146",tipsAndTricksUrl:"https://go.microsoft.com/fwlink/?linkid=852118",newsletterSignupUrl:"https://www.research.net/r/vsc-newsletter",youTubeUrl:"https://aka.ms/vscode-youtube",requestFeatureUrl:"https://go.microsoft.com/fwlink/?LinkID=533482",reportIssueUrl:"https://github.com/Microsoft/vscode/issues/new",reportMarketplaceIssueUrl:"https://github.com/microsoft/vsmarketplace/issues/new",licenseUrl:"https://go.microsoft.com/fwlink/?LinkID=533485",serverLicenseUrl:"https://aka.ms/vscode-server-license",privacyStatementUrl:"https://go.microsoft.com/fwlink/?LinkId=521839",showTelemetryOptOut:!0,npsSurveyUrl:"https://aka.ms/vscode-nps",checksumFailMoreInfoUrl:"https://go.microsoft.com/fwlink/?LinkId=828886",electronRepository:"Microsoft/vscode-electron-prebuilt",nodejsRepository:"Microsoft/vscode-node",settingsSearchUrl:"https://bingsettingssearch.trafficmanager.net/api/Search",surveys:[{surveyId:"cpp.1",surveyUrl:"https://www.research.net/r/VBVV6C6",languageId:"cpp",editCount:10,userProbability:.15},{surveyId:"java.2",surveyUrl:"https://www.research.net/r/vscodejava",languageId:"java",editCount:10,userProbability:.3},{surveyId:"javascript.1",surveyUrl:"https://www.research.net/r/vscode-js",languageId:"javascript",editCount:10,userProbability:.05},{surveyId:"typescript.1",surveyUrl:"https://www.research.net/r/vscode-ts",languageId:"typescript",editCount:10,userProbability:.05},{surveyId:"csharp.1",surveyUrl:"https://www.research.net/r/8KGJ9V8",languageId:"csharp",editCount:10,userProbability:.1}],extensionsEnabledWithApiProposalVersion:["GitHub.copilot-chat","ms-vscode.vscode-commander","ms-vscode.vscode-copilot-vision"],extensionEnabledApiProposals:{"ms-azuretools.vscode-dev-azurecloudshell":["contribEditSessions"],"ms-vscode.vscode-selfhost-test-provider":["testObserver","testRelatedCode"],"VisualStudioExptTeam.vscodeintellicode-completions":["inlineCompletionsAdditions"],"ms-vsliveshare.vsliveshare":["contribMenuBarHome","contribShareMenu","contribStatusBarItems","diffCommand","documentFiltersExclusive","fileSearchProvider","findTextInFiles","notebookCellExecutionState","notebookLiveShare","terminalDimensions","terminalDataWriteEvent","textSearchProvider"],"ms-vscode.js-debug":["portsAttributes","findTextInFiles","workspaceTrust","tunnels"],"ms-toolsai.vscode-ai-remote":["resolvers"],"ms-python.python":["codeActionAI","contribEditorContentMenu","quickPickSortByLabel","portsAttributes","testObserver","quickPickItemTooltip","terminalDataWriteEvent","terminalExecuteCommandEvent","notebookReplDocument","notebookVariableProvider","terminalShellEnv"],"ms-python.vscode-python-envs":["terminalShellEnv"],"ms-dotnettools.dotnet-interactive-vscode":["notebookMessaging"],"GitHub.codespaces":["contribEditSessions","contribMenuBarHome","contribRemoteHelp","contribViewsRemote","resolvers","tunnels","terminalDataWriteEvent","treeViewReveal","notebookKernelSource"],"ms-vscode.azure-repos":["extensionRuntime","fileSearchProvider","textSearchProvider"],"ms-vscode.remote-repositories":["canonicalUriProvider","contribEditSessions","contribRemoteHelp","contribMenuBarHome","contribViewsRemote","contribViewsWelcome","contribShareMenu","documentFiltersExclusive","editSessionIdentityProvider","extensionRuntime","fileSearchProvider","quickPickSortByLabel","workspaceTrust","shareProvider","scmActionButton","scmSelectedProvider","scmValidation","textSearchProvider","timeline"],"ms-vscode-remote.remote-wsl":["resolvers","contribRemoteHelp","contribViewsRemote","telemetry"],"ms-vscode-remote.remote-ssh":["resolvers","tunnels","terminalDataWriteEvent","contribRemoteHelp","contribViewsRemote","telemetry"],"ms-vscode.remote-server":["resolvers","tunnels","contribViewsWelcome"],"ms-vscode.remote-explorer":["contribRemoteHelp","contribViewsRemote","extensionsAny"],"ms-vscode-remote.remote-containers":["contribEditSessions","resolvers","portsAttributes","tunnels","workspaceTrust","terminalDimensions","contribRemoteHelp","contribViewsRemote"],"ms-vscode.js-debug-nightly":["portsAttributes","findTextInFiles","workspaceTrust","tunnels"],"ms-vscode.lsif-browser":["documentFiltersExclusive"],"ms-vscode.vscode-speech":["speech"],"GitHub.vscode-pull-request-github":["activeComment","codiconDecoration","codeActionRanges","commentingRangeHint","commentReactor","commentReveal","commentThreadApplicability","contribAccessibilityHelpContent","contribCommentEditorActionsMenu","contribCommentPeekContext","contribCommentThreadAdditionalMenu","contribCommentsViewThreadMenus","contribEditorContentMenu","contribMultiDiffEditorMenus","contribShareMenu","diffCommand","quickDiffProvider","shareProvider","tabInputTextMerge","tokenInformation","treeViewMarkdownMessage"],"GitHub.copilot":["inlineCompletionsAdditions"],"GitHub.copilot-nightly":["inlineCompletionsAdditions"],"GitHub.copilot-chat":["interactive","terminalDataWriteEvent","terminalExecuteCommandEvent","terminalSelection","terminalQuickFixProvider","chatParticipantAdditions","defaultChatParticipant","embeddings","chatEditing","chatProvider","mappedEditsProvider","aiRelatedInformation","aiSettingsSearch","codeActionAI","findTextInFiles","findTextInFiles2","textSearchProvider","textSearchProvider2","activeComment","commentReveal","contribSourceControlInputBoxMenu","contribCommentEditorActionsMenu","contribCommentThreadAdditionalMenu","contribCommentsViewThreadMenus","newSymbolNamesProvider","findFiles2","chatReferenceDiagnostic","extensionsAny","authLearnMore","testObserver","aiTextSearchProvider","documentFiltersExclusive","chatParticipantPrivate","contribDebugCreateConfiguration","inlineEdit","inlineCompletionsAdditions","chatReferenceBinaryData","languageModelSystem","languageModelCapabilities","languageModelDataPart","chatStatusItem","taskProblemMatcherStatus"],"GitHub.remotehub":["contribRemoteHelp","contribMenuBarHome","contribViewsRemote","contribViewsWelcome","documentFiltersExclusive","extensionRuntime","fileSearchProvider","quickPickSortByLabel","workspaceTrust","scmSelectedProvider","scmValidation","textSearchProvider","timeline"],"ms-python.gather":["notebookCellExecutionState"],"ms-python.vscode-pylance":["mcpConfigurationProvider","terminalShellEnv"],"ms-python.debugpy":["contribViewsWelcome","debugVisualization","portsAttributes"],"ms-toolsai.jupyter-renderers":["contribNotebookStaticPreloads"],"ms-toolsai.jupyter":["notebookDeprecated","notebookMessaging","notebookMime","portsAttributes","quickPickSortByLabel","notebookKernelSource","interactiveWindow","notebookControllerAffinityHidden","contribNotebookStaticPreloads","quickPickItemTooltip","notebookExecution","notebookCellExecution","notebookVariableProvider","notebookReplDocument"],"donjayamanne.kusto":["notebookVariableProvider"],"ms-toolsai.tensorboard":["portsAttributes"],"dbaeumer.vscode-eslint":[],"ms-vscode.azure-sphere-tools-ui":["tunnels"],"ms-azuretools.vscode-azureappservice":["terminalDataWriteEvent"],"ms-vscode.anycode":["extensionsAny"],"ms-vscode.cpptools":["terminalDataWriteEvent","chatParticipantAdditions"],"vscjava.vscode-java-pack":[],"ms-dotnettools.csdevkit":["inlineCompletionsAdditions"],"ms-dotnettools.vscodeintellicode-csharp":["inlineCompletionsAdditions"],"microsoft-IsvExpTools.powerplatform-vscode":["fileSearchProvider","textSearchProvider"],"microsoft-IsvExpTools.powerplatform-vscode-preview":["fileSearchProvider","textSearchProvider"],"TeamsDevApp.ms-teams-vscode-extension":["chatParticipantAdditions","languageModelSystem"],"ms-toolsai.datawrangler":[],"ms-vscode.vscode-commander":[],"ms-vscode.vscode-websearchforcopilot":[],"ms-vscode.vscode-copilot-vision":["chatReferenceBinaryData","codeActionAI"],"ms-autodev.vscode-autodev":["chatParticipantAdditions"]},tasConfig:{endpoint:"https://default.exp-tas.com/vscode/ab",telemetryEventName:"query-expfeature",assignmentContextTelemetryPropertyName:"abexp.assignmentcontext"},extensionKind:{"Shan.code-settings-sync":["ui"],"shalldie.background":["ui"],"techer.open-in-browser":["ui"],"CoenraadS.bracket-pair-colorizer-2":["ui"],"CoenraadS.bracket-pair-colorizer":["ui","workspace"],"hiro-sun.vscode-emacs":["ui","workspace"],"hnw.vscode-auto-open-markdown-preview":["ui","workspace"],"wayou.vscode-todo-highlight":["ui","workspace"],"aaron-bond.better-comments":["ui","workspace"],"vscodevim.vim":["ui"],"ollyhayes.colmak-vim":["ui"]},extensionPointExtensionKind:{typescriptServerPlugins:["workspace"]},extensionSyncedKeys:{"ritwickdey.liveserver":["liveServer.setup.version"]},extensionVirtualWorkspacesSupport:{"esbenp.prettier-vscode":{default:!1},"msjsdiag.debugger-for-chrome":{default:!1},"redhat.java":{default:!1},"HookyQR.beautify":{default:!1},"ritwickdey.LiveServer":{default:!1},"VisualStudioExptTeam.vscodeintellicode":{default:!1},"octref.vetur":{default:!1},"formulahendry.code-runner":{default:!1},"xdebug.php-debug":{default:!1},"ms-mssql.mssql":{default:!1},"christian-kohler.path-intellisense":{default:!1},"eg2.tslint":{default:!1},"eg2.vscode-npm-script":{default:!1},"donjayamanne.githistory":{default:!1},"Zignd.html-css-class-completion":{default:!1},"christian-kohler.npm-intellisense":{default:!1},"EditorConfig.EditorConfig":{default:!1},"austin.code-gnu-global":{default:!1},"johnpapa.Angular2":{default:!1},"ms-vscode.vscode-typescript-tslint-plugin":{default:!1},"DotJoshJohnson.xml":{default:!1},"techer.open-in-browser":{default:!1},"tht13.python":{default:!1},"bmewburn.vscode-intelephense-client":{default:!1},"Angular.ng-template":{default:!1},"xdebug.php-pack":{default:!1},"dbaeumer.jshint":{default:!1},"yzhang.markdown-all-in-one":{default:!1},"Dart-Code.flutter":{default:!1},"streetsidesoftware.code-spell-checker":{default:!1},"rebornix.Ruby":{default:!1},"ms-vscode.sublime-keybindings":{default:!1},"mitaki28.vscode-clang":{default:!1},"steoates.autoimport":{default:!1},"donjayamanne.python-extension-pack":{default:!1},"shd101wyy.markdown-preview-enhanced":{default:!1},"mikestead.dotenv":{default:!1},"pranaygp.vscode-css-peek":{default:!1},"ikappas.phpcs":{default:!1},"platformio.platformio-ide":{default:!1},"jchannon.csharpextensions":{default:!1},"gruntfuggly.todo-tree":{default:!1}},linkProtectionTrustedDomains:["https://*.visualstudio.com","https://*.microsoft.com","https://aka.ms","https://*.gallerycdn.vsassets.io","https://*.github.com","https://login.microsoftonline.com","https://*.vscode.dev","https://*.github.dev","https://gh.io","https://portal.azure.com","https://raw.githubusercontent.com","https://private-user-images.githubusercontent.com","https://avatars.githubusercontent.com"],trustedExtensionAuthAccess:{github:["vscode.github","github.remotehub","ms-vscode.remote-server","github.vscode-pull-request-github","github.codespaces","github.copilot","github.copilot-chat","ms-vsliveshare.vsliveshare","ms-azuretools.vscode-azure-github-copilot"],"github-enterprise":["vscode.github","github.remotehub","ms-vscode.remote-server","github.vscode-pull-request-github","github.codespaces","github.copilot","github.copilot-chat","ms-vsliveshare.vsliveshare","ms-azuretools.vscode-azure-github-copilot"],microsoft:["ms-vscode.azure-repos","ms-vscode.remote-server","ms-vsliveshare.vsliveshare","ms-azuretools.vscode-azure-github-copilot","ms-azuretools.vscode-azureresourcegroups","ms-azuretools.vscode-dev-azurecloudshell","ms-edu.vscode-learning","ms-toolsai.vscode-ai","ms-toolsai.vscode-ai-remote"],"microsoft-sovereign-cloud":["ms-vscode.azure-repos","ms-vscode.remote-server","ms-vsliveshare.vsliveshare","ms-azuretools.vscode-azure-github-copilot","ms-azuretools.vscode-azureresourcegroups","ms-azuretools.vscode-dev-azurecloudshell","ms-edu.vscode-learning","ms-toolsai.vscode-ai","ms-toolsai.vscode-ai-remote"],"__GitHub.copilot-chat":["ms-azuretools.vscode-azure-github-copilot","github.vscode-pull-request-github"]},trustedExtensionProtocolHandlers:["vscode.git","vscode.github-authentication","vscode.microsoft-authentication"],inheritAuthAccountPreference:{"github.copilot":["github.copilot-chat"]},auth:{loginUrl:"https://login.microsoftonline.com/common/oauth2/authorize",tokenUrl:"https://login.microsoftonline.com/common/oauth2/token",redirectUrl:"https://vscode-redirect.azurewebsites.net/",clientId:"aebc6443-996d-45c2-90f0-388ff96faa56"},"configurationSync.store":{url:"https://vscode-sync.trafficmanager.net/",stableUrl:"https://vscode-sync.trafficmanager.net/",insidersUrl:"https://vscode-sync-insiders.trafficmanager.net/",canSwitch:!1,authenticationProviders:{github:{scopes:["user:email"]},microsoft:{scopes:["openid","profile","email","offline_access"]}}},"editSessions.store":{url:"https://vscode-sync.trafficmanager.net/",authenticationProviders:{microsoft:{scopes:["openid","profile","email","offline_access"]},github:{scopes:["user:email"]}}},tunnelServerQualities:{stable:{serverApplicationName:"code-server"},exploration:{serverApplicationName:"code-server-exploration"},insider:{serverApplicationName:"code-server-insiders"}},tunnelApplicationName:"code-tunnel",tunnelApplicationConfig:{editorWebUrl:"https://vscode.dev",extension:{friendlyName:"Remote - Tunnels",extensionId:"ms-vscode.remote-server"},authenticationProviders:{github:{scopes:["user:email","read:org"]},microsoft:{scopes:["46da2f7e-b5ef-422a-88d4-2a7f9de6a0b2/.default","profile","openid"]}}},win32TunnelServiceMutex:"vscode-tunnelservice",win32TunnelMutex:"vscode-tunnel",commonlyUsedSettings:["files.autoSave","editor.fontSize","editor.fontFamily","GitHub.copilot.manageExtension","editor.tabSize","editor.renderWhitespace","editor.cursorStyle","editor.multiCursorModifier","editor.insertSpaces","editor.wordWrap","files.exclude","files.associations","workbench.editor.enablePreview"],aiGeneratedWorkspaceTrust:{title:"This workspace was generated by GitHub Copilot",checkboxText:"Trust the contents of all files in this workspace",trustOption:"Yes, I trust the contents",dontTrustOption:"No, I don't trust the contents",startupTrustRequestLearnMore:"If you don't trust the contents of the files generated by GitHub Copilot, we recommend continuing in restricted mode. See [our docs](https://aka.ms/vscode-workspace-trust) to learn more. "},defaultChatAgent:{extensionId:"GitHub.copilot",chatExtensionId:"GitHub.copilot-chat",documentationUrl:"https://aka.ms/github-copilot-overview",termsStatementUrl:"https://aka.ms/github-copilot-terms-statement",privacyStatementUrl:"https://aka.ms/github-copilot-privacy-statement",skusDocumentationUrl:"https://aka.ms/github-copilot-plans",publicCodeMatchesUrl:"https://aka.ms/github-copilot-match-public-code",manageSettingsUrl:"https://aka.ms/github-copilot-settings",managePlanUrl:"https://aka.ms/github-copilot-manage-plan",manageOverageUrl:"https://aka.ms/github-copilot-manage-overage",upgradePlanUrl:"https://aka.ms/github-copilot-upgrade-plan",providerId:"github",providerName:"GitHub",enterpriseProviderId:"github-enterprise",enterpriseProviderName:"GHE.com",providerUriSetting:"github-enterprise.uri",providerScopes:[["user:email"],["read:user"],["read:user","user:email","repo","workflow"]],entitlementUrl:"https://api.github.com/copilot_internal/user",entitlementSignupLimitedUrl:"https://api.github.com/copilot_internal/subscribe_limited_user",chatQuotaExceededContext:"github.copilot.chat.quotaExceeded",completionsQuotaExceededContext:"github.copilot.completions.quotaExceeded",walkthroughCommand:"github.copilot.open.walkthrough",completionsMenuCommand:"github.copilot.toggleStatusMenu",completionsRefreshTokenCommand:"github.copilot.signIn",chatRefreshTokenCommand:"github.copilot.refreshToken",completionsAdvancedSetting:"github.copilot.advanced",completionsEnablementSetting:"github.copilot.enable",nextEditSuggestionsSetting:"github.copilot.nextEditSuggestions.enabled"},chatParticipantRegistry:"https://main.vscode-cdn.net/extensions/chat.json",remoteDefaultExtensionsIfInstalledLocally:["GitHub.copilot","GitHub.copilot-chat","GitHub.vscode-pull-request-github"],extensionConfigurationPolicy:{"github.copilot.nextEditSuggestions.enabled":{name:"CopilotNextEditSuggestions",minimumVersion:"1.99"}},builtInExtensions:[{name:"ms-vscode.js-debug-companion",version:"1.1.3",sha256:"7380a890787452f14b2db7835dfa94de538caf358ebc263f9d46dd68ac52de93",repo:"https://github.com/microsoft/vscode-js-debug-companion",metadata:{id:"99cb0b7f-7354-4278-b8da-6cc79972169d",publisherId:{publisherId:"5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee",publisherName:"ms-vscode",displayName:"Microsoft",flags:"verified"},publisherDisplayName:"Microsoft"}},{name:"ms-vscode.js-debug",version:"1.100.1",sha256:"8c2218df3422d45b95e96d9d28cdc4aa4426a2799aaaedd862d3f60ecab03844",repo:"https://github.com/microsoft/vscode-js-debug",metadata:{id:"25629058-ddac-4e17-abba-74678e126c5d",publisherId:{publisherId:"5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee",publisherName:"ms-vscode",displayName:"Microsoft",flags:"verified"},publisherDisplayName:"Microsoft"}},{name:"ms-vscode.vscode-js-profile-table",version:"1.0.10",sha256:"7361748ddf9fd09d8a2ed1f2a2d7376a2cf9aae708692820b799708385c38e08",repo:"https://github.com/microsoft/vscode-js-profile-visualizer",metadata:{id:"7e52b41b-71ad-457b-ab7e-0620f1fc4feb",publisherId:{publisherId:"5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee",publisherName:"ms-vscode",displayName:"Microsoft",flags:"verified"},publisherDisplayName:"Microsoft"}}],version:"1.100.2",commit:"848b80aeb52026648a8ff9f7c45a9b0a80641e2e",date:"2025-05-14T21:47:40.416Z"},Object.keys(de).length===0&&Object.assign(de,{version:"1.95.0-dev",nameShort:"Code - OSS Dev",nameLong:"Code - OSS Dev",applicationName:"code-oss",dataFolderName:".vscode-oss",urlProtocol:"code-oss",reportIssueUrl:"https://github.com/microsoft/vscode/issues/new",licenseName:"MIT",licenseUrl:"https://github.com/microsoft/vscode/blob/main/LICENSE.txt",serverLicenseUrl:"https://github.com/microsoft/vscode/blob/main/LICENSE.txt"});var lh=de,hh=class{constructor(){this.a=new Map}add(e,t){Hs(nt(e)),Hs(Wa(t)),Hs(!this.a.has(e),"There is already an extension with this id"),this.a.set(e,t)}knows(e){return this.a.has(e)}as(e){return this.a.get(e)||null}dispose(){this.a.forEach(e=>{Vs(e.dispose)&&e.dispose()}),this.a.clear()}},uh=new hh,pf=new An("terminalTabFocusMode",!1,!0),Gn;(function(e){e.AutomationProfile="terminal.integrated.automationProfile.",e.DefaultProfile="terminal.integrated.defaultProfile.",e.Profiles="terminal.integrated.profiles."})(Gn||(Gn={}));var Xn;(function(e){e.SendKeybindingsToShell="terminal.integrated.sendKeybindingsToShell",e.AutomationProfileLinux="terminal.integrated.automationProfile.linux",e.AutomationProfileMacOs="terminal.integrated.automationProfile.osx",e.AutomationProfileWindows="terminal.integrated.automationProfile.windows",e.ProfilesWindows="terminal.integrated.profiles.windows",e.ProfilesMacOs="terminal.integrated.profiles.osx",e.ProfilesLinux="terminal.integrated.profiles.linux",e.DefaultProfileLinux="terminal.integrated.defaultProfile.linux",e.DefaultProfileMacOs="terminal.integrated.defaultProfile.osx",e.DefaultProfileWindows="terminal.integrated.defaultProfile.windows",e.UseWslProfiles="terminal.integrated.useWslProfiles",e.TabsDefaultColor="terminal.integrated.tabs.defaultColor",e.TabsDefaultIcon="terminal.integrated.tabs.defaultIcon",e.TabsEnabled="terminal.integrated.tabs.enabled",e.TabsEnableAnimation="terminal.integrated.tabs.enableAnimation",e.TabsHideCondition="terminal.integrated.tabs.hideCondition",e.TabsShowActiveTerminal="terminal.integrated.tabs.showActiveTerminal",e.TabsShowActions="terminal.integrated.tabs.showActions",e.TabsLocation="terminal.integrated.tabs.location",e.TabsFocusMode="terminal.integrated.tabs.focusMode",e.MacOptionIsMeta="terminal.integrated.macOptionIsMeta",e.MacOptionClickForcesSelection="terminal.integrated.macOptionClickForcesSelection",e.AltClickMovesCursor="terminal.integrated.altClickMovesCursor",e.CopyOnSelection="terminal.integrated.copyOnSelection",e.EnableMultiLinePasteWarning="terminal.integrated.enableMultiLinePasteWarning",e.DrawBoldTextInBrightColors="terminal.integrated.drawBoldTextInBrightColors",e.FontFamily="terminal.integrated.fontFamily",e.FontSize="terminal.integrated.fontSize",e.LetterSpacing="terminal.integrated.letterSpacing",e.LineHeight="terminal.integrated.lineHeight",e.MinimumContrastRatio="terminal.integrated.minimumContrastRatio",e.TabStopWidth="terminal.integrated.tabStopWidth",e.FastScrollSensitivity="terminal.integrated.fastScrollSensitivity",e.MouseWheelScrollSensitivity="terminal.integrated.mouseWheelScrollSensitivity",e.BellDuration="terminal.integrated.bellDuration",e.FontWeight="terminal.integrated.fontWeight",e.FontWeightBold="terminal.integrated.fontWeightBold",e.CursorBlinking="terminal.integrated.cursorBlinking",e.CursorStyle="terminal.integrated.cursorStyle",e.CursorStyleInactive="terminal.integrated.cursorStyleInactive",e.CursorWidth="terminal.integrated.cursorWidth",e.Scrollback="terminal.integrated.scrollback",e.DetectLocale="terminal.integrated.detectLocale",e.DefaultLocation="terminal.integrated.defaultLocation",e.GpuAcceleration="terminal.integrated.gpuAcceleration",e.TerminalTitleSeparator="terminal.integrated.tabs.separator",e.TerminalTitle="terminal.integrated.tabs.title",e.TerminalDescription="terminal.integrated.tabs.description",e.RightClickBehavior="terminal.integrated.rightClickBehavior",e.MiddleClickBehavior="terminal.integrated.middleClickBehavior",e.Cwd="terminal.integrated.cwd",e.ConfirmOnExit="terminal.integrated.confirmOnExit",e.ConfirmOnKill="terminal.integrated.confirmOnKill",e.EnableBell="terminal.integrated.enableBell",e.EnableVisualBell="terminal.integrated.enableVisualBell",e.CommandsToSkipShell="terminal.integrated.commandsToSkipShell",e.AllowChords="terminal.integrated.allowChords",e.AllowMnemonics="terminal.integrated.allowMnemonics",e.TabFocusMode="terminal.integrated.tabFocusMode",e.EnvMacOs="terminal.integrated.env.osx",e.EnvLinux="terminal.integrated.env.linux",e.EnvWindows="terminal.integrated.env.windows",e.EnvironmentChangesIndicator="terminal.integrated.environmentChangesIndicator",e.EnvironmentChangesRelaunch="terminal.integrated.environmentChangesRelaunch",e.ShowExitAlert="terminal.integrated.showExitAlert",e.SplitCwd="terminal.integrated.splitCwd",e.WindowsEnableConpty="terminal.integrated.windowsEnableConpty",e.WindowsUseConptyDll="terminal.integrated.windowsUseConptyDll",e.WordSeparators="terminal.integrated.wordSeparators",e.EnableFileLinks="terminal.integrated.enableFileLinks",e.AllowedLinkSchemes="terminal.integrated.allowedLinkSchemes",e.UnicodeVersion="terminal.integrated.unicodeVersion",e.EnablePersistentSessions="terminal.integrated.enablePersistentSessions",e.PersistentSessionReviveProcess="terminal.integrated.persistentSessionReviveProcess",e.HideOnStartup="terminal.integrated.hideOnStartup",e.HideOnLastClosed="terminal.integrated.hideOnLastClosed",e.CustomGlyphs="terminal.integrated.customGlyphs",e.RescaleOverlappingGlyphs="terminal.integrated.rescaleOverlappingGlyphs",e.PersistentSessionScrollback="terminal.integrated.persistentSessionScrollback",e.InheritEnv="terminal.integrated.inheritEnv",e.ShowLinkHover="terminal.integrated.showLinkHover",e.IgnoreProcessNames="terminal.integrated.ignoreProcessNames",e.ShellIntegrationEnabled="terminal.integrated.shellIntegration.enabled",e.ShellIntegrationShowWelcome="terminal.integrated.shellIntegration.showWelcome",e.ShellIntegrationDecorationsEnabled="terminal.integrated.shellIntegration.decorationsEnabled",e.ShellIntegrationEnvironmentReporting="terminal.integrated.shellIntegration.environmentReporting",e.EnableImages="terminal.integrated.enableImages",e.SmoothScrolling="terminal.integrated.smoothScrolling",e.IgnoreBracketedPasteMode="terminal.integrated.ignoreBracketedPasteMode",e.FocusAfterRun="terminal.integrated.focusAfterRun",e.FontLigaturesEnabled="terminal.integrated.fontLigatures.enabled",e.FontLigaturesFeatureSettings="terminal.integrated.fontLigatures.featureSettings",e.FontLigaturesFallbackLigatures="terminal.integrated.fontLigatures.fallbackLigatures",e.DeveloperPtyHostLatency="terminal.integrated.developer.ptyHost.latency",e.DeveloperPtyHostStartupDelay="terminal.integrated.developer.ptyHost.startupDelay",e.DevMode="terminal.integrated.developer.devMode"})(Xn||(Xn={}));var Jn;(function(e){e.Bash="bash",e.Fish="fish",e.Sh="sh",e.Csh="csh",e.Ksh="ksh",e.Zsh="zsh"})(Jn||(Jn={}));var Yn;(function(e){e.CommandPrompt="cmd",e.Wsl="wsl",e.GitBash="gitbash"})(Yn||(Yn={}));var Qn;(function(e){e.PowerShell="pwsh",e.Python="python",e.Julia="julia",e.NuShell="nu",e.Node="node"})(Qn||(Qn={}));var mt;(function(e){e[e.Api=0]="Api",e[e.Process=1]="Process",e[e.Sequence=2]="Sequence",e[e.Config=3]="Config"})(mt||(mt={}));var Ke;(function(e){e.LocalPty="localPty",e.PtyHost="ptyHost",e.PtyHostWindow="ptyHostWindow",e.Logger="logger",e.Heartbeat="heartbeat"})(Ke||(Ke={}));var Kn;(function(e){e.Cwd="cwd",e.InitialCwd="initialCwd",e.FixedDimensions="fixedDimensions",e.Title="title",e.ShellType="shellType",e.HasChildProcesses="hasChildProcesses",e.ResolvedShellLaunchConfig="resolvedShellLaunchConfig",e.OverrideDimensions="overrideDimensions",e.FailedShellIntegrationActivation="failedShellIntegrationActivation",e.UsedShellIntegrationInjection="usedShellIntegrationInjection",e.ShellIntegrationInjectionFailureReason="shellIntegrationInjectionFailureReason"})(Kn||(Kn={}));var mf=Oe("ptyService"),Ai;(function(e){e[e.BeatInterval=5e3]="BeatInterval",e[e.ConnectingBeatInterval=2e4]="ConnectingBeatInterval",e[e.FirstWaitMultiplier=1.2]="FirstWaitMultiplier",e[e.SecondWaitMultiplier=1]="SecondWaitMultiplier",e[e.CreateProcessTimeout=5e3]="CreateProcessTimeout"})(Ai||(Ai={}));var Zn;(function(e){e[e.Panel=1]="Panel",e[e.Editor=2]="Editor"})(Zn||(Zn={}));var eo;(function(e){e.TerminalView="view",e.Editor="editor"})(eo||(eo={}));var to;(function(e){e[e.GraceTime=6e4]="GraceTime",e[e.ShortGraceTime=6e3]="ShortGraceTime"})(to||(to={}));var so;(function(e){e[e.HighWatermarkChars=1e5]="HighWatermarkChars",e[e.LowWatermarkChars=5e3]="LowWatermarkChars",e[e.CharCountAckSize=5e3]="CharCountAckSize"})(so||(so={}));var io;(function(e){e.GitBash="Git Bash",e.Pwsh="PowerShell"})(io||(io={}));var ro;(function(e){e[e.Off=0]="Off",e[e.FinalTerm=1]="FinalTerm",e[e.VSCode=2]="VSCode"})(ro||(ro={}));var no;(function(e){e.InjectionSettingDisabled="injectionSettingDisabled",e.NoExecutable="noExecutable",e.FeatureTerminal="featureTerminal",e.IgnoreShellIntegrationFlag="ignoreShellIntegrationFlag",e.Winpty="winpty",e.UnsupportedArgs="unsupportedArgs",e.UnsupportedShell="unsupportedShell",e.FailedToSetStickyBit="failedToSetStickyBit",e.FailedToCreateTmpDir="failedToCreateTmpDir"})(no||(no={}));var oo;(function(e){e[e.Unknown=0]="Unknown",e[e.Shutdown=1]="Shutdown",e[e.Process=2]="Process",e[e.User=3]="User",e[e.Extension=4]="Extension"})(oo||(oo={}));var fh={Backend:"workbench.contributions.terminal.processBackend"},dh=class{constructor(){this.a=new Map}get backends(){return this.a}registerTerminalBackend(e){const t=this.b(e.remoteAuthority);if(this.a.has(t))throw new Error(`A terminal backend with remote authority '${t}' was already registered.`);this.a.set(t,e)}getTerminalBackend(e){return this.a.get(this.b(e))}b(e){return e?.toLowerCase()??""}};uh.add(fh.Backend,new dh);var gf=Oe("localPtyService"),vf=Oe("terminalLogService"),ph=class extends G{constructor(){super(),this.a=this.B(new C),this.onBeat=this.a.event;const e=setInterval(()=>{this.a.fire()},Ai.BeatInterval);this.B(be(()=>clearInterval(e)))}};import{execFile as ao,exec as mh}from"child_process";import{userInfo as gh}from"os";import*as co from"os";var lo=/^\d+$/,vh=/^Microsoft.PowerShell_.*/,bh=/^Microsoft.PowerShellPreview_.*/,ho;(function(e){e[e.x64=0]="x64",e[e.x86=1]="x86",e[e.ARM=2]="ARM"})(ho||(ho={}));var Ut;switch(process.arch){case"ia32":Ut=1;break;case"arm":case"arm64":Ut=2;break;default:Ut=0;break}var gt;process.env.PROCESSOR_ARCHITEW6432?gt=process.env.PROCESSOR_ARCHITEW6432==="ARM64"?2:0:process.env.PROCESSOR_ARCHITECTURE==="ARM64"?gt=2:process.env.PROCESSOR_ARCHITECTURE==="X86"?gt=1:gt=0;var Bt=class{constructor(e,t,s){this.exePath=e,this.displayName=t,this.a=s}async exists(){return this.a===void 0&&(this.a=await $e.existsFile(this.exePath)),this.a}};function yh({useAlternateBitness:e=!1}={}){return e?Ut===0?process.env["ProgramFiles(x86)"]||null:gt===0&&process.env.ProgramW6432||null:process.env.ProgramFiles||null}async function ks({useAlternateBitness:e=!1,findPreview:t=!1}={}){const s=yh({useAlternateBitness:e});if(!s)return null;const i=O(s,"PowerShell");if(!await $e.existsDirectory(i))return null;let r=-1,n=null;for(const l of await fi.readdir(i)){let c=-1;if(t){const h=l.indexOf("-");if(h<0)continue;const f=l.substring(0,h);if(!lo.test(f)||l.substring(h+1)!=="preview")continue;c=parseInt(f,10)}else{if(!lo.test(l))continue;c=parseInt(l,10)}if(c<=r)continue;const u=O(i,l,"pwsh.exe");await $e.existsFile(u)&&(n=u,r=c)}if(!n)return null;const o=s.includes("x86")?" (x86)":"",a=t?" Preview":"";return new Bt(n,`PowerShell${a}${o}`,!0)}async function uo({findPreview:e}={}){if(!process.env.LOCALAPPDATA)return null;const t=O(process.env.LOCALAPPDATA,"Microsoft","WindowsApps");if(!await $e.existsDirectory(t))return null;const{pwshMsixDirRegex:s,pwshMsixName:i}=e?{pwshMsixDirRegex:bh,pwshMsixName:"PowerShell Preview (Store)"}:{pwshMsixDirRegex:vh,pwshMsixName:"PowerShell (Store)"};for(const r of await fi.readdir(t))if(s.test(r)){const n=O(t,r,"pwsh.exe");return new Bt(n,i)}return null}function wh(){const e=O(co.homedir(),".dotnet","tools","pwsh.exe");return new Bt(e,".NET Core PowerShell Global Tool")}function Ch(){const e=O(co.homedir(),"scoop","apps"),t=O(e,"pwsh","current","pwsh.exe");return new Bt(t,"PowerShell (Scoop)")}function xh(){const e=O(process.env.windir,Ut===1&&gt!==1?"SysNative":"System32","WindowsPowerShell","v1.0","powershell.exe");return new Bt(e,"Windows PowerShell",!0)}async function*Eh(){let e=await ks();e&&(yield e),e=await ks({useAlternateBitness:!0}),e&&(yield e),e=await uo(),e&&(yield e),e=wh(),e&&(yield e),e=await ks({findPreview:!0}),e&&(yield e),e=await uo({findPreview:!0}),e&&(yield e),e=await ks({useAlternateBitness:!0,findPreview:!0}),e&&(yield e),e=await Ch(),e&&(yield e),e=xh(),e&&(yield e)}async function*kh(){for await(const e of Eh())await e.exists()&&(yield e)}async function Sh(){for await(const e of kh())return e;return null}async function Ph(e,t){return e===1?N?Dh():cl(t):Ah(e,t)}var Di=null;function Ah(e,t){if(St&&e===2||Fe&&e===3)return"/bin/bash";if(!Di){let s;if(N)s="/bin/bash";else{if(s=t.SHELL,!s)try{s=gh().shell}catch{}s||(s="sh"),s==="/bin/false"&&(s="/bin/bash")}Di=s}return Di}var Li=null;async function Dh(){return Li||(Li=(await Sh()).exePath),Li}var $i=class extends G{constructor(t,s){super(),this.h=s,this.a=0,this.c=new Map,this.f=new Map,this.g=this.B(new C),this.onCreateRequest=this.g.event,this.b=t===void 0?15e3:t,this.B(be(()=>{for(const i of this.f.values())Ne(i)}))}createRequest(t){return new Promise((s,i)=>{const r=++this.a;this.c.set(r,s),this.g.fire({requestId:r,...t});const n=new ti;Ue(this.b,n.token).then(()=>i(`Request ${r} timed out (${this.b}ms)`)),this.f.set(r,[be(()=>n.cancel())])})}acceptReply(t,s){const i=this.c.get(t);i?(this.c.delete(t),Ne(this.f.get(t)||[]),this.f.delete(t),i(s)):this.h.warn(`RequestStore#acceptReply was called without receiving a matching request ${t}`)}};$i=__decorate([__param(1,Ye)],$i);var Lh=class{constructor(e){this.b=e,this.a=new Map}dispose(){for(const e of this.a.values())e.dispose()}startBuffering(e,t,s=5){const i=t(r=>{const n=typeof r=="string"?r:r.data;let o=this.a.get(e);if(o){o.data.push(n);return}const a=setTimeout(()=>this.flushBuffer(e),s);o={data:[n],timeoutId:a,dispose:()=>{clearTimeout(a),this.flushBuffer(e),i.dispose()}},this.a.set(e,o)});return i}stopBuffering(e){this.a.get(e)?.dispose()}flushBuffer(e){const t=this.a.get(e);t&&(this.a.delete(e),this.b(e,t.data.join("")))}};function $h(e){let t=e;t.includes("\\")&&(t=t.replace(/\\/g,"\\\\"));const s=/[\`\$\|\&\>\~\#\!\^\*\;\<\"\']/g;return t=t.replace(s,""),`'${t}'`}function Mh(e){return e.match(/^['"].*['"]$/)&&(e=e.substring(1,e.length-1)),fr===1&&e&&e[1]===":"?e[0].toUpperCase()+e.substring(1):e}import*as Ss from"os";var Ee;(function(e){e[e.Replace=1]="Replace",e[e.Append=2]="Append",e[e.Prepend=3]="Prepend"})(Ee||(Ee={}));function Ih(e){return new Map(e)}function Oh(e){return new Map(e??[])}function _h(e){return new Map(e.map(t=>[t[0],{map:Ih(t[1]),descriptionMap:Oh(t[2])}]))}var Rh=new Map([[Ee.Append,"APPEND"],[Ee.Prepend,"PREPEND"],[Ee.Replace,"REPLACE"]]),Nh=class{constructor(e){this.collections=e,this.a=new Map,this.b=new Map,e.forEach((t,s)=>{this.d(t,s);const i=t.map.entries();let r=i.next();for(;!r.done;){const n=r.value[1],o=r.value[0];let a=this.a.get(o);if(a||(a=[],this.a.set(o,a)),a.length>0&&a[0].type===Ee.Replace){r=i.next();continue}const l={extensionIdentifier:s,value:n.value,type:n.type,scope:n.scope,variable:n.variable,options:n.options};l.scope||delete l.scope,a.unshift(l),r=i.next()}})}async applyToProcessEnvironment(e,t,s){let i;N&&(i={},Object.keys(e).forEach(r=>i[r.toLowerCase()]=r));for(const[r,n]of this.getVariableMap(t)){const o=N&&i[r.toLowerCase()]||r;for(const a of n){const l=s?await s(a.value):a.value;if(a.options?.applyAtProcessCreation??!0)switch(a.type){case Ee.Append:e[o]=(e[o]||"")+l;break;case Ee.Prepend:e[o]=l+(e[o]||"");break;case Ee.Replace:e[o]=l;break}if(a.options?.applyAtShellIntegration??!1){const c=`VSCODE_ENV_${Rh.get(a.type)}`;e[c]=(e[c]?e[c]+":":"")+r+"="+this.c(l)}}}}c(e){return e.replaceAll(":","\\x3a")}diff(e,t){const s=new Map,i=new Map,r=new Map;if(e.getVariableMap(t).forEach((n,o)=>{const a=this.getVariableMap(t).get(o),l=po(n,a);l&&s.set(o,l)}),this.getVariableMap(t).forEach((n,o)=>{const a=e.getVariableMap(t).get(o),l=po(n,a);l&&r.set(o,l)}),this.getVariableMap(t).forEach((n,o)=>{const a=e.getVariableMap(t).get(o),l=Fh(n,a);l&&i.set(o,l)}),!(s.size===0&&i.size===0&&r.size===0))return{added:s,changed:i,removed:r}}getVariableMap(e){const t=new Map;for(const s of this.a.values()){const i=s.filter(r=>fo(r,e));i.length>0&&t.set(i[0].variable,i)}return t}getDescriptionMap(e){const t=new Map;for(const s of this.b.values()){const i=s.filter(r=>fo(r,e,!0));for(const r of i)t.set(r.extensionIdentifier,r.description)}return t}d(e,t){if(!e.descriptionMap)return;const s=e.descriptionMap.entries();let i=s.next();for(;!i.done;){const r=i.value[1],n=i.value[0];let o=this.b.get(n);o||(o=[],this.b.set(n,o));const a={extensionIdentifier:t,scope:r.scope,description:r.description};a.scope||delete a.scope,o.push(a),i=s.next()}}};function fo(e,t,s=!1){return e.scope?!!(e.scope.workspaceFolder&&t?.workspaceFolder&&e.scope.workspaceFolder.index===t.workspaceFolder.index):s?t===e.scope:!0}function po(e,t){if(!t)return e;const s=new Set;t.forEach(r=>s.add(r.extensionIdentifier));const i=[];return e.forEach(r=>{s.has(r.extensionIdentifier)||i.push(r)}),i.length===0?void 0:i}function Fh(e,t){if(!t)return;const s=new Map;t.forEach(r=>s.set(r.extensionIdentifier,r));const i=[];return e.forEach(r=>{const n=s.get(r.extensionIdentifier);n&&(r.type!==n.type||r.value!==n.value||r.scope?.workspaceFolder?.index!==n.scope?.workspaceFolder?.index)&&i.push(n)}),i.length===0?void 0:i}import{chmod as mo,realpathSync as Th,mkdirSync as jh}from"fs";import{promisify as go}from"util";function Ze(){const e=/(\d+)\.(\d+)\.(\d+)/g.exec(Ss.release());let t=0;return e&&e.length===4&&(t=parseInt(e[3])),t}async function Uh(e,t,s,i,r,n=!1){if(!t.shellIntegration.enabled)return{type:"failure",reason:"injectionSettingDisabled"};if(!e.executable)return{type:"failure",reason:"noExecutable"};if(e.isFeatureTerminal&&!e.forceShellIntegration)return{type:"failure",reason:"featureTerminal"};if(e.ignoreShellIntegration)return{type:"failure",reason:"ignoreShellIntegrationFlag"};if(N&&(!t.windowsEnableConpty||Ze()<18309))return{type:"failure",reason:"winpty"};const o=e.args,a=pr==="win32"?hs(e.executable).toLowerCase():hs(e.executable),l=Dt(Mt.asFileUri("").fsPath),c="injection";let u;const h={VSCODE_INJECTION:"1"};t.shellIntegration.nonce&&(h.VSCODE_NONCE=t.shellIntegration.nonce);const f=["PATH","VIRTUAL_ENV","HOME","SHELL","PWD"];if(e.shellIntegrationEnvironmentReporting&&(N?(t.windowsUseConptyDll||t.windowsEnableConpty&&Ze()>=22631&&a!=="bash.exe")&&(h.VSCODE_SHELL_ENV_REPORTING=f.join(",")):h.VSCODE_SHELL_ENV_REPORTING=f.join(",")),N)return a==="pwsh.exe"||a==="powershell.exe"?(!o||yo(o)?u=Y.get(J.WindowsPwsh):bo(o)&&(u=Y.get(J.WindowsPwshLogin)),u?(u=[...u],u[u.length-1]=lt(u[u.length-1],l,""),h.VSCODE_STABLE=r.quality==="stable"?"1":"0",t.shellIntegration.suggestEnabled&&(h.VSCODE_SUGGEST="1"),{type:c,newArgs:u,envMixin:h}):{type:"failure",reason:"unsupportedArgs"}):a==="bash.exe"?(!o||o.length===0?u=Y.get(J.Bash):Ls(o)&&(h.VSCODE_SHELL_LOGIN="1",Ps(t,h,a),u=Y.get(J.Bash)),u?(u=[...u],u[u.length-1]=lt(u[u.length-1],l),h.VSCODE_STABLE=r.quality==="stable"?"1":"0",{type:c,newArgs:u,envMixin:h}):{type:"failure",reason:"unsupportedArgs"}):(i.warn(`Shell integration cannot be enabled for executable "${e.executable}" and args`,e.args),{type:"failure",reason:"unsupportedShell"});switch(a){case"bash":return!o||o.length===0?u=Y.get(J.Bash):Ls(o)&&(h.VSCODE_SHELL_LOGIN="1",Ps(t,h,a),u=Y.get(J.Bash)),u?(u=[...u],u[u.length-1]=lt(u[u.length-1],l),h.VSCODE_STABLE=r.quality==="stable"?"1":"0",{type:c,newArgs:u,envMixin:h}):{type:"failure",reason:"unsupportedArgs"};case"fish":return!o||o.length===0?u=Y.get(J.Fish):Ls(o)?u=Y.get(J.FishLogin):(o===Y.get(J.Fish)||o===Y.get(J.FishLogin))&&(u=o),u?(Ps(t,h,a),u=[...u],u[u.length-1]=lt(u[u.length-1],l),{type:c,newArgs:u,envMixin:h}):{type:"failure",reason:"unsupportedArgs"};case"pwsh":return!o||yo(o)?u=Y.get(J.Pwsh):bo(o)&&(u=Y.get(J.PwshLogin)),u?(t.shellIntegration.suggestEnabled&&(h.VSCODE_SUGGEST="1"),u=[...u],u[u.length-1]=lt(u[u.length-1],l,""),h.VSCODE_STABLE=r.quality==="stable"?"1":"0",{type:c,newArgs:u,envMixin:h}):{type:"failure",reason:"unsupportedArgs"};case"zsh":{if(!o||o.length===0?u=Y.get(J.Zsh):Ls(o)?(u=Y.get(J.ZshLogin),Ps(t,h,a)):(o===Y.get(J.Zsh)||o===Y.get(J.ZshLogin))&&(u=o),!u)return{type:"failure",reason:"unsupportedArgs"};u=[...u],u[u.length-1]=lt(u[u.length-1],l);let d;try{d=Ss.userInfo().username}catch{d="unknown"}const p=Th(Ss.tmpdir()),v=O(p,`${d}-${r.applicationName}-zsh`);if(!n)try{await go(mo)(v,960)}catch($){if($.message.includes("ENOENT")){try{jh(v)}catch(R){return i.error(`Failed to create zdotdir at ${v}: ${R}`),{type:"failure",reason:"failedToCreateTmpDir"}}try{await go(mo)(v,960)}catch{return i.error(`Failed to set sticky bit on ${v}: ${$}`),{type:"failure",reason:"failedToSetStickyBit"}}}return i.error(`Failed to set sticky bit on ${v}: ${$}`),{type:"failure",reason:"failedToSetStickyBit"}}h.ZDOTDIR=v;const g=s?.ZDOTDIR??Ss.homedir()??"~";h.USER_ZDOTDIR=g;const b=[];return b.push({source:O(l,"out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh"),dest:O(v,".zshrc")}),b.push({source:O(l,"out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh"),dest:O(v,".zprofile")}),b.push({source:O(l,"out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh"),dest:O(v,".zshenv")}),b.push({source:O(l,"out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh"),dest:O(v,".zlogin")}),{type:c,newArgs:u,envMixin:h,filesToCopy:b}}}return i.warn(`Shell integration cannot be enabled for executable "${e.executable}" and args`,e.args),{type:"failure",reason:"unsupportedShell"}}function Ps(e,t,s){if((Fe||s==="fish")&&e.environmentVariableCollections){const i=_h(e.environmentVariableCollections),n=new Nh(i).getVariableMap({workspaceFolder:e.workspaceFolder}).get("PATH"),o=[];if(n)for(const a of n)a.type===Ee.Prepend&&o.push(a.value);o.length>0&&(t.VSCODE_PATH_PREFIX=o.join(""))}}var J;(function(e){e.WindowsPwsh="windows-pwsh",e.WindowsPwshLogin="windows-pwsh-login",e.Pwsh="pwsh",e.PwshLogin="pwsh-login",e.Zsh="zsh",e.ZshLogin="zsh-login",e.Bash="bash",e.Fish="fish",e.FishLogin="fish-login"})(J||(J={}));var Y=new Map;Y.set(J.WindowsPwsh,["-noexit","-command",'try { . "{0}\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1" } catch {}{1}']),Y.set(J.WindowsPwshLogin,["-l","-noexit","-command",'try { . "{0}\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1" } catch {}{1}']),Y.set(J.Pwsh,["-noexit","-command",'. "{0}/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"{1}']),Y.set(J.PwshLogin,["-l","-noexit","-command",'. "{0}/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"']),Y.set(J.Zsh,["-i"]),Y.set(J.ZshLogin,["-il"]),Y.set(J.Bash,["--init-file","{0}/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"]),Y.set(J.Fish,["--init-command",'source "{0}/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish"']),Y.set(J.FishLogin,["-l","--init-command",'source "{0}/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish"']);var As=["-login","-l"],vo=["--login","-l"],Bh=["-i","--interactive"],Ds=["-nol","-nologo"];function bo(e){return typeof e=="string"?As.includes(e.toLowerCase()):e.length===1&&As.includes(e[0].toLowerCase())||e.length===2&&(As.includes(e[0].toLowerCase())||As.includes(e[1].toLowerCase()))&&(Ds.includes(e[0].toLowerCase())||Ds.includes(e[1].toLowerCase()))}function yo(e){return typeof e=="string"?Ds.includes(e.toLowerCase()):e.length===0||e?.length===1&&Ds.includes(e[0].toLowerCase())}function Ls(e){return typeof e!="string"&&(e=e.filter(t=>!Bh.includes(t.toLowerCase()))),e==="string"&&vo.includes(e.toLowerCase())||typeof e!="string"&&e.length===1&&vo.includes(e[0].toLowerCase())}import*as zt from"fs";import{exec as zh}from"child_process";var Wh=Oe("productService");import{exec as $s}from"child_process";function Hh(e){return new Promise((t,s)=>{let i;const r=new Map;function n(l,c,u,h,f){const d=r.get(c);if(l===e||d){const p={name:o(u),cmd:u,pid:l,ppid:c,load:h,mem:f};r.set(l,p),l===e&&(i=p),d&&(d.children||(d.children=[]),d.children.push(p),d.children.length>1&&(d.children=d.children.sort((v,g)=>v.pid-g.pid)))}}function o(l){const c=/--utility-sub-type=network/i,u=/--crashes-directory/i,h=/\\pipe\\winpty-control/i,f=/conhost\.exe.+--headless/i,d=/--type=([a-zA-Z-]+)/;if(u.exec(l))return"electron-crash-reporter";if(h.exec(l))return"winpty-agent";if(f.exec(l))return"conpty-agent";let p=d.exec(l);if(p&&p.length===2)return p[1]==="renderer"?"window":p[1]==="utility"?c.exec(l)?"utility-network-service":"utility-process":p[1]==="extensionHost"?"extension-host":p[1];const v=/[a-zA-Z-]+\.js/g;let g="";do p=v.exec(l),p&&(g+=p+" ");while(p);return g&&l.indexOf("node ")<0&&l.indexOf("node.exe")<0?`electron-nodejs (${g})`:l}if(process.platform==="win32"){const l=c=>c.indexOf("\\\\?\\")===0||c.indexOf("\\??\\")===0?c.substring(4):c.indexOf('"\\\\?\\')===0||c.indexOf('"\\??\\')===0?'"'+c.substring(5):c;import("@vscode/windows-process-tree").then(c=>{c.getProcessList(e,u=>{if(!u){s(new Error(`Root process ${e} not found`));return}c.getProcessCpuUsage(u,h=>{const f=new Map;h.forEach(d=>{const p=l(d.commandLine||"");f.set(d.pid,{name:o(p),cmd:p,pid:d.pid,ppid:d.ppid,load:d.cpu||0,mem:d.memory||0})}),i=f.get(e),i?(f.forEach(d=>{const p=f.get(d.ppid);p&&(p.children||(p.children=[]),p.children.push(d))}),f.forEach(d=>{d.children&&(d.children=d.children.sort((p,v)=>p.pid-v.pid))}),t(i)):s(new Error(`Root process ${e} not found`))})},c.ProcessDataFlag.CommandLine|c.ProcessDataFlag.Memory)})}else{let l=function(){let c=[i];const u=[];for(;c.length;){const f=c.shift();f&&(u.push(f.pid),f.children&&(c=c.concat(f.children)))}let h=JSON.stringify(Mt.asFileUri("vs/base/node/cpuUsage.sh").fsPath);h+=" "+u.join(" "),$s(h,{},(f,d,p)=>{if(f||p)s(f||new Error(p.toString()));else{const v=d.toString().split(`
`);for(let g=0;g<u.length;g++){const b=r.get(u[g]);b.load=parseFloat(v[g])}if(!i){s(new Error(`Root process ${e} not found`));return}t(i)}})};var a=l;$s("which ps",{},(c,u,h)=>{if(c||h)if(process.platform!=="linux")s(c||new Error(h.toString()));else{const f=JSON.stringify(Mt.asFileUri("vs/base/node/ps.sh").fsPath);$s(f,{},(d,p,v)=>{d||v?s(d||new Error(v.toString())):(wo(p,n),l())})}else{const f=u.toString().trim();$s(`${f} -ax -o pid=,ppid=,pcpu=,pmem=,command=`,{maxBuffer:1e3*1024,env:{LC_NUMERIC:"en_US.UTF-8"}},(p,v,g)=>{p||g&&!g.includes("screen size is bogus")?s(p||new Error(g.toString())):(wo(v,n),process.platform==="linux"?l():i?t(i):s(new Error(`Root process ${e} not found`)))})}})}})}function wo(e,t){const s=/^\s*([0-9]+)\s+([0-9]+)\s+([0-9]+\.[0-9]+)\s+([0-9]+\.[0-9]+)\s+(.+)$/,i=e.toString().split(`
`);for(const r of i){const n=s.exec(r.trim());n&&n.length===6&&t(parseInt(n[1]),parseInt(n[2]),n[5],parseFloat(n[3]),parseFloat(n[4]))}}var Co;(function(e){e[e.InactiveThrottleDuration=5e3]="InactiveThrottleDuration",e[e.ActiveDebounceDuration=1e3]="ActiveDebounceDuration"})(Co||(Co={}));var Mi=[],Wt=class extends G{set hasChildProcesses(t){this.a!==t&&(this.a=t,this.f.debug("ChildProcessMonitor: Has child processes changed",t),this.b.fire(t))}get hasChildProcesses(){return this.a}constructor(t,s){super(),this.c=t,this.f=s,this.a=!1,this.b=this.B(new C),this.onDidChangeHasChildProcesses=this.b.event}handleInput(){this.g()}handleOutput(){this.h()}async g(){if(!this.q.isDisposed)try{const t=await Hh(this.c);this.hasChildProcesses=this.j(t)}catch(t){this.f.debug("ChildProcessMonitor: Fetching process tree failed",t)}}h(){this.g()}j(t){if(!t.children)return!1;if(t.children.length===1){const s=t.children[0];let i;if(s.cmd.startsWith('"'))i=s.cmd.substring(1,s.cmd.indexOf('"',1));else{const r=s.cmd.indexOf(" ");r===-1?i=s.cmd:i=s.cmd.substring(0,r)}return Mi.indexOf(N1(i).name)===-1}return t.children.length>0}};__decorate([li(1e3)],Wt.prototype,"g",null),__decorate([Vr(5e3)],Wt.prototype,"h",null),Wt=__decorate([__param(1,Ye)],Wt);var Vh=["cmd.exe","powershell.exe","pwsh.exe","bash.exe","git-cmd.exe","wsl.exe","ubuntu.exe","ubuntu1804.exe","kali.exe","debian.exe","opensuse-42.exe","sles-12.exe","julia.exe","nu.exe","node.exe"],qh=[/^python(\d(\.\d{0,2})?)?\.exe$/],Ii,xo=class extends G{get shellType(){return this.b}get shellTitle(){return this.c}get onShellNameChanged(){return this.f.event}get onShellTypeChanged(){return this.g.event}constructor(e){if(super(),this.h=e,this.c="",this.f=new C,this.g=new C,!N)throw new Error(`WindowsShellHelper cannot be instantiated on ${C1}`);this.j()}async j(){this.q.isDisposed||this.checkShell()}async checkShell(){N&&(await Ue(300),this.getShellName().then(e=>{const t=this.getShellType(e);t!==this.b&&(this.g.fire(t),this.f.fire(e),this.b=t,this.c=e)}))}m(e){if(!e)return"";if(Vh.indexOf(e.name)===-1)return e.name;for(const s of qh)if(e.name.match(s))return e.name;if(!e.children||e.children.length===0)return e.name;let t=0;for(;t<e.children.length;t++){const s=e.children[t];if(!s.children||s.children.length===0||s.children[0].name!=="conhost.exe")break}return t>=e.children.length?e.name:this.m(e.children[t])}async getShellName(){return this.q.isDisposed?Promise.resolve(""):this.a?this.a:(Ii||(Ii=await import("@vscode/windows-process-tree")),this.a=new Promise(e=>{Ii.getProcessTree(this.h,t=>{const s=this.m(t);this.a=void 0,e(s)})}),this.a)}getShellType(e){switch(e.toLowerCase()){case"cmd.exe":return"cmd";case"powershell.exe":case"pwsh.exe":return"pwsh";case"bash.exe":case"git-cmd.exe":return"gitbash";case"julia.exe":return"julia";case"node.exe":return"node";case"nu.exe":return"nu";case"wsl.exe":case"ubuntu.exe":case"ubuntu1804.exe":case"kali.exe":case"debian.exe":case"opensuse-42.exe":case"sles-12.exe":return"wsl";default:return e.match(/python(\d(\.\d{0,2})?)?\.exe/)?"python":void 0}}};__decorate([li(500)],xo.prototype,"checkShell",null);import{spawn as Gh}from"node-pty";var Eo;(function(e){e[e.WriteMaxChunkSize=50]="WriteMaxChunkSize"})(Eo||(Eo={}));function Xh(e){const t=[];let s=0;for(let i=0;i<e.length-1;i++)(i-s+1>=50||e[i+1]==="\x1B")&&(t.push(e.substring(s,i+1)),s=i+1,i++);return s!==e.length&&t.push(e.substring(s)),t}var Ht,ko;(function(e){e[e.DataFlushTimeout=250]="DataFlushTimeout",e[e.MaximumShutdownTime=5e3]="MaximumShutdownTime"})(ko||(ko={}));var So;(function(e){e[e.KillSpawnThrottleInterval=250]="KillSpawnThrottleInterval",e[e.KillSpawnSpacingDuration=50]="KillSpawnSpacingDuration",e[e.WriteInterval=5]="WriteInterval"})(So||(So={}));var Po=new Map([["bash","bash"],["csh","csh"],["fish","fish"],["ksh","ksh"],["sh","sh"],["zsh","zsh"]]),Ao=new Map([["pwsh","pwsh"],["powershell","pwsh"],["python","python"],["julia","julia"],["nu","nu"],["node","node"]]),Oi=class extends G{static{Ht=this}static{this.b=0}get exitMessage(){return this.h}get currentTitle(){return this.t?.shellTitle||this.n}get shellType(){return N?this.t?.shellType:Po.get(this.n)||Ao.get(this.n)}get hasChildProcesses(){return this.u?.hasChildProcesses||!1}constructor(t,s,i,r,n,o,a,l,c){super(),this.shellLaunchConfig=t,this.N=o,this.O=a,this.P=l,this.Q=c,this.id=0,this.shouldPersist=!1,this.a={cwd:"",initialCwd:"",fixedDimensions:{cols:void 0,rows:void 0},title:"",shellType:void 0,hasChildProcesses:!0,resolvedShellLaunchConfig:{},overrideDimensions:void 0,failedShellIntegrationActivation:!1,usedShellIntegrationInjection:void 0,shellIntegrationInjectionFailureReason:void 0},this.n="",this.w=null,this.y=[],this.G=!1,this.H=0,this.I=this.B(new C),this.onProcessData=this.I.event,this.J=this.B(new C),this.onProcessReady=this.J.event,this.L=this.B(new C),this.onDidChangeProperty=this.L.event,this.M=this.B(new C),this.onProcessExit=this.M.event;let u;N?u=hs(this.shellLaunchConfig.executable||""):u="xterm-256color",this.D=s,this.a.initialCwd=this.D,this.a.cwd=this.D;const h=this.O.windowsEnableConpty&&process.platform==="win32"&&Ze()>=18309,f=h&&this.O.windowsUseConptyDll;this.F={name:u,cwd:s,env:n,cols:i,rows:r,useConpty:h,useConptyDll:f,conptyInheritCursor:h&&!!t.initialText},N&&(h&&i===0&&r===0&&this.shellLaunchConfig.executable?.endsWith("Git\\bin\\bash.exe")&&(this.C=new Jh,this.B(this.C.onTrigger(d=>{this.C?.dispose(),this.C=void 0,d.cols&&d.rows&&this.resize(d.cols,d.rows)}))),this.onProcessReady(d=>{this.t=this.B(new xo(d.pid)),this.B(this.t.onShellTypeChanged(p=>this.L.fire({type:"shellType",value:p}))),this.B(this.t.onShellNameChanged(p=>this.L.fire({type:"title",value:p})))})),this.B(be(()=>{this.w&&(clearInterval(this.w),this.w=null)}))}async start(){const s=(await Promise.all([this.R(),this.S()])).find(r=>r!==void 0);if(s)return s;const i=await Uh(this.shellLaunchConfig,this.O,this.F.env,this.P,this.Q);if(i.type==="injection"){if(this.L.fire({type:"usedShellIntegrationInjection",value:!0}),i.envMixin)for(const[r,n]of Object.entries(i.envMixin))this.F.env||={},this.F.env[r]=n;if(i.filesToCopy)for(const r of i.filesToCopy)try{await zt.promises.mkdir(Dt(r.dest),{recursive:!0}),await zt.promises.copyFile(r.source,r.dest)}catch{}}else this.L.fire({type:"failedShellIntegrationActivation",value:!0}),this.L.fire({type:"shellIntegrationInjectionFailureReason",value:i.reason});try{const r=i.type==="injection"?i:void 0;return await this.U(this.shellLaunchConfig,this.F,r),r?.newArgs?{injectedArgs:r.newArgs}:void 0}catch(r){return this.P.trace("node-pty.node-pty.IPty#spawn native exception",r),{message:`A native exception occurred during launch (${r.message})`}}}async R(){try{if(!(await zt.promises.stat(this.D)).isDirectory())return{message:w(2286,null,this.D.toString())}}catch(t){if(t?.code==="ENOENT")return{message:w(2287,null,this.D.toString())}}this.L.fire({type:"initialCwd",value:this.D})}async S(){const t=this.shellLaunchConfig;if(!t.executable)throw new Error("IShellLaunchConfig.executable not set");const s=t.cwd instanceof j?t.cwd.path:t.cwd,i=t.env&&t.env.PATH?t.env.PATH.split(br):void 0,r=await hl(t.executable,s,i,this.N);if(!r)return{message:w(2288,null,t.executable)};try{const n=await zt.promises.stat(r);if(!n.isFile()&&!n.isSymbolicLink())return{message:w(2289,null,t.executable)};t.executable=r}catch(n){if(n?.code!=="EACCES")throw n}}async U(t,s,i){const r=i?.newArgs||t.args||[];await this.Z(),this.P.trace("node-pty.IPty#spawn",t.executable,r,s);const n=Gh(t.executable,r,s);this.m=n,this.u=this.B(new Wt(n.pid,this.P)),this.u.onDidChangeHasChildProcesses(o=>this.L.fire({type:"hasChildProcesses",value:o})),this.s=new Promise(o=>{this.onProcessReady(()=>o())}),n.onData(o=>{this.H+=o.length,!this.G&&this.H>1e5&&(this.P.trace(`Flow control: Pause (${this.H} > 100000)`),this.G=!0,n.pause()),this.P.trace("node-pty.IPty#onData",o),this.I.fire(o),this.j&&this.X(),this.t?.checkShell(),this.u?.handleOutput()}),n.onExit(o=>{this.g=o.exitCode,this.X()}),this.$(n.pid),this.W(n)}W(t){setTimeout(()=>this.ab(t)),N||(this.w=setInterval(()=>{this.n!==t.process&&this.ab(t)},200))}X(){this.P.getLevel()===A.Trace&&this.P.trace("TerminalProcess#_queueProcessExit",new Error().stack?.replace(/^Error/,"")),this.j&&clearTimeout(this.j),this.j=setTimeout(()=>{this.j=void 0,this.Y()},250)}async Y(){if(await this.s,!this.q.isDisposed){try{this.m&&(await this.Z(),this.P.trace("node-pty.IPty#kill"),this.m.kill())}catch{}this.M.fire(this.g||0),this.dispose()}}async Z(){if(!(!N||!("useConpty"in this.F)||!this.F.useConpty)&&!this.F.useConptyDll){for(;Date.now()-Ht.b<250;)this.P.trace("Throttling kill/spawn call"),await Ue(250-(Date.now()-Ht.b)+50);Ht.b=Date.now()}}$(t){this.J.fire({pid:t,cwd:this.D,windowsPty:this.getWindowsPty()})}ab(t){if(this.q.isDisposed)return;this.n=t.process??"",this.L.fire({type:"title",value:this.n});let s=this.currentTitle.replace(/ \(figterm\)$/g,"");if(N||(s=hs(s)),s.toLowerCase().startsWith("python"))this.L.fire({type:"shellType",value:"python"});else if(s.toLowerCase().startsWith("julia"))this.L.fire({type:"shellType",value:"julia"});else{const i=Po.get(s)||Ao.get(s);this.L.fire({type:"shellType",value:i})}}shutdown(t){this.P.getLevel()===A.Trace&&this.P.trace("TerminalProcess#shutdown",new Error().stack?.replace(/^Error/,"")),t&&!N?this.Y():!this.j&&!this.q.isDisposed&&(this.X(),setTimeout(()=>{this.j&&!this.q.isDisposed&&(this.j=void 0,this.Y())},5e3))}input(t,s=!1){this.q.isDisposed||!this.m||(this.y.push(...Xh(t).map(i=>({isBinary:s,data:i}))),this.bb())}async processBinary(t){this.input(t,!0)}async refreshProperty(t){switch(t){case"cwd":{const s=await this.getCwd();return s!==this.a.cwd&&(this.a.cwd=s,this.L.fire({type:"cwd",value:this.a.cwd})),s}case"initialCwd":{const s=await this.getInitialCwd();return s!==this.a.initialCwd&&(this.a.initialCwd=s,this.L.fire({type:"initialCwd",value:this.a.initialCwd})),s}case"title":return this.currentTitle;default:return this.shellType}}async updateProperty(t,s){t==="fixedDimensions"&&(this.a.fixedDimensions=s)}bb(){if(!(this.z!==void 0||this.y.length===0)){if(this.cb(),this.y.length===0){this.z=void 0;return}this.z=setTimeout(()=>{this.z=void 0,this.bb()},5)}}cb(){const t=this.y.shift();this.P.trace("node-pty.IPty#write",t.data),t.isBinary?this.m.write(Buffer.from(t.data,"binary")):this.m.write(t.data),this.u?.handleInput()}resize(t,s){if(!this.q.isDisposed&&!(typeof t!="number"||typeof s!="number"||isNaN(t)||isNaN(s))&&this.m){if(t=Math.max(t,1),s=Math.max(s,1),this.C){this.C.cols=t,this.C.rows=s;return}this.P.trace("node-pty.IPty#resize",t,s);try{this.m.resize(t,s)}catch(i){if(this.P.trace("node-pty.IPty#resize exception "+i.message),this.g!==void 0&&i.message!=="ioctl(2) failed, EBADF"&&i.message!=="Cannot resize a pty that has already exited")throw i}}}clearBuffer(){this.m?.clear()}acknowledgeDataEvent(t){this.H=Math.max(this.H-t,0),this.P.trace(`Flow control: Ack ${t} chars (unacknowledged: ${this.H})`),this.G&&this.H<5e3&&(this.P.trace(`Flow control: Resume (${this.H} < 5000)`),this.m?.resume(),this.G=!1)}clearUnacknowledgedChars(){this.H=0,this.P.trace("Flow control: Cleared all unacknowledged chars, forcing resume"),this.G&&(this.m?.resume(),this.G=!1)}async setUnicodeVersion(t){}getInitialCwd(){return Promise.resolve(this.D)}async getCwd(){if(Fe)return new Promise(t=>{if(!this.m){t(this.D);return}this.P.trace("node-pty.IPty#pid"),zh("lsof -OPln -p "+this.m.pid+" | grep cwd",{env:{...process.env,LANG:"en_US.UTF-8"}},(s,i,r)=>{!s&&i!==""?t(i.substring(i.indexOf("/"),i.length-1)):(this.P.error("lsof did not run successfully, it may not be on the $PATH?",s,i,r),t(this.D))})});if(St){if(!this.m)return this.D;this.P.trace("node-pty.IPty#pid");try{return await zt.promises.readlink(`/proc/${this.m.pid}/cwd`)}catch{return this.D}}return this.D}getWindowsPty(){return N?{backend:"useConpty"in this.F&&this.F.useConpty?"conpty":"winpty",buildNumber:Ze()}:void 0}};Oi=Ht=__decorate([__param(7,Ye),__param(8,Wh)],Oi);var Jh=class extends G{get onTrigger(){return this.b.event}constructor(){super(),this.b=this.B(new C),this.a=setTimeout(()=>{this.b.fire({rows:this.rows,cols:this.cols})},1e3),this.B(be(()=>clearTimeout(this.a)))}},Yh=class extends G{constructor(){super(...arguments),this.a=new Map,this.b=this.B(new C),this.onDidRemoveCapabilityType=this.b.event,this.f=this.B(new C),this.onDidAddCapabilityType=this.f.event,this.g=this.B(new C),this.onDidRemoveCapability=this.g.event,this.h=this.B(new C),this.onDidAddCapability=this.h.event}get items(){return this.a.keys()}add(e,t){this.a.set(e,t),this.f.fire(e),this.h.fire({id:e,capability:t})}get(e){return this.a.get(e)}remove(e){const t=this.a.get(e);t&&(this.a.delete(e),this.b.fire(e),this.h.fire({id:e,capability:t}))}has(e){return this.a.has(e)}},Do=class da{get command(){return this.b.command}get commandLineConfidence(){return this.b.commandLineConfidence}get isTrusted(){return this.b.isTrusted}get timestamp(){return this.b.timestamp}get duration(){return this.b.duration}get promptStartMarker(){return this.b.promptStartMarker}get marker(){return this.b.marker}get endMarker(){return this.b.endMarker}set endMarker(t){this.b.endMarker=t}get executedMarker(){return this.b.executedMarker}get aliases(){return this.b.aliases}get wasReplayed(){return this.b.wasReplayed}get cwd(){return this.b.cwd}get exitCode(){return this.b.exitCode}get commandStartLineContent(){return this.b.commandStartLineContent}get markProperties(){return this.b.markProperties}get executedX(){return this.b.executedX}get startX(){return this.b.startX}constructor(t,s){this.a=t,this.b=s}static deserialize(t,s,i){const r=t.buffer.normal,n=s.startLine!==void 0?t.registerMarker(s.startLine-(r.baseY+r.cursorY)):void 0;if(!n)return;const o=s.promptStartLine!==void 0?t.registerMarker(s.promptStartLine-(r.baseY+r.cursorY)):void 0,a=s.endLine!==void 0?t.registerMarker(s.endLine-(r.baseY+r.cursorY)):void 0,l=s.executedLine!==void 0?t.registerMarker(s.executedLine-(r.baseY+r.cursorY)):void 0;return new da(t,{command:i?"":s.command,commandLineConfidence:s.commandLineConfidence??"low",isTrusted:s.isTrusted,promptStartMarker:o,marker:n,startX:s.startX,endMarker:a,executedMarker:l,executedX:s.executedX,timestamp:s.timestamp,duration:s.duration,cwd:s.cwd,commandStartLineContent:s.commandStartLineContent,exitCode:s.exitCode,markProperties:s.markProperties,aliases:void 0,wasReplayed:!0})}serialize(t){return{promptStartLine:this.promptStartMarker?.line,startLine:this.marker?.line,startX:void 0,endLine:this.endMarker?.line,executedLine:this.executedMarker?.line,executedX:this.executedX,command:t?"":this.command,commandLineConfidence:t?"low":this.commandLineConfidence,isTrusted:this.isTrusted,cwd:this.cwd,exitCode:this.exitCode,commandStartLineContent:this.commandStartLineContent,timestamp:this.timestamp,duration:this.duration,markProperties:this.markProperties}}extractCommandLine(){return $o(this.a.buffer.active,this.a.cols,this.marker,this.startX,this.executedMarker,this.executedX)}getOutput(){if(!this.executedMarker||!this.endMarker)return;const t=this.executedMarker.line,s=this.endMarker.line;if(t===s)return;let i="",r;for(let n=t;n<s;n++)r=this.a.buffer.active.getLine(n),r&&(i+=r.translateToString(!r.isWrapped)+(r.isWrapped?"":`
`));return i===""?void 0:i}getOutputMatch(t){if(!this.executedMarker||!this.endMarker)return;const s=this.endMarker.line;if(s===-1)return;const i=this.a.buffer.active,r=Math.max(this.executedMarker.line,0),n=t.lineMatcher,o=typeof n=="string"?1:t.length||Qh(n),a=[];let l;if(t.anchor==="bottom")for(let c=s-(t.offset||0);c>=r;c--){let u=c;const h=c;for(;u>=r&&i.getLine(u)?.isWrapped;)u--;if(c=u,a.unshift(Mo(i,u,h,this.a.cols)),l||(l=a[0].match(n)),a.length>=o)break}else for(let c=r+(t.offset||0);c<s;c++){const u=c;let h=c;for(;h+1<s&&i.getLine(h+1)?.isWrapped;)h++;if(c=h,a.push(Mo(i,u,h,this.a.cols)),l||(l=a[a.length-1].match(n)),a.length>=o)break}return l?{regexMatch:l,outputLines:a}:void 0}hasOutput(){return!this.executedMarker?.isDisposed&&!this.endMarker?.isDisposed&&!!(this.executedMarker&&this.endMarker&&this.executedMarker.line<this.endMarker.line)}getPromptRowCount(){return Io(this,this.a.buffer.active)}getCommandRowCount(){return Oo(this)}},Lo=class{constructor(e){this.c=e}serialize(e){if(this.commandStartMarker)return{promptStartLine:this.promptStartMarker?.line,startLine:this.commandStartMarker.line,startX:this.commandStartX,endLine:void 0,executedLine:void 0,executedX:void 0,command:"",commandLineConfidence:"low",isTrusted:!0,cwd:e,exitCode:void 0,commandStartLineContent:void 0,timestamp:0,duration:0,markProperties:void 0}}promoteToFullCommand(e,t,s,i){if(t===void 0&&this.command===void 0&&(this.command=""),this.command!==void 0&&!this.command.startsWith("\\")||s)return new Do(this.c,{command:s?"":this.command||"",commandLineConfidence:s?"low":this.commandLineConfidence||"low",isTrusted:!!this.isTrusted,promptStartMarker:this.promptStartMarker,marker:this.commandStartMarker,startX:this.commandStartX,endMarker:this.commandFinishedMarker,executedMarker:this.commandExecutedMarker,executedX:this.commandExecutedX,timestamp:Date.now(),duration:this.b||0,cwd:e,exitCode:t,commandStartLineContent:this.commandStartLineContent,markProperties:i})}markExecutedTime(){this.a===void 0&&(this.a=Date.now())}markFinishedTime(){this.b===void 0&&this.a!==void 0&&(this.b=Date.now()-this.a)}extractCommandLine(){return $o(this.c.buffer.active,this.c.cols,this.commandStartMarker,this.commandStartX,this.commandExecutedMarker,this.commandExecutedX)}getPromptRowCount(){return Io(this,this.c.buffer.active)}getCommandRowCount(){return Oo(this)}};function $o(e,t,s,i,r,n){if(!s||!r||i===void 0||n===void 0)return"";let o="";for(let a=s.line;a<=r.line;a++){const l=e.getLine(a);l&&(o+=l.translateToString(!0,a===s.line?i:0,a===r.line?n:t))}return o}function Mo(e,t,s,i){const r=Math.max(2048/i*2);s=Math.min(s,t+r);let n="";for(let o=t;o<=s;o++){const a=e.getLine(o);a&&(n+=a.translateToString(!0,0,i))}return n}function Qh(e){if(!e.multiline)return 1;const t=e.source;let s=1,i=t.indexOf("\\n");for(;i!==-1;)s++,i=t.indexOf("\\n",i+1);return s}function Io(e,t){const s="hasOutput"in e?e.marker:e.commandStartMarker;if(!s||!e.promptStartMarker)return 1;let i=1,r=e.promptStartMarker.line;for(;r<s.line&&(t.getLine(r)?.translateToString(!0)??"").length===0;)r++;return i=s.line-r+1,i}function Oo(e){const t="hasOutput"in e?e.marker:e.commandStartMarker,s="hasOutput"in e?e.executedMarker:e.commandExecutedMarker;if(!t||!s)return 1;let r=Math.max(s.line,t.line)-t.line+1;return("hasOutput"in e?e.executedX:e.commandExecutedX)===0&&r--,r}var _o;(function(e){e[e.Unknown=0]="Unknown",e[e.Input=1]="Input",e[e.Execute=2]="Execute"})(_o||(_o={}));var Ms=class extends G{get value(){return this.r}get prefix(){return this.r.substring(0,this.s)}get suffix(){return this.r.substring(this.s,this.t===-1?void 0:this.t)}get cursorIndex(){return this.s}get ghostTextIndex(){return this.t}constructor(t,s,i,r,n){super(),this.D=t,this.F=n,this.c=0,this.g=0,this.n="",this.r="",this.s=0,this.t=-1,this.u=this.B(new C),this.onDidStartInput=this.u.event,this.w=this.B(new C),this.onDidChangeInput=this.w.event,this.z=this.B(new C),this.onDidFinishInput=this.z.event,this.C=this.B(new C),this.onDidInterrupt=this.C.event,this.B(K.any(this.D.onCursorMove,this.D.onData,this.D.onWriteParsed)(()=>this.L())),this.B(this.D.onData(o=>this.N(o))),this.B(s(o=>this.H(o))),this.B(i(()=>this.I())),this.B(r(()=>this.J())),this.B(this.onDidStartInput(()=>this.G("PromptInputModel#onDidStartInput"))),this.B(this.onDidChangeInput(()=>this.G("PromptInputModel#onDidChangeInput"))),this.B(this.onDidFinishInput(()=>this.G("PromptInputModel#onDidFinishInput"))),this.B(this.onDidInterrupt(()=>this.G("PromptInputModel#onDidInterrupt")))}G(t){this.F.getLevel()===A.Trace&&this.F.trace(t,this.getCombinedString())}setShellType(t){this.m=t}setContinuationPrompt(t){this.j=t,this.L()}setLastPromptLine(t){this.h=t,this.L()}setConfidentCommandLine(t){this.r!==t&&(this.r=t,this.s=-1,this.t=-1,this.w.fire(this.Z()))}getCombinedString(t){const s=this.r.replaceAll(`
`,"\u23CE");if(this.s===-1)return s;let i=`${s.substring(0,this.cursorIndex)}|`;return this.ghostTextIndex!==-1?(i+=`${s.substring(this.cursorIndex,this.ghostTextIndex)}[`,i+=`${s.substring(this.ghostTextIndex)}]`):i+=s.substring(this.cursorIndex),i==="|"&&t?"":i}serialize(){return{modelState:this.Z(),commandStartX:this.g,lastPromptLine:this.h,continuationPrompt:this.j,lastUserInput:this.n}}deserialize(t){this.r=t.modelState.value,this.s=t.modelState.cursorIndex,this.t=t.modelState.ghostTextIndex,this.g=t.commandStartX,this.h=t.lastPromptLine,this.j=t.continuationPrompt,this.n=t.lastUserInput}H(t){this.c!==1&&(this.c=1,this.f=t.marker,this.g=this.D.buffer.active.cursorX,this.r="",this.s=0,this.u.fire(this.Z()),this.w.fire(this.Z()),this.h&&this.g!==this.h.length&&this.D.buffer.active.getLine(this.f.line)?.translateToString(!0).startsWith(this.h)&&(this.g=this.h.length,this.L()))}I(){this.c===1&&(this.g=this.D.buffer.active.cursorX,this.w.fire(this.Z()),this.L())}J(){if(this.c===2)return;this.s=-1,this.t!==-1&&(this.r=this.r.substring(0,this.t),this.t=-1);const t=this.Z();this.n===""&&(this.n="",this.C.fire(t)),this.c=2,this.z.fire(t),this.w.fire(t)}L(){try{this.M()}catch(t){this.F.error("Error while syncing prompt input model",t)}}M(){if(this.c!==1)return;let t=this.f?.line;if(t===void 0)return;const s=this.D.buffer.active;let i=s.getLine(t);const r=s.baseY+s.cursorY;let n,o=i?.translateToString(!0,this.g);if(this.m==="fish"&&(!i||!o)&&(t+=1,i=s.getLine(t),i&&(o=i.translateToString(!0),n=r===t?s.cursorX:o?.trimEnd().length)),i===void 0||o===void 0){this.F.trace("PromptInputModel#_sync: no line");return}let a=o,l=-1;n===void 0&&(r===t?n=this.X(this.g,s,i):n=o.trimEnd().length);for(let c=t+1;c<=r;c++){const u=s.getLine(c),h=u?.translateToString(!0);if(h&&u){if(u.isWrapped||r===c&&this.j&&!this.U(h)){a+=`${h}`;const f=this.X(0,s,u);r===c?n+=f:n+=h.length}else if(this.m==="fish")a.endsWith("\\")?(a=a.substring(0,a.length-1),a+=`${h.trim()}`,n+=h.trim().length-1):/^ {6,}/.test(h)?(a+=`
${h.trim()}`,n+=h.trim().length+1):(a+=h,n+=h.length);else if(this.j===void 0||this.U(h)){const f=this.S(h);if(a+=`
${f}`,r===c){const d=this.W(u,h),p=this.X(d,s,u);n+=p+1}else n+=f.length+1}}}for(let c=r+1;c<s.baseY+this.D.rows;c++){const u=s.getLine(c),h=u?.translateToString(!0);if(h&&u)this.m==="fish"?a+=`${h}`:this.j===void 0||this.U(h)?a+=`
${this.S(h)}`:a+=h;else break}this.F.getLevel()===A.Trace&&this.F.trace(`PromptInputModel#_sync: ${this.getCombinedString()}`);{let c=this.r.length-this.r.trimEnd().length;this.n==="\x7F"&&(this.n="",n===this.s-1&&(this.r.trimEnd().length>a.trimEnd().length&&a.trimEnd().length<=n?c=Math.max(this.r.length-1-a.trimEnd().length,0):c=Math.max(c-1,0))),this.n==="\x1B[3~"&&(this.n="",n===this.s&&(c=Math.max(c-1,0)));const u=a.split(`
`),h=u.length>1,f=a.trimEnd();if(!h){f.length<a.length&&(this.n===" "&&(this.n="",n>f.length&&n>this.s&&c++),c=Math.max(n-f.length,c,0));const d=n===0?"":a[n-1];c>0&&n===this.s+1&&this.n!==""&&d!==" "&&(c=this.r.length-this.s)}if(h){u[u.length-1]=u.at(-1)?.trimEnd()??"";const d=(u.length-1)*(this.j?.length??0);c=Math.max(0,n-a.length-d)}a=u.map(d=>d.trimEnd()).join(`
`)+" ".repeat(c)}l=this.O(s,i,n),(this.r!==a||this.s!==n||this.t!==l)&&(this.r=a,this.s=n,this.t=l,this.w.fire(this.Z()))}N(t){this.n=t}O(t,s,i){if(!this.value.trim().length)return-1;let r=-1,n=!1,o=t.cursorX;for(;o>0;){const a=s.getCell(--o);if(!a)break;if(a.getChars().trim().length>0){n=!this.Y(a);break}}if(n){let a=0,l=t.cursorX;for(;l<s.length;){const c=s.getCell(l++);if(!c||c.getCode()===0)break;if(this.Y(c)){r=i+a;break}a+=c.getChars().length}}return r===-1&&(r=this.P(t,s,i)),r>-1&&this.value.substring(r).endsWith(" ")&&(this.r=this.value.trim(),this.value.substring(r)||(r=-1)),r}P(t,s,i){let r=-1,n=t.cursorX;const o=new Map;let a=s.getCell(n),l=a;for(;l&&n<s.length;){const u=this.Q(l);o.set(u,[...o.get(u)??[],n]),l=s.getCell(++n),l?.getChars().trim().length&&(a=l)}if(!a?.getChars().trim().length||this.R(s.getCell(this.g),a))return-1;const c=o.get(this.Q(a));if(c){for(let u=1;u<c.length;u++)if(c[u]!==c[u-1]+1)return-1;t.baseY+t.cursorY===this.f?.line?r=c[0]-this.g:r=c[0]}if(r!==-1)for(let u=t.cursorX;u>=this.g;u--){const h=s.getCell(u);if(h?.getChars.length&&h&&h.getCode()!==0&&this.R(a,h))return-1}return r>=i?r:-1}Q(t){return`${t.getFgColor()}${t.getBgColor()}${t.isBold()}${t.isItalic()}${t.isDim()}${t.isUnderline()}${t.isBlink()}${t.isInverse()}${t.isInvisible()}${t.isStrikethrough()}${t.isOverline()}${t.getFgColorMode()}${t.getBgColorMode()}`}R(t,s){return!t||!s?!1:t.getFgColor()===s.getFgColor()&&t.getBgColor()===s.getBgColor()&&t.isBold()===s.isBold()&&t.isItalic()===s.isItalic()&&t.isDim()===s.isDim()&&t.isUnderline()===s.isUnderline()&&t.isBlink()===s.isBlink()&&t.isInverse()===s.isInverse()&&t.isInvisible()===s.isInvisible()&&t.isStrikethrough()===s.isStrikethrough()&&t.isOverline()===s.isOverline()&&t?.getBgColorMode()===s?.getBgColorMode()&&t?.getFgColorMode()===s?.getFgColorMode()}S(t){return this.U(t)&&(t=t.substring(this.j.length)),t}U(t){return!!(this.j&&t.startsWith(this.j.trimEnd()))}W(t,s){if(!this.j||!s.startsWith(this.j.trimEnd()))return 0;let i="",r=0,n;for(;i!==this.j&&(n=t.getCell(r++),!!n);)i+=n.getChars();return r}X(t,s,i){return i?.translateToString(!0,t,s.cursorX).length??0}Y(t){return!!(t.isItalic()||t.isDim())}Z(){return Object.freeze({value:this.r,prefix:this.prefix,suffix:this.suffix,cursorIndex:this.s,ghostTextIndex:this.t})}};__decorate([Vr(0)],Ms.prototype,"L",null),Ms=__decorate([__param(4,Ye)],Ms);var Is=class extends G{get promptInputModel(){return this.c}get hasRichCommandDetection(){return this.w}get promptType(){return this.z}get commands(){return this.f}get executingCommand(){return this.n.command}get executingCommandObject(){if(this.n.commandStartMarker)return this.n.promoteToFullCommand(this.g,void 0,this.u?.ignoreCommandLine??!1,void 0)}get executingCommandConfidence(){const t=this.n;return"commandLineConfidence"in t?t.commandLineConfidence:void 0}get currentCommand(){return this.n}get cwd(){return this.g}get promptTerminator(){return this.h}constructor(t,s){super(),this.P=t,this.Q=s,this.type=2,this.f=[],this.r=[],this.t=!1,this.w=!1,this.F=this.B(new C),this.onCommandStarted=this.F.event,this.G=this.B(new C),this.onCommandStartChanged=this.G.event,this.H=this.B(new C),this.onBeforeCommandFinished=this.H.event,this.I=this.B(new C),this.onCommandFinished=this.I.event,this.J=this.B(new C),this.onCommandExecuted=this.J.event,this.L=this.B(new C),this.onCommandInvalidated=this.L.event,this.M=this.B(new C),this.onCurrentCommandInvalidated=this.M.event,this.N=this.B(new C),this.onPromptTypeChanged=this.N.event,this.O=this.B(new C),this.onSetRichCommandDetection=this.O.event,this.n=new Lo(this.P),this.c=this.B(new Ms(this.P,this.onCommandStarted,this.onCommandStartChanged,this.onCommandExecuted,this.Q)),this.B(this.onCommandExecuted(r=>{if(r.commandLineConfidence!=="high"){const n=r;r.command=n.extractCommandLine(),r.commandLineConfidence="low","getOutput"in n?n.promptStartMarker&&n.marker&&n.executedMarker&&r.command.indexOf(`
`)===-1&&n.startX!==void 0&&n.startX>0&&(r.commandLineConfidence="medium"):n.promptStartMarker&&n.commandStartMarker&&n.commandExecutedMarker&&r.command.indexOf(`
`)===-1&&n.commandStartX!==void 0&&n.commandStartX>0&&(r.commandLineConfidence="medium")}}));const i=this;this.C=new class{get onCurrentCommandInvalidatedEmitter(){return i.M}get onCommandStartedEmitter(){return i.F}get onCommandExecutedEmitter(){return i.J}get dimensions(){return i.s}get isCommandStorageDisabled(){return i.t}get commandMarkers(){return i.r}set commandMarkers(r){i.r=r}get clearCommandsInViewport(){return i.U.bind(i)}},this.D=this.B(new Za(new _i(this.P,this,this.C,this.Q))),this.s={cols:this.P.cols,rows:this.P.rows},this.B(this.P.onResize(r=>this.R(r))),this.B(this.P.onCursorMove(()=>this.S()))}R(t){this.D.value.preHandleResize?.(t),this.s.cols=t.cols,this.s.rows=t.rows}S(){this.q.isDisposed||this.P.buffer.active===this.P.buffer.normal&&this.n.commandStartMarker&&this.P.buffer.active.baseY+this.P.buffer.active.cursorY<this.n.commandStartMarker.line&&(this.U(),this.n.isInvalid=!0,this.M.fire({reason:"windows"}))}U(){let t=0;for(let s=this.f.length-1;s>=0;s--){const i=this.f[s].marker?.line;if(i&&i<this.P.buffer.active.baseY)break;t++}t>0&&this.L.fire(this.f.splice(this.f.length-t,t))}setContinuationPrompt(t){this.c.setContinuationPrompt(t)}setPromptTerminator(t,s){this.Q.debug("CommandDetectionCapability#setPromptTerminator",t),this.h=t,this.c.setLastPromptLine(s)}setCwd(t){this.g=t}setIsWindowsPty(t){if(t&&!(this.D.value instanceof Vt)){const s=this;this.D.value=new Vt(this.P,this,new class{get onCurrentCommandInvalidatedEmitter(){return s.M}get onCommandStartedEmitter(){return s.F}get onCommandExecutedEmitter(){return s.J}get dimensions(){return s.s}get isCommandStorageDisabled(){return s.t}get commandMarkers(){return s.r}set commandMarkers(i){s.r=i}get clearCommandsInViewport(){return s.U.bind(s)}},this.Q)}else!t&&!(this.D.value instanceof _i)&&(this.D.value=new _i(this.P,this,this.C,this.Q))}setHasRichCommandDetection(t){this.w=t,this.O.fire(t)}setPromptType(t){this.z=t,this.N.fire(t)}setIsCommandStorageDisabled(){this.t=!0}getCommandForLine(t){if(this.n.promptStartMarker&&t>=this.n.promptStartMarker?.line)return this.n;if(this.f.length!==0&&!((this.f[0].promptStartMarker??this.f[0].marker).line>t)){for(let s=this.commands.length-1;s>=0;s--)if((this.commands[s].promptStartMarker??this.commands[s].marker).line<=t)return this.commands[s]}}getCwdForLine(t){if(this.n.promptStartMarker&&t>=this.n.promptStartMarker?.line)return this.g;const s=this.getCommandForLine(t);if(s&&"cwd"in s)return s.cwd}handlePromptStart(t){const s=this.commands.at(-1);s?.endMarker&&s?.executedMarker&&s.endMarker.line===s.executedMarker.line&&(this.Q.debug("CommandDetectionCapability#handlePromptStart adjusted commandFinished",`${s.endMarker.line} -> ${s.executedMarker.line+1}`),s.endMarker=qt(this.P,s.executedMarker,1)),this.n.promptStartMarker=t?.marker||(s?.endMarker?qt(this.P,s.endMarker):this.P.registerMarker(0)),this.Q.debug("CommandDetectionCapability#handlePromptStart",this.P.buffer.active.cursorX,this.n.promptStartMarker?.line)}handleContinuationStart(){this.n.currentContinuationMarker=this.P.registerMarker(0),this.Q.debug("CommandDetectionCapability#handleContinuationStart",this.n.currentContinuationMarker)}handleContinuationEnd(){if(!this.n.currentContinuationMarker){this.Q.warn("CommandDetectionCapability#handleContinuationEnd Received continuation end without start");return}this.n.continuations||(this.n.continuations=[]),this.n.continuations.push({marker:this.n.currentContinuationMarker,end:this.P.buffer.active.cursorX}),this.n.currentContinuationMarker=void 0,this.Q.debug("CommandDetectionCapability#handleContinuationEnd",this.n.continuations[this.n.continuations.length-1])}handleRightPromptStart(){this.n.commandRightPromptStartX=this.P.buffer.active.cursorX,this.Q.debug("CommandDetectionCapability#handleRightPromptStart",this.n.commandRightPromptStartX)}handleRightPromptEnd(){this.n.commandRightPromptEndX=this.P.buffer.active.cursorX,this.Q.debug("CommandDetectionCapability#handleRightPromptEnd",this.n.commandRightPromptEndX)}handleCommandStart(t){if(this.u=t,this.n.cwd=this.g,this.n.commandStartMarker=t?.marker||this.n.commandStartMarker,this.n.commandStartMarker?.line===this.P.buffer.active.cursorY){this.n.commandStartX=this.P.buffer.active.cursorX,this.G.fire(),this.Q.debug("CommandDetectionCapability#handleCommandStart",this.n.commandStartX,this.n.commandStartMarker?.line);return}this.D.value.handleCommandStart(t)}handleCommandExecuted(t){this.D.value.handleCommandExecuted(t),this.n.markExecutedTime()}handleCommandFinished(t,s){if(this.n.commandExecutedMarker||this.handleCommandExecuted(),this.n.markFinishedTime(),this.D.value.preHandleCommandFinished?.(),this.Q.debug("CommandDetectionCapability#handleCommandFinished",this.P.buffer.active.cursorX,s?.marker?.line,this.n.command,this.n),t===void 0){const r=this.commands.length>0?this.commands[this.commands.length-1]:void 0;this.n.command&&this.n.command.length>0&&r?.command===this.n.command&&(t=r.exitCode)}if(this.n.commandStartMarker===void 0||!this.P.buffer.active)return;this.n.commandFinishedMarker=s?.marker||this.P.registerMarker(0),this.D.value.postHandleCommandFinished?.();const i=this.n.promoteToFullCommand(this.g,t,this.u?.ignoreCommandLine??!1,s?.markProperties);i&&(this.f.push(i),this.H.fire(i),this.n.isInvalid||(this.Q.debug("CommandDetectionCapability#onCommandFinished",i),this.I.fire(i))),this.n=new Lo(this.P),this.u=void 0}setCommandLine(t,s){this.Q.debug("CommandDetectionCapability#setCommandLine",t,s),this.n.command=t,this.n.commandLineConfidence="high",this.n.isTrusted=s,s&&this.c.setConfidentCommandLine(t)}serialize(){const t=this.commands.map(i=>i.serialize(this.t)),s=this.n.serialize(this.g);return s&&t.push(s),{isWindowsPty:this.D.value instanceof Vt,hasRichCommandDetection:this.w,commands:t,promptInputModel:this.c.serialize()}}deserialize(t){t.isWindowsPty&&this.setIsWindowsPty(t.isWindowsPty),t.hasRichCommandDetection&&this.setHasRichCommandDetection(t.hasRichCommandDetection);const s=this.P.buffer.normal;for(const i of t.commands){if(!i.endLine){const n=i.startLine!==void 0?this.P.registerMarker(i.startLine-(s.baseY+s.cursorY)):void 0;if(!n)continue;this.n.commandStartMarker=i.startLine!==void 0?this.P.registerMarker(i.startLine-(s.baseY+s.cursorY)):void 0,this.n.commandStartX=i.startX,this.n.promptStartMarker=i.promptStartLine!==void 0?this.P.registerMarker(i.promptStartLine-(s.baseY+s.cursorY)):void 0,this.g=i.cwd,this.F.fire({marker:n});continue}const r=Do.deserialize(this.P,i,this.t);r&&(this.f.push(r),this.Q.debug("CommandDetectionCapability#onCommandFinished",r),this.I.fire(r))}t.promptInputModel&&this.c.deserialize(t.promptInputModel)}};__decorate([li(500)],Is.prototype,"S",null),Is=__decorate([__param(1,Ye)],Is);var _i=class extends G{constructor(e,t,s,i){super(),this.c=e,this.f=t,this.g=s,this.h=i,this.B(e.parser.registerCsiHandler({final:"J"},r=>(r.length>=1&&(r[0]===2||r[0]===3)&&s.clearCommandsInViewport(),!1)))}handleCommandStart(e){const t=this.f.currentCommand;t.commandStartX=this.c.buffer.active.cursorX,t.commandStartMarker=e?.marker||this.c.registerMarker(0),t.commandExecutedMarker?.dispose(),t.commandExecutedMarker=void 0,t.commandExecutedX=void 0;for(const s of this.g.commandMarkers)s.dispose();this.g.commandMarkers.length=0,this.g.onCommandStartedEmitter.fire({marker:e?.marker||t.commandStartMarker,markProperties:e?.markProperties}),this.h.debug("CommandDetectionCapability#handleCommandStart",t.commandStartX,t.commandStartMarker?.line)}handleCommandExecuted(e){const t=this.f.currentCommand;t.commandExecutedMarker=e?.marker||this.c.registerMarker(0),t.commandExecutedX=this.c.buffer.active.cursorX,this.h.debug("CommandDetectionCapability#handleCommandExecuted",t.commandExecutedX,t.commandExecutedMarker?.line),!(!t.commandStartMarker||!t.commandExecutedMarker||t.commandStartX===void 0)&&(t.command=this.f.promptInputModel.ghostTextIndex>-1?this.f.promptInputModel.value.substring(0,this.f.promptInputModel.ghostTextIndex):this.f.promptInputModel.value,this.g.onCommandExecutedEmitter.fire(t))}},Ro;(function(e){e[e.MaxCheckLineCount=10]="MaxCheckLineCount",e[e.Interval=20]="Interval",e[e.MaximumPollCount=10]="MaximumPollCount"})(Ro||(Ro={}));var Vt=class extends G{constructor(t,s,i,r){super(),this.n=t,this.r=s,this.s=i,this.t=r,this.c=this.B(new sr),this.g=0,this.h=0,this.B(t.parser.registerCsiHandler({final:"J"},n=>(n.length>=1&&(n[0]===2||n[0]===3)&&this.s.clearCommandsInViewport(),!1))),this.B(this.r.onBeforeCommandFinished(n=>{(n.command.trim().toLowerCase()==="clear"||n.command.trim().toLowerCase()==="cls")&&(this.f?.cancel(),this.f=void 0,this.s.clearCommandsInViewport(),this.r.currentCommand.isInvalid=!0,this.s.onCurrentCommandInvalidatedEmitter.fire({reason:"windows"}))}))}preHandleResize(t){const s=this.n.buffer.active.baseY,i=t.rows-this.s.dimensions.rows;i>0&&this.D().then(()=>{const r=Math.min(i,s);for(let n=this.r.commands.length-1;n>=0;n--){const o=this.r.commands[n];if(!o.marker||o.marker.line<s||o.commandStartLineContent===void 0)break;const a=this.n.buffer.active.getLine(o.marker.line);if(!a||a.translateToString(!0)===o.commandStartLineContent)continue;const l=o.marker.line-r;this.n.buffer.active.getLine(l)?.translateToString(!0)===o.commandStartLineContent&&this.n._core._bufferService.buffer.lines.onDeleteEmitter.fire({index:this.n.buffer.active.baseY,amount:r})}})}handleCommandStart(){this.r.currentCommand.commandStartX=this.n.buffer.active.cursorX,this.s.commandMarkers.length=0;const t=this.r.currentCommand.commandStartMarker=this.r.currentCommand.promptStartMarker?qt(this.n,this.r.currentCommand.promptStartMarker):this.n.registerMarker(0);this.r.currentCommand.commandStartX=0,this.g=0,this.h=0,this.f=new jc(()=>this.u(t),20),this.f.schedule()}u(t){if(this.q.isDisposed)return;const s=this.n.buffer.active;let i=this.g;for(;i<10&&t.line+i<s.baseY+this.n.rows;){if(this.C()){const r=this.F(t.line+i);if(r){const n=typeof r=="string"?r:r.prompt;if(this.r.currentCommand.commandStartMarker=this.n.registerMarker(0),typeof r=="object"&&r.likelySingleLine){this.t.debug("CommandDetectionCapability#_tryAdjustCommandStartMarker adjusted promptStart",`${this.r.currentCommand.promptStartMarker?.line} -> ${this.r.currentCommand.commandStartMarker.line}`),this.r.currentCommand.promptStartMarker?.dispose(),this.r.currentCommand.promptStartMarker=qt(this.n,this.r.currentCommand.commandStartMarker);const o=this.r.commands.at(-1);o&&this.r.currentCommand.commandStartMarker.line!==o.endMarker?.line&&(o.endMarker?.dispose(),o.endMarker=qt(this.n,this.r.currentCommand.commandStartMarker))}this.r.currentCommand.commandStartX=n.length,this.t.debug("CommandDetectionCapability#_tryAdjustCommandStartMarker adjusted commandStart",`${t.line} -> ${this.r.currentCommand.commandStartMarker.line}:${this.r.currentCommand.commandStartX}`),this.w();return}}i++}i<10?(this.g=i,++this.h<10?this.f?.schedule():this.w()):this.w()}w(){if(this.f&&(this.h=10,this.f.flush(),this.f=void 0),this.r.currentCommand.commandExecutedMarker||(this.c.value=this.n.onCursorMove(()=>{if(this.s.commandMarkers.length===0||this.s.commandMarkers[this.s.commandMarkers.length-1].line!==this.n.buffer.active.cursorY){const t=this.n.registerMarker(0);t&&this.s.commandMarkers.push(t)}})),this.r.currentCommand.commandStartMarker){const t=this.n.buffer.active.getLine(this.r.currentCommand.commandStartMarker.line);t&&(this.r.currentCommand.commandStartLineContent=t.translateToString(!0))}this.s.onCommandStartedEmitter.fire({marker:this.r.currentCommand.commandStartMarker}),this.t.debug("CommandDetectionCapability#_handleCommandStartWindows",this.r.currentCommand.commandStartX,this.r.currentCommand.commandStartMarker?.line)}handleCommandExecuted(t){this.f&&this.w(),this.c.clear(),this.z(),this.r.currentCommand.commandExecutedX=this.n.buffer.active.cursorX,this.s.onCommandExecutedEmitter.fire(this.r.currentCommand),this.t.debug("CommandDetectionCapability#handleCommandExecuted",this.r.currentCommand.commandExecutedX,this.r.currentCommand.commandExecutedMarker?.line)}preHandleCommandFinished(){this.r.currentCommand.commandExecutedMarker||(this.s.commandMarkers.length===0&&(this.r.currentCommand.commandStartMarker||(this.r.currentCommand.commandStartMarker=this.n.registerMarker(0)),this.r.currentCommand.commandStartMarker&&this.s.commandMarkers.push(this.r.currentCommand.commandStartMarker)),this.z())}postHandleCommandFinished(){const t=this.r.currentCommand,s=t.command,i=t.commandStartMarker?.line,r=t.commandExecutedMarker?.line;if(!s||s.length===0||i===void 0||i===-1||r===void 0||r===-1)return;let n=0,o=!1;for(let a=i;a<=r;a++){const l=this.n.buffer.active.getLine(a);if(!l)break;const c=l.translateToString(!0);for(let u=0;u<c.length;u++){for(;s.length<n&&s[n]===" ";)n++;if(c[u]===s[n]&&n++,n===s.length){const h=u>=this.n.cols-1;t.commandExecutedMarker=this.n.registerMarker(a-(this.n.buffer.active.baseY+this.n.buffer.active.cursorY)+(h?1:0)),t.commandExecutedX=h?0:u+1,o=!0;break}}if(o)break}}z(){if(this.s.commandMarkers.length!==0){if(this.s.commandMarkers=this.s.commandMarkers.sort((t,s)=>t.line-s.line),this.r.currentCommand.commandStartMarker=this.s.commandMarkers[0],this.r.currentCommand.commandStartMarker){const t=this.n.buffer.active.getLine(this.r.currentCommand.commandStartMarker.line);t&&(this.r.currentCommand.commandStartLineContent=t.translateToString(!0))}this.r.currentCommand.commandExecutedMarker=this.s.commandMarkers[this.s.commandMarkers.length-1],this.s.onCommandExecutedEmitter.fire(this.r.currentCommand)}}C(){const t=this.r.commands.at(-1);if(!t)return!0;const s=this.n.buffer.active.baseY+this.n.buffer.active.cursorY,i=(t.endMarker?t.endMarker.line:t.marker?.line)??-1;return s>i}D(){const t=this.n.buffer.active.cursorX,s=this.n.buffer.active.cursorY;let i=0;return new Promise((r,n)=>{const o=setInterval(()=>{if(t!==this.n.buffer.active.cursorX||s!==this.n.buffer.active.cursorY){r(),clearInterval(o);return}i+=10,i>1e3&&(clearInterval(o),r())},10)})}F(t=this.n.buffer.active.baseY+this.n.buffer.active.cursorY){const s=this.n.buffer.active.getLine(t);if(!s)return;const i=s.translateToString(!0);if(!i)return;const r=i.match(/(?<prompt>(\(.+\)\s)?(?:PS.+>\s?))/)?.groups?.prompt;if(r){const c=this.G(r,i,">");if(c)return{prompt:c,likelySingleLine:!0}}const n=i.match(/.*\u276f(?=[^\u276f]*$)/g)?.[0];if(n){const c=this.G(n,i,"\u276F");if(c)return c}const o=i.match(/^(?<prompt>\$)/)?.groups?.prompt;if(o){const c=this.G(o,i,"$");if(c)return c}const a=i.match(/^(?<prompt>>>> )/g)?.groups?.prompt;if(a)return{prompt:a,likelySingleLine:!0};if(this.r.promptTerminator&&i.trim().endsWith(this.r.promptTerminator)){const c=this.G(i,i,this.r.promptTerminator);if(c)return c}const l=i.match(/^(?<prompt>(\(.+\)\s)?(?:[A-Z]:\\.*>))/);return l?.groups?.prompt?{prompt:l.groups.prompt,likelySingleLine:!0}:void 0}G(t,s,i){if(t)return s===t&&t.endsWith(i)&&(t+=" "),t}};Vt=__decorate([__param(3,Ye)],Vt);function qt(e,t,s=0){return e.registerMarker(t.line-(e.buffer.active.baseY+e.buffer.active.cursorY)+s)}var Kh=class extends G{constructor(){super(...arguments),this.type=0,this.a="",this.b=new Map,this.c=this.B(new C),this.onDidChangeCwd=this.c.event}get cwds(){return Array.from(this.b.keys())}getCwd(){return this.a}updateCwd(e){const t=this.a!==e;this.a=e;const s=this.b.get(this.a)||0;this.b.delete(this.a),this.b.set(this.a,s+1),t&&this.c.fire(e)}},No;(function(e){e[e.MinimumPromptLength=2]="MinimumPromptLength"})(No||(No={}));var Zh=class extends He{get commands(){return this.a}constructor(e){super(),this.c=e,this.type=3,this.a=[],this.b=this.add(new C),this.onCommandFinished=this.b.event,this.add(this.c.onData(t=>this.h(t))),this.add(this.c.parser.registerCsiHandler({final:"J"},t=>(t.length>=1&&(t[0]===2||t[0]===3)&&this.m(),!1)))}h(e){e==="\r"&&this.j()}j(){if(this.c&&this.c.buffer.active.cursorX>=2){const e=this.c.registerMarker(0);e&&(this.a.push(e),this.b.fire(e))}}m(){let e=0;for(let t=this.a.length-1;t>=0&&!(this.a[t].line<this.c.buffer.active.baseY);t--)e++;this.a.splice(this.a.length-e,e)}},eu=class extends G{constructor(e){super(),this.f=e,this.type=4,this.a=new Map,this.b=new Map,this.c=this.B(new C),this.onMarkAdded=this.c.event}*markers(){for(const e of this.a.values())yield e;for(const e of this.b.values())yield e}addMark(e){const t=e?.marker||this.f.registerMarker(),s=e?.id;t&&(s?(this.a.set(s,t),t.onDispose(()=>this.a.delete(s))):(this.b.set(t.id,t),t.onDispose(()=>this.b.delete(t.id))),this.c.fire({marker:t,id:s,hidden:e?.hidden,hoverMessage:e?.hoverMessage}))}getMark(e){return this.a.get(e)}},tu=class extends G{constructor(){super(...arguments),this.type=5,this.b={value:new Map,isTrusted:!0},this.c=this.B(new C),this.onDidChangeEnv=this.c.event}get env(){return this.g()}setEnvironment(e,t){if(!ys(this.env.value,e)){this.b.value.clear();for(const[s,i]of Object.entries(e))i!==void 0&&this.b.value.set(s,i);this.b.isTrusted=t,this.f()}}startEnvironmentSingleVar(e,t){e?this.a={value:new Map,isTrusted:t}:this.a={value:new Map(this.b.value),isTrusted:this.b.isTrusted&&t}}setEnvironmentSingleVar(e,t,s){this.a&&e!==void 0&&t!==void 0&&(this.a.value.set(e,t),this.a.isTrusted&&=s)}endEnvironmentSingleVar(e){if(!this.a)return;this.a.isTrusted&&=e,!za(this.b.value,this.a.value)&&(this.b=this.a,this.f()),this.a=void 0}deleteEnvironmentSingleVar(e,t,s){this.a&&e!==void 0&&t!==void 0&&(this.a.value.delete(e),this.a.isTrusted&&=s)}f(){this.c.fire(this.g())}g(){return{value:Object.fromEntries(this.b.value),isTrusted:this.b.isTrusted}}},Fo;(function(e){e[e.FinalTerm=133]="FinalTerm",e[e.VSCode=633]="VSCode",e[e.ITerm=1337]="ITerm",e[e.SetCwd=7]="SetCwd",e[e.SetWindowsFriendlyCwd=9]="SetWindowsFriendlyCwd"})(Fo||(Fo={}));var To;(function(e){e.PromptStart="A",e.CommandStart="B",e.CommandExecuted="C",e.CommandFinished="D"})(To||(To={}));var jo;(function(e){e.PromptStart="A",e.CommandStart="B",e.CommandExecuted="C",e.CommandFinished="D",e.CommandLine="E",e.ContinuationStart="F",e.ContinuationEnd="G",e.RightPromptStart="H",e.RightPromptEnd="I",e.Property="P",e.SetMark="SetMark",e.EnvJson="EnvJson",e.EnvSingleDelete="EnvSingleDelete",e.EnvSingleStart="EnvSingleStart",e.EnvSingleEntry="EnvSingleEntry",e.EnvSingleEnd="EnvSingleEnd"})(jo||(jo={}));var Uo;(function(e){e.SetMark="SetMark",e.CurrentDir="CurrentDir"})(Uo||(Uo={}));var su=class extends G{get seenSequences(){return this.g}get status(){return this.h}constructor(e,t,s,i){super(),this.n=e,this.r=t,this.s=s,this.t=i,this.capabilities=this.B(new Yh),this.b=!1,this.f=[],this.g=new Set,this.h=0,this.j=new C,this.onDidChangeStatus=this.j.event,this.m=new C,this.onDidChangeSeenSequences=this.m.event,this.B(be(()=>{this.F(),this.u()}))}u(){Ne(this.f),this.f.length=0}activate(e){this.a=e,this.capabilities.add(3,this.B(new Zh(this.a))),this.B(e.parser.registerOscHandler(633,t=>this.C(t))),this.B(e.parser.registerOscHandler(1337,t=>this.L(t))),this.f.push(e.parser.registerOscHandler(133,t=>this.y(t))),this.B(e.parser.registerOscHandler(7,t=>this.N(t))),this.B(e.parser.registerOscHandler(9,t=>this.M(t))),this.D()}getMarkerId(e,t){this.Q(e).getMark(t)}w(e){this.g.has(e)||(this.g.add(e),this.m.fire(this.g))}y(e){const t=this.z(e);return this.h===0&&(this.h=1,this.j.fire(this.h)),t}z(e){if(!this.a)return!1;const[t,...s]=e.split(";");switch(this.w(t),t){case"A":return this.P(this.a).handlePromptStart(),!0;case"B":return this.P(this.a).handleCommandStart({ignoreCommandLine:!0}),!0;case"C":return this.P(this.a).handleCommandExecuted(),!0;case"D":{const i=s.length===1?parseInt(s[0]):void 0;return this.P(this.a).handleCommandFinished(i),!0}}return!1}C(e){const t=this.G(e);return!this.b&&t&&(this.s?.publicLog2("terminal/shellIntegrationActivationSucceeded"),this.b=!0,this.F()),this.h!==2&&(this.h=2,this.j.fire(this.h)),t}async D(){!this.s||this.r||(this.c=setTimeout(()=>{!this.capabilities.get(2)&&!this.capabilities.get(0)&&(this.s?.publicLog2("terminal/shellIntegrationActivationTimeout"),this.t.warn("Shell integration failed to add capabilities within 10 seconds")),this.b=!0},1e4))}F(){this.c!==void 0&&(clearTimeout(this.c),this.c=void 0)}G(e){if(!this.a)return!1;const t=e.indexOf(";"),s=t===-1?e:e.substring(0,t);this.w(s);const i=t===-1?[]:e.substring(t+1).split(";");switch(s){case"A":return this.P(this.a).handlePromptStart(),!0;case"B":return this.P(this.a).handleCommandStart(),!0;case"C":return this.P(this.a).handleCommandExecuted(),!0;case"D":{const r=i[0],n=r!==void 0?parseInt(r):void 0;return this.P(this.a).handleCommandFinished(n),!0}case"E":{const r=i[0],n=i[1];let o;return r!==void 0?o=Gt(r):o="",this.P(this.a).setCommandLine(o,n===this.n),!0}case"F":return this.P(this.a).handleContinuationStart(),!0;case"G":return this.P(this.a).handleContinuationEnd(),!0;case"EnvJson":{const r=i[0],n=i[1];if(r!==void 0)try{const o=JSON.parse(Gt(r));this.R().setEnvironment(o,n===this.n)}catch{this.t.warn("Failed to parse environment from shell integration sequence",r)}return!0}case"EnvSingleStart":return this.R().startEnvironmentSingleVar(i[0]==="1",i[1]===this.n),!0;case"EnvSingleDelete":{const r=i[0],n=i[1],o=i[2];if(r!==void 0&&n!==void 0){const a=Gt(n);this.R().deleteEnvironmentSingleVar(r,a,o===this.n)}return!0}case"EnvSingleEntry":{const r=i[0],n=i[1],o=i[2];if(r!==void 0&&n!==void 0){const a=Gt(n);this.R().setEnvironmentSingleVar(r,a,o===this.n)}return!0}case"EnvSingleEnd":return this.R().endEnvironmentSingleVar(i[0]===this.n),!0;case"H":return this.P(this.a).handleRightPromptStart(),!0;case"I":return this.P(this.a).handleRightPromptEnd(),!0;case"P":{const r=i[0],n=r!==void 0?Gt(r):"",{key:o,value:a}=Bo(n);if(a===void 0)return!0;switch(o){case"ContinuationPrompt":return this.H(yc(a)),!0;case"Cwd":return this.J(a),!0;case"IsWindows":return this.P(this.a).setIsWindowsPty(a==="True"),!0;case"HasRichCommandDetection":return this.P(this.a).setHasRichCommandDetection(a==="True"),!0;case"Prompt":{const l=a.replace(/\x1b\[[0-9;]*m/g,"");return this.I(l),!0}case"PromptType":return this.P(this.a).setPromptType(a),!0;case"Task":return this.Q(this.a),this.capabilities.get(2)?.setIsCommandStorageDisabled(),!0}}case"SetMark":return this.Q(this.a).addMark(iu(i)),!0}return!1}H(e){this.a&&this.P(this.a).setContinuationPrompt(e)}I(e){if(!this.a)return;const t=e.substring(e.lastIndexOf(`
`)+1),s=t.substring(t.lastIndexOf(" "));s&&this.P(this.a).setPromptTerminator(s,t)}J(e){e=Mh(e),this.O().updateCwd(e),this.capabilities.get(2)?.setCwd(e)}L(e){if(!this.a)return!1;const[t]=e.split(";");switch(this.w(`1337;${t}`),t){case"SetMark":this.Q(this.a).addMark();default:{const{key:s,value:i}=Bo(t);if(i===void 0)return!0;switch(s){case"CurrentDir":return this.J(i),!0}}}return!1}M(e){if(!this.a)return!1;const[t,...s]=e.split(";");switch(this.w(`9;${t}`),t){case"9":return s.length&&this.J(s[0]),!0}return!1}N(e){if(!this.a)return!1;const[t]=e.split(";");if(this.w(`7;${t}`),t.match(/^file:\/\/.*\//)){const s=j.parse(t);if(s.path&&s.path.length>0)return this.J(s.path),!0}return!1}serialize(){return!this.a||!this.capabilities.has(2)?{isWindowsPty:!1,hasRichCommandDetection:!1,commands:[],promptInputModel:void 0}:this.P(this.a).serialize()}deserialize(e){if(!this.a)throw new Error("Cannot restore commands before addon is activated");const t=this.P(this.a);t.deserialize(e),t.cwd&&this.J(t.cwd)}O(){let e=this.capabilities.get(0);return e||(e=this.B(new Kh),this.capabilities.add(0,e)),e}P(e){let t=this.capabilities.get(2);return t||(t=this.B(new Is(e,this.t)),this.capabilities.add(2,t)),t}Q(e){let t=this.capabilities.get(4);return t||(t=this.B(new eu(e)),this.capabilities.add(4,t)),t}R(){let e=this.capabilities.get(5);return e||(e=this.B(new tu),this.capabilities.add(5,e)),e}};function Gt(e){return e.replaceAll(/\\(\\|x([0-9a-f]{2}))/gi,(t,s,i)=>i?String.fromCharCode(parseInt(i,16)):s)}function Bo(e){const t=e.indexOf("=");return t===-1?{key:e,value:void 0}:{key:e.substring(0,t),value:e.substring(1+t)}}function iu(e){let t,s=!1;for(const i of e)i!==void 0&&(i==="Hidden"&&(s=!0),i.startsWith("Id=")&&(t=i.substring(3)));return{id:t,hidden:s}}function ru(e,t={}){let s="";return t.excludeLeadingNewLine||(s+=`\r
`),s+="\x1B[0m\x1B[7m * ",t.loudFormatting?s+="\x1B[0;104m":s+="\x1B[0m",s+=` ${e} \x1B[0m
\r`,s}import{join as nu}from"path";function Ri(e){const t=[];typeof e=="number"&&t.push("code/timeOrigin",e);function s(r,n){t.push(r,n?.startTime??Date.now())}function i(){const r=[];for(let n=0;n<t.length;n+=2)r.push({name:t[n],startTime:t[n+1]});return r}return{mark:s,getMarks:i}}function ou(){if(typeof performance=="object"&&typeof performance.mark=="function"&&!performance.nodeTiming)return typeof performance.timeOrigin!="number"&&!performance.timing?Ri():{mark(e,t){performance.mark(e,t)},getMarks(){let e=performance.timeOrigin;typeof e!="number"&&(e=performance.timing.navigationStart||performance.timing.redirectStart||performance.timing.fetchStart);const t=[{name:"code/timeOrigin",startTime:Math.round(e)}];for(const s of performance.getEntriesByType("mark"))t.push({name:s.name,startTime:Math.round(e+s.startTime)});return t}};if(typeof process=="object"){const e=performance?.timeOrigin;return Ri(e)}else return console.trace("perf-util loaded in UNKNOWN environment"),Ri()}function au(e){return e.MonacoPerformanceMarks||(e.MonacoPerformanceMarks=ou()),e.MonacoPerformanceMarks}var zo=au(globalThis),Xt=zo.mark,cu=zo.getMarks;import lu from"@xterm/headless";var hu=class extends G{constructor(e,t,s,i){super(),this.a=0,this.b=!1,this.c=!1,this.B(e.onProcessData(r=>{if(this.b||this.c)return;const n=typeof r=="string"?r:r.data;for(let o=0;o<n.length;o++)n[o]===t[this.a]?this.a++:this.f(),this.a===t.length&&(i.debug(`Auto reply match: "${t}", response: "${s}"`),e.input(s),this.c=!0,Ue(1e3).then(()=>this.c=!1),this.f())}))}f(){this.a=0}handleResize(){N&&(this.b=!0)}handleInput(){this.b=!1}},Ni=class{constructor(t){this.d=t,this.a=new Map,this.b=new Map,this.c=new Map}async installAutoReply(t,s){this.a.set(t,s);for(const i of this.c.keys()){const r=this.b.get(i);if(!r){this.d.error("Could not find terminal process to install auto reply");continue}this.f(i,r,t,s)}}async uninstallAllAutoReplies(){for(const t of this.a.keys())for(const s of this.c.values())s.get(t)?.dispose(),s.delete(t)}handleProcessReady(t,s){this.b.set(t,s),this.c.set(t,new Map);for(const[i,r]of this.a.entries())this.f(t,s,i,r)}handleProcessDispose(t){const s=this.c.get(t);if(s){for(const i of s.values())i.dispose();s.clear()}}handleProcessInput(t,s){const i=this.c.get(t);if(i)for(const r of i.values())r.handleInput()}handleProcessResize(t,s,i){const r=this.c.get(t);if(r)for(const n of r.values())n.handleResize()}f(t,s,i,r){const n=this.c.get(t);n&&(n.get(i)?.dispose(),n.set(i,new hu(s,i,r,this.d)))}};Ni=__decorate([__param(0,Ye)],Ni);var{Terminal:uu}=lu;function _(e,t,s){if(typeof s.value!="function")throw new Error("not supported");const i="value",r=s.value;s[i]=async function(...n){this.traceRpcArgs.logService.getLevel()===A.Trace&&this.traceRpcArgs.logService.trace(`[RPC Request] PtyService#${r.name}(${n.map(a=>JSON.stringify(a)).join(", ")})`),this.traceRpcArgs.simulatedLatency&&await Ue(this.traceRpcArgs.simulatedLatency);let o;try{o=await r.apply(this,n)}catch(a){throw this.traceRpcArgs.logService.error(`[RPC Response] PtyService#${r.name}`,a),a}return this.traceRpcArgs.logService.getLevel()===A.Trace&&this.traceRpcArgs.logService.trace(`[RPC Response] PtyService#${r.name}`,o),o}}var Fi,Ti,I=class extends G{async installAutoReply(e,t){await this.j.installAutoReply(e,t)}async uninstallAllAutoReplies(){await this.j.uninstallAllAutoReplies()}I(e,t){return t(s=>{this.J.getLevel()===A.Trace&&this.J.trace(`[RPC Event] PtyService#${e}.fire(${JSON.stringify(s)})`)}),t}get traceRpcArgs(){return{logService:this.J,simulatedLatency:this.N}}constructor(e,t,s,i){super(),this.J=e,this.L=t,this.M=s,this.N=i,this.a=new Map,this.b=new Map,this.g=new Map,this.u=0,this.w=this.B(new C),this.onHeartbeat=this.I("_onHeartbeat",this.w.event),this.y=this.B(new C),this.onProcessData=this.I("_onProcessData",this.y.event),this.z=this.B(new C),this.onProcessReplay=this.I("_onProcessReplay",this.z.event),this.C=this.B(new C),this.onProcessReady=this.I("_onProcessReady",this.C.event),this.D=this.B(new C),this.onProcessExit=this.I("_onProcessExit",this.D.event),this.F=this.B(new C),this.onProcessOrphanQuestion=this.I("_onProcessOrphanQuestion",this.F.event),this.G=this.B(new C),this.onDidRequestDetach=this.I("_onDidRequestDetach",this.G.event),this.H=this.B(new C),this.onDidChangeProperty=this.I("_onDidChangeProperty",this.H.event),this.B(be(()=>{for(const r of this.a.values())r.shutdown(!0);this.a.clear()})),this.f=this.B(new $i(void 0,this.J)),this.f.onCreateRequest(this.G.fire,this.G),this.j=new Ni(this.J),this.n=[this.j]}async refreshIgnoreProcessNames(e){Mi.length=0,Mi.push(...e)}async requestDetachInstance(e,t){return this.f.createRequest({workspaceId:e,instanceId:t})}async acceptDetachInstanceReply(e,t){let s;const i=this.a.get(t);i&&(s=await this.U(t,i)),this.f.acceptReply(e,s)}async freePortKillProcess(e){const s=(await new Promise((i,r)=>{mh(N?`netstat -ano | findstr "${e}"`:`lsof -nP -iTCP -sTCP:LISTEN | grep ${e}`,{},(n,o)=>{if(n)return r("Problem occurred when listing active processes");i(o)})})).split(/\r?\n/).filter(i=>!!i.trim());if(s.length>=1){const i=/\s+(\d+)(?:\s+|$)/,r=s[0].match(i)?.[1];if(r)try{process.kill(Number.parseInt(r))}catch{}else throw new Error(`Processes for port ${e} were not found`);return{port:e,processId:r}}throw new Error(`Could not kill process with port ${e}`)}async serializeTerminalState(e){const t=[];for(const[i,r]of this.a.entries())r.hasWrittenData&&e.indexOf(i)!==-1&&t.push(ci.withAsyncBody(async n=>{n({id:i,shellLaunchConfig:r.shellLaunchConfig,processDetails:await this.U(i,r),processLaunchConfig:r.processLaunchOptions,unicodeVersion:r.unicodeVersion,replayEvent:await r.serializeNormalBuffer(),timestamp:Date.now()})}));const s={version:1,state:await Promise.all(t)};return JSON.stringify(s)}async reviveTerminalProcesses(e,t,s){const i=[];for(const r of t)i.push(this.O(e,r));await Promise.all(i)}async O(e,t){const s=w(2285,null);let i="";if(N){const o=t.replayEvent.events.length>0?t.replayEvent.events.at(-1):void 0;o&&(i+=`\r
`.repeat(o.rows-1)+"\x1B[H")}const r=await this.createProcess({...t.shellLaunchConfig,cwd:t.processDetails.cwd,color:t.processDetails.color,icon:t.processDetails.icon,name:t.processDetails.titleSource===mt.Api?t.processDetails.title:void 0,initialText:t.replayEvent.events[0].data+ru(s,{loudFormatting:!0})+i},t.processDetails.cwd,t.replayEvent.events[0].cols,t.replayEvent.events[0].rows,t.unicodeVersion,t.processLaunchConfig.env,t.processLaunchConfig.executableEnv,t.processLaunchConfig.options,!0,t.processDetails.workspaceId,t.processDetails.workspaceName,!0,t.replayEvent.events[0].data),n=this.S(e,t.id);this.g.set(n,{newId:r,state:t}),this.J.info(`Revived process, old id ${n} -> new id ${r}`)}async shutdownAll(){this.dispose()}async createProcess(e,t,s,i,r,n,o,a,l,c,u,h,f){if(e.attachPersistentProcess)throw new Error("Attempt to create a process when attach object was provided");const d=++this.u,p=new Oi(e,t,s,i,n,o,a,this.J,this.L),v={env:n,executableEnv:o,options:a},g=new fu(d,p,c,u,l,s,i,v,r,this.M,this.J,h&&typeof e.initialText=="string"?e.initialText:void 0,f,e.icon,e.color,e.name,e.fixedDimensions);return p.onProcessExit(b=>{for(const $ of this.n)$.handleProcessDispose(d);g.dispose(),this.a.delete(d),this.D.fire({id:d,event:b})}),g.onProcessData(b=>this.y.fire({id:d,event:b})),g.onProcessReplay(b=>this.z.fire({id:d,event:b})),g.onProcessReady(b=>this.C.fire({id:d,event:b})),g.onProcessOrphanQuestion(()=>this.F.fire({id:d})),g.onDidChangeProperty(b=>this.H.fire({id:d,property:b})),g.onPersistentProcessReady(()=>{for(const b of this.n)b.handleProcessReady(d,p)}),this.a.set(d,g),d}async attachToProcess(e){try{await this.W(e).attach(),this.J.info(`Persistent process reconnection "${e}"`)}catch(t){throw this.J.warn(`Persistent process reconnection "${e}" failed`,t.message),t}}async updateTitle(e,t,s){this.W(e).setTitle(t,s)}async updateIcon(e,t,s,i){this.W(e).setIcon(t,s,i)}async clearBuffer(e){this.W(e).clearBuffer()}async refreshProperty(e,t){return this.W(e).refreshProperty(t)}async updateProperty(e,t,s){return this.W(e).updateProperty(t,s)}async detachFromProcess(e,t){return this.W(e).detach(t)}async reduceConnectionGraceTime(){for(const e of this.a.values())e.reduceGraceTime()}async listProcesses(){const e=Array.from(this.a.entries()).filter(([i,r])=>r.shouldPersistTerminal);this.J.info(`Listing ${e.length} persistent terminals, ${this.a.size} total terminals`);const t=e.map(async([i,r])=>this.U(i,r));return(await Promise.all(t)).filter(i=>i.isOrphan)}async getPerformanceMarks(){return cu()}async start(e){const t=this.a.get(e);return t?t.start():{message:`Could not find pty with id "${e}"`}}async shutdown(e,t){return this.a.get(e)?.shutdown(t)}async input(e,t){const s=this.W(e);if(s){for(const i of this.n)i.handleProcessInput(e,t);s.input(t)}}async processBinary(e,t){return this.W(e).writeBinary(t)}async resize(e,t,s){const i=this.W(e);if(i){for(const r of this.n)r.handleProcessResize(e,t,s);i.resize(t,s)}}async getInitialCwd(e){return this.W(e).getInitialCwd()}async getCwd(e){return this.W(e).getCwd()}async acknowledgeDataEvent(e,t){return this.W(e).acknowledgeDataEvent(t)}async setUnicodeVersion(e,t){return this.W(e).setUnicodeVersion(t)}async getLatency(){return[]}async orphanQuestionReply(e){return this.W(e).orphanQuestionReply()}async getDefaultSystemShell(e=fr){return Ph(e,process.env)}async getEnvironment(){return{...process.env}}async getWslPath(e,t){if(t==="win-to-unix"){if(!N)return e;if(Ze()<17063)return e.replace(/\\/g,"/");const s=this.P();return s?new Promise(i=>{ao(s,["-e","wslpath",e],{},(n,o,a)=>{i(n?e:$h(o.trim()))}).stdin.end()}):e}if(t==="unix-to-win"&&N){if(Ze()<17063)return e;const s=this.P();return s?new Promise(i=>{ao(s,["-e","wslpath","-w",e],{},(n,o,a)=>{i(n?e:o.trim())}).stdin.end()}):e}return e}P(){const e=Ze()>=16299,t=process.env.hasOwnProperty("PROCESSOR_ARCHITEW6432"),s=process.env.SystemRoot;if(s)return nu(s,t?"Sysnative":"System32",e?"wsl.exe":"bash.exe")}async getRevivedPtyNewId(e,t){try{return this.g.get(this.S(e,t))?.newId}catch(s){this.J.warn(`Couldn't find terminal ID ${e}-${t}`,s.message)}}async setTerminalLayoutInfo(e){this.b.set(e.workspaceId,e)}async getTerminalLayoutInfo(e){Xt("code/willGetTerminalLayoutInfo");const t=this.b.get(e.workspaceId);if(t){const s=new Set,r=(await Promise.all(t.tabs.map(async n=>this.Q(e.workspaceId,n,s)))).filter(n=>n.terminals.length>0);return Xt("code/didGetTerminalLayoutInfo"),{tabs:r}}Xt("code/didGetTerminalLayoutInfo")}async Q(e,t,s){const r=(await Promise.all(t.terminals.map(n=>this.R(e,n,s)))).filter(n=>n.terminal!==null);return{isActive:t.isActive,activePersistentProcessId:t.activePersistentProcessId,terminals:r}}async R(e,t,s){try{const i=this.S(e,t.terminal),r=this.g.get(i)?.newId;this.J.info(`Expanding terminal instance, old id ${i} -> new id ${r}`),this.g.delete(i);const n=r??t.terminal;if(s.has(n))throw new Error(`Terminal ${n} has already been expanded`);s.add(n);const o=this.W(n);return{terminal:{...o&&await this.U(t.terminal,o,r!==void 0),id:n},relativeSize:t.relativeSize}}catch(i){return this.J.warn("Couldn't get layout info, a terminal was probably disconnected",i.message),this.J.debug("Reattach to wrong terminal debug info - layout info by id",t),this.J.debug("Reattach to wrong terminal debug info - _revivePtyIdMap",Array.from(this.g.values())),this.J.debug("Reattach to wrong terminal debug info - _ptys ids",Array.from(this.a.keys())),{terminal:null,relativeSize:t.relativeSize}}}S(e,t){return`${e}-${t}`}async U(e,t,s=!1){Xt(`code/willBuildProcessDetails/${e}`);const[i,r]=await Promise.all([t.getCwd(),s?!0:t.isOrphaned()]),n={id:e,title:t.title,titleSource:t.titleSource,pid:t.pid,workspaceId:t.workspaceId,workspaceName:t.workspaceName,cwd:i,isOrphan:r,icon:t.icon,color:t.color,fixedDimensions:t.fixedDimensions,environmentVariableCollections:t.processLaunchOptions.options.environmentVariableCollections,reconnectionProperties:t.shellLaunchConfig.reconnectionProperties,waitOnExit:t.shellLaunchConfig.waitOnExit,hideFromUser:t.shellLaunchConfig.hideFromUser,isFeatureTerminal:t.shellLaunchConfig.isFeatureTerminal,type:t.shellLaunchConfig.type,hasChildProcesses:t.hasChildProcesses,shellIntegrationNonce:t.processLaunchOptions.options.shellIntegration.nonce,tabActions:t.shellLaunchConfig.tabActions};return Xt(`code/didBuildProcessDetails/${e}`),n}W(e){const t=this.a.get(e);if(!t)throw new rt(`Could not find pty ${e} on pty host`);return t}};__decorate([_],I.prototype,"installAutoReply",null),__decorate([_],I.prototype,"uninstallAllAutoReplies",null),__decorate([U],I.prototype,"traceRpcArgs",null),__decorate([_],I.prototype,"refreshIgnoreProcessNames",null),__decorate([_],I.prototype,"requestDetachInstance",null),__decorate([_],I.prototype,"acceptDetachInstanceReply",null),__decorate([_],I.prototype,"freePortKillProcess",null),__decorate([_],I.prototype,"serializeTerminalState",null),__decorate([_],I.prototype,"reviveTerminalProcesses",null),__decorate([_],I.prototype,"shutdownAll",null),__decorate([_],I.prototype,"createProcess",null),__decorate([_],I.prototype,"attachToProcess",null),__decorate([_],I.prototype,"updateTitle",null),__decorate([_],I.prototype,"updateIcon",null),__decorate([_],I.prototype,"clearBuffer",null),__decorate([_],I.prototype,"refreshProperty",null),__decorate([_],I.prototype,"updateProperty",null),__decorate([_],I.prototype,"detachFromProcess",null),__decorate([_],I.prototype,"reduceConnectionGraceTime",null),__decorate([_],I.prototype,"listProcesses",null),__decorate([_],I.prototype,"getPerformanceMarks",null),__decorate([_],I.prototype,"start",null),__decorate([_],I.prototype,"shutdown",null),__decorate([_],I.prototype,"input",null),__decorate([_],I.prototype,"processBinary",null),__decorate([_],I.prototype,"resize",null),__decorate([_],I.prototype,"getInitialCwd",null),__decorate([_],I.prototype,"getCwd",null),__decorate([_],I.prototype,"acknowledgeDataEvent",null),__decorate([_],I.prototype,"setUnicodeVersion",null),__decorate([_],I.prototype,"getLatency",null),__decorate([_],I.prototype,"orphanQuestionReply",null),__decorate([_],I.prototype,"getDefaultSystemShell",null),__decorate([_],I.prototype,"getEnvironment",null),__decorate([_],I.prototype,"getWslPath",null),__decorate([_],I.prototype,"getRevivedPtyNewId",null),__decorate([_],I.prototype,"setTerminalLayoutInfo",null),__decorate([_],I.prototype,"getTerminalLayoutInfo",null);var Wo;(function(e){e.None="None",e.ReplayOnly="ReplayOnly",e.Session="Session"})(Wo||(Wo={}));var fu=class extends G{get pid(){return this.J}get shellLaunchConfig(){return this.U.shellLaunchConfig}get hasWrittenData(){return this.g.value!=="None"}get title(){return this.M||this.U.currentTitle}get titleSource(){return this.N}get icon(){return this.X}get color(){return this.Y}get fixedDimensions(){return this.Q}get hasChildProcesses(){return this.U.hasChildProcesses}setTitle(e,t){t===mt.Api&&(this.g.setValue("Session","setTitle"),this.O.freeRawReviveBuffer()),this.M=e,this.N=t}setIcon(e,t,s){(!this.X||"id"in t&&"id"in this.X&&t.id!==this.X.id||!this.color||s!==this.Y)&&(this.O.freeRawReviveBuffer(),e&&this.g.setValue("Session","setIcon")),this.X=t,this.Y=s}R(e){this.Q=e}constructor(e,t,s,i,r,n,o,a,l,c,u,h,f,d,p,v,g){super(),this.S=e,this.U=t,this.workspaceId=s,this.workspaceName=i,this.shouldPersistTerminal=r,this.processLaunchOptions=a,this.unicodeVersion=l,this.W=u,this.X=d,this.Y=p,this.b=new Map,this.f=!1,this.u=new Ur,this.z=this.B(new C),this.onProcessReplay=this.z.event,this.C=this.B(new C),this.onProcessReady=this.C.event,this.D=this.B(new C),this.onPersistentProcessReady=this.D.event,this.F=this.B(new C),this.onProcessData=this.F.event,this.G=this.B(new C),this.onProcessOrphanQuestion=this.G.event,this.H=this.B(new C),this.onDidChangeProperty=this.H.event,this.I=!1,this.J=-1,this.L="",this.N=mt.Process,this.g=new du(`Persistent process "${this.S}" interaction state`,"None",this.W),this.P=h!==void 0,this.O=new pu(n,o,c.scrollback,l,h,a.options.shellIntegration.nonce,r?f:void 0,this.W),v&&this.setTitle(v,mt.Api),this.Q=g,this.j=null,this.n=0,this.w=this.B(new Br(()=>{this.W.info(`Persistent process "${this.S}": The reconnection grace time of ${Ho(c.graceTime)} has expired, shutting down pid "${this.J}"`),this.shutdown(!0)},c.graceTime)),this.y=this.B(new Br(()=>{this.W.info(`Persistent process "${this.S}": The short reconnection grace time of ${Ho(c.shortGraceTime)} has expired, shutting down pid ${this.J}`),this.shutdown(!0)},c.shortGraceTime)),this.B(this.U.onProcessExit(()=>this.a.stopBuffering(this.S))),this.B(this.U.onProcessReady(b=>{this.J=b.pid,this.L=b.cwd,this.C.fire(b)})),this.B(this.U.onDidChangeProperty(b=>{this.H.fire(b)})),this.a=new Lh((b,$)=>this.F.fire($)),this.B(this.a.startBuffering(this.S,this.U.onProcessData)),this.B(this.onProcessData(b=>this.O.handleData(b)))}async attach(){!this.w.isScheduled()&&!this.y.isScheduled()&&this.W.warn(`Persistent process "${this.S}": Process had no disconnect runners but was an orphan`),this.w.cancel(),this.y.cancel()}async detach(e){this.shouldPersistTerminal&&(this.g.value!=="None"||e)?this.w.schedule():this.shutdown(!0)}serializeNormalBuffer(){return this.O.generateReplayEvent(!0,this.g.value!=="Session")}async refreshProperty(e){return this.U.refreshProperty(e)}async updateProperty(e,t){if(e==="fixedDimensions")return this.R(t)}async start(){if(!this.f){const e=await this.U.start();return e&&"message"in e||(this.f=!0,this.P?this.triggerReplay():this.D.fire()),e}this.C.fire({pid:this.J,cwd:this.L,windowsPty:this.U.getWindowsPty()}),this.H.fire({type:"title",value:this.U.currentTitle}),this.H.fire({type:"shellType",value:this.U.shellType}),this.triggerReplay()}shutdown(e){return this.U.shutdown(e)}input(e){if(this.g.setValue("Session","input"),this.O.freeRawReviveBuffer(),!this.I)return this.U.input(e)}writeBinary(e){return this.U.processBinary(e)}resize(e,t){if(!this.I)return this.O.handleResize(e,t),this.a.flushBuffer(this.S),this.U.resize(e,t)}async clearBuffer(){this.O.clearBuffer(),this.U.clearBuffer()}setUnicodeVersion(e){this.unicodeVersion=e,this.O.setUnicodeVersion?.(e)}acknowledgeDataEvent(e){if(!this.I)return this.U.acknowledgeDataEvent(e)}getInitialCwd(){return this.U.getInitialCwd()}getCwd(){return this.U.getCwd()}async triggerReplay(){this.g.value==="None"&&this.g.setValue("ReplayOnly","triggerReplay");const e=await this.O.generateReplayEvent();let t=0;for(const s of e.events)t+=s.data.length;this.W.info(`Persistent process "${this.S}": Replaying ${t} chars and ${e.events.length} size events`),this.z.fire(e),this.U.clearUnacknowledgedChars(),this.D.fire()}sendCommandResult(e,t,s){this.b.get(e)&&this.b.delete(e)}orphanQuestionReply(){if(this.n=Date.now(),this.j){const e=this.j;this.j=null,e.open()}}reduceGraceTime(){this.y.isScheduled()||this.w.isScheduled()&&this.y.schedule()}async isOrphaned(){return await this.u.queue(async()=>this.Z())}async Z(){return this.w.isScheduled()||this.y.isScheduled()?!0:(this.j||(this.j=new Nc(4e3),this.n=0,this.G.fire()),await this.j.wait(),Date.now()-this.n>500)}},du=class{get value(){return this.b}setValue(e,t){this.b!==e&&(this.b=e,this.f(t))}constructor(e,t,s){this.a=e,this.b=t,this.d=s,this.f("initialized")}f(e){this.d.debug(`MutationLogger "${this.a}" set to "${this.b}", reason: ${e}`)}},pu=class{constructor(e,t,s,i,r,n,o,a){this.f=o,this.a=new uu({cols:e,rows:t,scrollback:s,allowProposedApi:!0}),r&&this.a.writeln(r),this.setUnicodeVersion(i),this.b=new su(n,!0,void 0,a),this.a.loadAddon(this.b)}freeRawReviveBuffer(){this.f=void 0}handleData(e){this.a.write(e)}handleResize(e,t){this.a.resize(e,t)}clearBuffer(){this.a.clear()}async generateReplayEvent(e,t){const s=new(await this._getSerializeConstructor());this.a.loadAddon(s);const i={scrollback:this.a.options.scrollback};e&&(i.excludeAltBuffer=!0,i.excludeModes=!0);let r;return t&&this.f?r=this.f:r=s.serialize(i),{events:[{cols:this.a.cols,rows:this.a.rows,data:r}],commands:this.b.serialize()}}async setUnicodeVersion(e){this.a.unicode.activeVersion!==e&&(e==="11"?(this.d=new(await this._getUnicode11Constructor()),this.a.loadAddon(this.d)):(this.d?.dispose(),this.d=void 0),this.a.unicode.activeVersion=e)}async _getUnicode11Constructor(){return Ti||(Ti=(await import("@xterm/addon-unicode11")).Unicode11Addon),Ti}async _getSerializeConstructor(){return Fi||(Fi=(await import("@xterm/addon-serialize")).SerializeAddon),Fi}};function Ho(e){let t=0,s=0,i=0;e>=1e3&&(i=Math.floor(e/1e3),e-=i*1e3),i>=60&&(s=Math.floor(i/60),i-=s*60),s>=60&&(t=Math.floor(s/60),s-=t*60);const r=t?`${t}h`:"",n=s?`${s}m`:"",o=i?`${i}s`:"",a=e?`${e}ms`:"";return`${r}${n}${o}${a}`}mu();async function mu(){const e=parseInt(process.env.VSCODE_STARTUP_DELAY??"0"),t=parseInt(process.env.VSCODE_LATENCY??"0"),s={graceTime:parseInt(process.env.VSCODE_RECONNECT_GRACE_TIME||"0"),shortGraceTime:parseInt(process.env.VSCODE_RECONNECT_SHORT_GRACE_TIME||"0"),scrollback:parseInt(process.env.VSCODE_RECONNECT_SCROLLBACK||"100")};delete process.env.VSCODE_RECONNECT_GRACE_TIME,delete process.env.VSCODE_RECONNECT_SHORT_GRACE_TIME,delete process.env.VSCODE_RECONNECT_SCROLLBACK,delete process.env.VSCODE_LATENCY,delete process.env.VSCODE_STARTUP_DELAY,e&&await Ue(e);const i=un(process);let r;i?r=new dl:r=new ul(Ke.PtyHost);const n={_serviceBrand:void 0,...lh},o=new Al(fn(process.argv,ml),n),a=new ch(Yl(o),o.logsHome);r.registerChannel(Ke.Logger,new Zl(a,()=>V1));const l=a.createLogger("ptyhost",{name:w(2284,null)}),c=new eh(l);e&&c.warn(`Pty Host startup is delayed ${e}ms`),t&&c.warn(`Pty host is simulating ${t}ms latency`);const u=new He,h=new ph;r.registerChannel(Ke.Heartbeat,bs.fromService(h,u));const f=new I(c,n,s,t),d=bs.fromService(f,u);r.registerChannel(Ke.PtyHost,d),i&&r.registerChannel(Ke.PtyHostWindow,d),process.once("exit",()=>{c.trace("Pty host exiting"),c.dispose(),h.dispose(),f.dispose()})}

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/848b80aeb52026648a8ff9f7c45a9b0a80641e2e/core/vs/platform/terminal/node/ptyHostMain.js.map
